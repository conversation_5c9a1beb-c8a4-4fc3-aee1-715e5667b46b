-- ========================================
-- VÉRIFICATION DE LA BASE DE DONNÉES
-- ========================================

-- 1. Vérifier la structure de la table evenement
DESCRIBE evenement;

-- 2. Vérifier les colonnes existantes
SHOW COLUMNS FROM evenement;

-- 3. Vérifier si la colonne type_de_degat existe
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'evenement' 
AND TABLE_SCHEMA = DATABASE();

-- 4. Voir les données actuelles dans la table evenement
SELECT id, code, libelle, type_de_degat, tier, responsabilite 
FROM evenement 
LIMIT 10;

-- 5. Compter les enregistrements avec type_de_degat NULL vs NOT NULL
SELECT 
    COUNT(*) as total_records,
    COUNT(type_de_degat) as records_with_type_degat,
    COUNT(*) - COUNT(type_de_degat) as records_with_null_type_degat
FROM evenement;

-- 6. Si la colonne n'existe pas, l'ajouter
-- ALTER TABLE evenement ADD COLUMN type_de_degat VARCHAR(50);
-- ALTER TABLE evenement ADD COLUMN tier BOOLEAN DEFAULT FALSE;
-- ALTER TABLE evenement ADD COLUMN responsabilite INT;

-- 7. Test d'insertion manuelle pour vérifier
-- INSERT INTO evenement (code, libelle, type_de_degat, tier, responsabilite) 
-- VALUES ('TEST_DB', 'Test Database', 'MATERIEL', TRUE, 2);

-- 8. Vérifier le test d'insertion
-- SELECT * FROM evenement WHERE code = 'TEST_DB';

-- ========================================
-- INSTRUCTIONS
-- ========================================
-- 1. Exécutez les requêtes 1-5 pour diagnostiquer
-- 2. Si la colonne n'existe pas, décommentez et exécutez la requête 6
-- 3. Testez l'insertion avec les requêtes 7-8
-- ========================================
