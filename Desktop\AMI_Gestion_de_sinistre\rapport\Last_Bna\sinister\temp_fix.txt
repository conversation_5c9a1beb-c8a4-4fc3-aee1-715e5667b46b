  proceedWithContractValidation(contrat: Contrat, dateOfOccurrence: string, dateCreation: string): void {
    // Convert dates for comparison
    const survenance = new Date(dateOfOccurrence);
    const creation = new Date(dateCreation);

    if (survenance > creation) {
      this.dateValidationMessage = 'Erreur: La date de survenance doit être inférieure ou égale à la date de déclaration.';
      this.showDateValidationError = true;
      return;
    }

    // Calculate delay between date_creation and date_survenance
    const delaiJours = Math.floor((creation.getTime() - survenance.getTime()) / (1000 * 60 * 60 * 24));

    console.log(`📊 Calculated delay: ${delaiJours} days`);
    console.log(`📅 Date creation: ${dateCreation}`);
    console.log(`📅 Date survenance: ${dateOfOccurrence}`);

    // Check if delay exceeds allowed limit (typically 30 days for insurance)
    if (delaiJours > 30) {
      this.delaiMessage = `Ce sinistre est declare hors le delai qui peut etre une clause de rejet. (Delai: ${delaiJours} jours)`;
      this.showDelaiAlert = true;
      // Don't return here - let user continue after seeing the alert
    } else {
      console.log('✅ Delay is within acceptable limits');
      // Proceed directly to guarantee check
      this.checkGuarantees(contrat);
    }

    // Use the improved contract coverage and delay check methods
    this.checkContractCoverage(contrat, dateOfOccurrence, dateCreation);
  }
