package com.fed.platform.sinister.domain;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;

@Entity
public class Driver {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String name;

    @Enumerated(EnumType.STRING)
    @Column(name = "permis_category")
    private CategorieDuPermis permisCategory;

    @Enumerated(EnumType.STRING)
    private EtatSinistre etatSinistre;

    @Enumerated(EnumType.STRING)
    @Column(name = "type_personne")
    private TypePersonne typePersonne;

    @Enumerated(EnumType.STRING)
    private Sexe sexe;

    // <-- Change here: use @Enumerated for enum, no ManyToOne
    @Enumerated(EnumType.STRING)
    @Column(name = "nature_tiers")
    private NatureTiers natureTiers;

    @Enumerated(EnumType.STRING)
    @Column(name = "type_tiers")
    private TypeTiers typeTiers;

    @Enumerated(EnumType.STRING)
    @Column(name = "compagnie_tiers")
    private CompagnieTiers compagnieTiers;

    @Enumerated(EnumType.STRING)
    @Column(name = "statut_tiers")
    private StatutTiers statutTiers;

    @Enumerated(EnumType.STRING)
    @Column(name = "organisme_tiers")
    private OrganismeTiers organismeTiers;

    @Enumerated(EnumType.STRING)
    @Column(name = "type_degat")
    private TypeDegats typeDegats;

    @Enumerated(EnumType.STRING)
    @Column(name = "role_utilisateur")
    private RoleUtilisateur roleUtilisateur;

    @Enumerated(EnumType.STRING)
    @Column(name = "type_document")
    private TypeDocument typeDocument;

    public Driver(String name, CategorieDuPermis permisCategory) {
        this.name = name;
        this.permisCategory = permisCategory;
    }

    // Getters and Setters

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public CategorieDuPermis getPermisCategory() {
        return permisCategory;
    }

    public void setPermisCategory(CategorieDuPermis permisCategory) {
        this.permisCategory = permisCategory;
    }

    public EtatSinistre getEtatSinistre() {
        return etatSinistre;
    }

    public void setEtatSinistre(EtatSinistre etatSinistre) {
        this.etatSinistre = etatSinistre;
    }

    public TypePersonne getTypePersonne() {
        return typePersonne;
    }

    public void setTypePersonne(TypePersonne typePersonne) {
        this.typePersonne = typePersonne;
    }

    public Sexe getSexe() {
        return sexe;
    }

    public void setSexe(Sexe sexe) {
        this.sexe = sexe;
    }

    public NatureTiers getNatureTiers() {
        return natureTiers;
    }

    public void setNatureTiers(NatureTiers natureTiers) {
        this.natureTiers = natureTiers;
    }

    public TypeTiers getTypeTiers() {
        return typeTiers;
    }

    public void setTypeTiers(TypeTiers typeTiers) {
        this.typeTiers = typeTiers;
    }

    public CompagnieTiers getCompagnieTiers() {
        return compagnieTiers;
    }

    public void setCompagnieTiers(CompagnieTiers compagnieTiers) {
        this.compagnieTiers = compagnieTiers;
    }

    public StatutTiers getStatutTiers() {
        return statutTiers;
    }

    public void setStatutTiers(StatutTiers statutTiers) {
        this.statutTiers = statutTiers;
    }

    public OrganismeTiers getOrganismeTiers() {
        return organismeTiers;
    }

    public void setOrganismeTiers(OrganismeTiers organismeTiers) {
        this.organismeTiers = organismeTiers;
    }

    public TypeDegats getTypeDegats() {
        return typeDegats;
    }

    public void setTypeDegats(TypeDegats typeDegats) {
        this.typeDegats = typeDegats;
    }

    public RoleUtilisateur getRoleUtilisateur() {
        return roleUtilisateur;
    }

    public void setRoleUtilisateur(RoleUtilisateur roleUtilisateur) {
        this.roleUtilisateur = roleUtilisateur;
    }

    public TypeDocument getTypeDocument() {
        return typeDocument;
    }

    public void setTypeDocument(TypeDocument typeDocument) {
        this.typeDocument = typeDocument;
    }
}
