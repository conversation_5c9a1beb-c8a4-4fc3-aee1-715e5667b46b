/* Global font styles */
* {
    font-family: 'Gilmer Bold', sans-serif;
  }
  
  
/* Layout principal full-height */
.backoffice-layout {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
  background-color: #fbfafa;
  overflow: hidden;
}
  
  .top-navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 1.5rem;
    background-color: white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    flex-wrap: wrap;
    flex-shrink: 0;
    height: 80px;
    box-sizing: border-box;
  }
  
  img {
    width: 225px;
    max-width: 100%;
  }
  
  .actions {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    flex-wrap: wrap;
  }
  
  .icon-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 0;
  }
  
  .notification-container {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .notification-badge {
    background-color: #1A3E8D;
    color: white;
    border-radius: 9999px;
    height: 16px;
    width: 16px;
    font-size: 0.625rem;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: -6px;
    right: -6px;
  }
  
  .profile {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex-wrap: nowrap;
  }
  
  .profile-circle {
    background-color: #1A3E8D;
    color: white;
    border-radius: 50%;
    height: 2rem;
    width: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    text-transform: lowercase;
    font-size: 1rem;
  }
  
  .profile-name {
    font-family: 'Gilmer Bold', sans-serif;
    margin-left: 10px;
    color: #1C3F93;
  }
  
  .bottom-bar {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 15px 30px;
    background: #1A3E8D;
    height: 60px;
    border-bottom-right-radius: 48px;
    flex-shrink: 0;
    box-sizing: border-box;
  }
  
  .bottom-bar-title {
    font-family: 'Gilmer Bold', sans-serif;
    font-size: 23px;
    color: white;
  }

  /* Container principal pour le layout horizontal */
  .main-layout {
    display: flex;
    flex: 1;
    height: calc(100vh - 140px); /* Hauteur totale moins navbar et bottom-bar */
    overflow: hidden;
  }
  
  .svg {
    position: relative;
    right: 980px;
    top: 15px;
    max-width: 100%;
    height: auto;
    display: inline-block;
  }
  
  /* Progress Steps - Sidebar Navigation */
  .progress-steps {
    width: 290px;
    background: #1C3F93;
    padding: 24px 16px;
    border-radius: 0;
    height: 100%;
    overflow-y: auto;
    transition: all 0.3s ease;
    flex-shrink: 0;
    box-sizing: border-box;
  }
  
  
  .step {
    margin-bottom: 16px;
    padding: 12px;
    border-radius: 8px;
    transition: all 0.3s ease;
  }
  
  .step:hover {
    background: rgba(255, 255, 255, 0.1);
  }
  
  .step.active {
    background: rgba(255, 255, 255, 0.2);
  }
  
  .step-title {
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    transition: color 0.3s ease;
  }
  
  .step.active .step-title {
    color: white;
    font-weight: 600;
  }
  
  /* Custom scrollbar for progress steps */
  .progress-steps::-webkit-scrollbar {
    width: 6px;
  }
  
  .progress-steps::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
  }
  
  .progress-steps::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
  }
  
  .progress-steps::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.3);
  }
  
  .main-container {
    display: flex;
    flex-direction: column;
    flex: 1;
    height: 100%;
    overflow: hidden;
    background: #fbfafa;
    margin: 0;
    padding: 0;
    position: relative;
  }
  
  .form-content {
    flex: 1;
    height: 100%;
    background: #fff;
    border-radius: 0;
    box-shadow: none;
    padding: 24px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
  }
  
  .form-content::-webkit-scrollbar {
    width: 6px;
  }
  
  .form-content::-webkit-scrollbar-track {
    background: #f1f1f1;
  }
  
  .form-content::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 3px;
  }
  
  .form-content::-webkit-scrollbar-thumb:hover {
    background: #555;
  }
  
  .form-row {
    display: flex;
    gap: 36px;
    margin-bottom: 24px;
  }
  
  .form-group {
    flex: 1;
    position: relative;
  }
  
  .form-group label {
    display: block;
    font-family: 'Gilmer Bold', sans-serif;
    font-size: 14px;
    line-height: 120%;
    margin-bottom: 8px;
    color: #6B7280;
  }
  
  .input-wrapper {
    position: relative;
  }
  
  .input-wrapper input,
  .input-wrapper select {
    font-family: 'Gilmer Bold', sans-serif;
    width: 100%;
    height: 56px;
    padding: 16px;
    border: 1px solid #E5E7EB;
    border-radius: 0;
    border-top-right-radius: 24px;
    border-bottom-left-radius: 24px;
    font-size: 14px;
    color: #374151;
    background: white;
    appearance: none;
  }
  
  .arrow-icon {
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    pointer-events: none;
  }
  
  .required {
    color: #FF0000;
  }
  
  .error-message {
    font-family: 'Gilmer Bold', sans-serif;
    color: #dc3545;
    font-size: 12px;
    margin-top: 4px;
  }
  
  /* Custom Material Date Picker */
  .custom-form-field {
    width: 398px !important;
  }
  
  ::ng-deep .mat-mdc-form-field {
    width: 398px !important;
  }
  
  ::ng-deep .mat-mdc-text-field-wrapper {
    width: 398px !important;
    background-color: white !important;
    padding: 0 !important;
    height: 56px !important;
  }
  
  ::ng-deep .mat-mdc-form-field-flex {
    padding: 0 16px !important;
    height: 56px !important;
    border: 1px solid #E5E7EB !important;
    border-radius: 0 !important;
    border-top-right-radius: 24px !important;
    border-bottom-left-radius: 24px !important;
    width: 398px !important;
  }
  
  ::ng-deep .mat-mdc-form-field-infix {
    padding: 16px 0 !important;
    width: 100% !important;
  }
  
  ::ng-deep .mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline__leading,
  ::ng-deep .mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline__notch,
  ::ng-deep .mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline__trailing {
    border-color: transparent !important;
  }
  
  ::ng-deep .mat-mdc-form-field-subscript-wrapper {
    display: none;
  }
  
  ::ng-deep #input-hey {
    width: 398px !important;
  }
  
  ::ng-deep #input-hey .mat-mdc-form-field-flex {
    width: 398px !important;
    height: 56px !important;
  }
  
  ::ng-deep #input-hey .mat-mdc-form-field-infix {
    height: 56px !important;
    display: flex;
    align-items: center;
  }
  
  ::ng-deep #input-hey input.mat-mdc-input-element {
    height: 56px !important;
    box-sizing: border-box;
  }

/* Styles pour la liste des sinistres */
.sinistres-container {
  padding: 24px;
  background: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  margin: 0;
  height: 100%;
  min-height: 500px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  flex-wrap: wrap;
  gap: 16px;
  flex-shrink: 0; /* Empêche le header de se rétrécir */
}

.header h2 {
  color: #1F2937;
  font-size: 24px;
  font-weight: 600;
  margin: 0;
}

.search-container {
  flex: 1;
  max-width: 400px;
  min-width: 250px;
}

.search-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: 12px;
  z-index: 1;
  pointer-events: none;
}

.search-input {
  width: 100%;
  padding: 12px 16px 12px 44px;
  border: 1px solid #D1D5DB;
  border-radius: 8px;
  font-size: 14px;
  background: #FFFFFF;
  transition: all 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: #1C3F93;
  box-shadow: 0 0 0 3px rgba(28, 63, 147, 0.1);
}

.search-input::placeholder {
  color: #9CA3AF;
}

.clear-search {
  position: absolute;
  right: 8px;
  background: none;
  border: none;
  padding: 4px;
  cursor: pointer;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
}

.clear-search:hover {
  background-color: #F3F4F6;
}

.error-message {
  background-color: #FEF2F2;
  border: 1px solid #FECACA;
  color: #DC2626;
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 16px;
  font-size: 14px;
}

.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 48px;
}

.spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #E5E7EB;
  border-top: 3px solid #1C3F93;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.table-responsive {
  overflow-y: auto;
  overflow-x: auto;
  border-radius: 8px;
  border: 1px solid #E5E7EB;
  flex: 1;
  height: 400px; /* Hauteur fixe pour permettre le défilement */
  max-height: calc(100vh - 350px);
}

/* Styles pour le défilement du tableau */
.table-responsive::-webkit-scrollbar {
  width: 12px;
  height: 12px;
}

.table-responsive::-webkit-scrollbar-track {
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.table-responsive::-webkit-scrollbar-thumb {
  background: #6c757d;
  border-radius: 6px;
  border: 2px solid #f8f9fa;
}

.table-responsive::-webkit-scrollbar-thumb:hover {
  background: #495057;
}

.table-responsive::-webkit-scrollbar-corner {
  background: #f8f9fa;
}

table {
  width: 100%;
  border-collapse: collapse;
  background: #FFFFFF;
  min-height: 100%;
}

thead {
  background: #F9FAFB;
}

th {
  padding: 16px;
  text-align: left;
  font-weight: 600;
  color: #374151;
  font-size: 14px;
  border-bottom: 1px solid #E5E7EB;
}

td {
  padding: 16px;
  border-bottom: 1px solid #F3F4F6;
  color: #1F2937;
  font-size: 14px;
}

tr:hover {
  background-color: #F9FAFB;
}

.numero-sinistre {
  font-weight: 600;
  color: #1C3F93;
}

.status-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-en-cours {
  background-color: #FEF3C7;
  color: #D97706;
}

.status-traite {
  background-color: #D1FAE5;
  color: #059669;
}

.status-attente {
  background-color: #DBEAFE;
  color: #2563EB;
}

.actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.icon-btn {
  background: none;
  border: none;
  padding: 8px;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.icon-btn:hover {
  background-color: #F3F4F6;
  transform: scale(1.05);
}

.icon-btn.view:hover {
  background-color: #EBF4FF;
}

.icon-btn.edit:hover {
  background-color: #EBF4FF;
}

.icon-btn.delete:hover {
  background-color: #FEF2F2;
}

.no-data {
  text-align: center;
  padding: 48px 24px;
  color: #6B7280;
}

.no-data svg {
  margin-bottom: 16px;
  opacity: 0.5;
}

.no-data p {
  font-size: 16px;
  margin: 0;
}

/* Responsive design pour les sinistres */
@media (max-width: 768px) {
  .sinistres-container {
    margin: 16px;
    padding: 16px;
  }

  .header {
    flex-direction: column;
    align-items: stretch;
  }

  .search-container {
    max-width: none;
  }

  .table-responsive {
    font-size: 12px;
  }

  th, td {
    padding: 12px 8px;
  }

  .actions {
    flex-direction: column;
    gap: 4px;
  }

  .icon-btn {
    padding: 6px;
  }
}