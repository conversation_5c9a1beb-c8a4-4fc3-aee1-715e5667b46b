<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test CSM.ai API</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        .button { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        .button:hover { background: #0056b3; }
        .log { background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 10px 0; font-family: monospace; white-space: pre-wrap; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test CSM.ai API</h1>
        
        <div class="section">
            <h2>📷 Upload Image for 3D Generation</h2>
            <input type="file" id="imageInput" accept="image/*">
            <button class="button" onclick="testImageTo3D()">Generate 3D Model</button>
        </div>

        <div class="section">
            <h2>📊 Session Status Check</h2>
            <input type="text" id="sessionIdInput" placeholder="Enter Session ID" style="width: 300px; padding: 5px;">
            <button class="button" onclick="checkSessionStatus()">Check Status</button>
        </div>

        <div class="section">
            <h2>📝 API Logs</h2>
            <div id="logs" class="log">Ready to test CSM.ai API...\n</div>
            <button class="button" onclick="clearLogs()">Clear Logs</button>
        </div>
    </div>

    <script>
        const API_KEY = '793308e392807ce9d1Ad41F3b41EafDF';
        const API_BASE = 'https://api.csm.ai/v3';

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('logs');
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info';
            logElement.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function clearLogs() {
            document.getElementById('logs').innerHTML = 'Logs cleared...\n';
        }

        async function convertImageToBase64(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = () => resolve(reader.result);
                reader.onerror = reject;
                reader.readAsDataURL(file);
            });
        }

        async function testImageTo3D() {
            const fileInput = document.getElementById('imageInput');
            const file = fileInput.files[0];
            
            if (!file) {
                log('❌ Please select an image file first', 'error');
                return;
            }

            log(`🚀 Starting 3D generation for: ${file.name} (${file.size} bytes)`);
            
            try {
                // Convert image to base64
                log('📸 Converting image to base64...');
                const imageDataUrl = await convertImageToBase64(file);
                log('✅ Image converted to base64');

                // Prepare request
                const requestBody = {
                    type: 'image_to_3d',
                    input: {
                        image: imageDataUrl,
                        manual_segmentation: false,
                        num_variations: 1,
                        model: 'sculpt',
                        settings: {
                            geometry_model: 'base',
                            texture_model: 'none',
                            topology: 'tris',
                            resolution: 100000,
                            symmetry: 'off',
                            scaled_bbox: [-1, -1, -1],
                            preserve_aspect_ratio: false,
                            pivot_point: [0, -0.5, 0]
                        }
                    }
                };

                log('🌐 Sending request to CSM.ai API...');
                
                const response = await fetch(`${API_BASE}/sessions/`, {
                    method: 'POST',
                    headers: {
                        'x-api-key': API_KEY,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestBody)
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const result = await response.json();
                log(`✅ Session created successfully!`, 'success');
                log(`📋 Session ID: ${result._id}`, 'success');
                log(`📊 Status: ${result.status}`, 'info');
                
                // Auto-fill session ID for status check
                document.getElementById('sessionIdInput').value = result._id;
                
                log('🔄 You can now check the session status using the Session ID above');

            } catch (error) {
                log(`❌ Error: ${error.message}`, 'error');
                console.error('Full error:', error);
            }
        }

        async function checkSessionStatus() {
            const sessionId = document.getElementById('sessionIdInput').value.trim();
            
            if (!sessionId) {
                log('❌ Please enter a Session ID', 'error');
                return;
            }

            log(`🔍 Checking status for session: ${sessionId}`);
            
            try {
                const response = await fetch(`${API_BASE}/sessions/${sessionId}`, {
                    method: 'GET',
                    headers: {
                        'x-api-key': API_KEY,
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const result = await response.json();
                log(`📊 Status: ${result.status}`, 'info');
                
                if (result.status === 'complete') {
                    log('🎉 Generation complete!', 'success');
                    if (result.output?.meshes?.[0]?.data?.glb_url) {
                        log(`🎯 GLB Model URL: ${result.output.meshes[0].data.glb_url}`, 'success');
                    }
                    if (result.output?.meshes?.[0]?.data?.obj_url) {
                        log(`🎯 OBJ Model URL: ${result.output.meshes[0].data.obj_url}`, 'success');
                    }
                } else if (result.status === 'failed') {
                    log('❌ Generation failed', 'error');
                } else {
                    log('⏳ Still processing... Check again in a few seconds', 'info');
                }

            } catch (error) {
                log(`❌ Error: ${error.message}`, 'error');
                console.error('Full error:', error);
            }
        }

        // Log initial info
        log('🔑 API Key: ' + API_KEY.substring(0, 8) + '...');
        log('🌐 API Base: ' + API_BASE);
        log('📝 Ready to test CSM.ai API');
    </script>
</body>
</html>
