package com.fed.platform.sinister.service;

import java.util.List;

import org.springframework.stereotype.Service;

import com.fed.platform.sinister.domain.NatureTiers;
import com.fed.platform.sinister.domain.NatureTiersEntity;
import com.fed.platform.sinister.repository.NatureTiersRepository;

@Service
public class NatureTiersService {

    private final NatureTiersRepository repository;

    public NatureTiersService(NatureTiersRepository repository) {
        this.repository = repository;
    }

    public List<NatureTiersEntity> findAll() {
        return repository.findAll();
    }

    public NatureTiersEntity save(NatureTiers nt) {
        return repository.save(new NatureTiersEntity(nt));
    }

    public void deleteById(Long id) {
        repository.deleteById(id);
    }
}
