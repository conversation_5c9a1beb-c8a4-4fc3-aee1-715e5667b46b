/* === Base styles (unchanged) === */


body {
  background-color: #fbfafa;
  margin: 0;
  padding: 0;
  width: 100%;
  min-height: 190vh;
  font-family: 'Gilmer Bold', sans-serif;
}
.top-navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 1.5rem;
    background-color: white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    flex-wrap: wrap;
  }
  
  img {
    width: 225px;
    max-width: 100%;
  }
  
  .actions {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    flex-wrap: wrap;
  }
  
  .icon-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 0;
  }
  
  .notification-container {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .notification-icon-wrapper {
    position: relative;
    display: inline-flex;
  }
  
  .notification-icon {
    width: 24px;
    height: 24px;
    color: #1A3E8D;
  }
  
  .notification-badge {
    background-color: #1A3E8D;
    color: white;
    border-radius: 9999px;
    height: 16px;
    width: 16px;
    font-size: 0.625rem;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: -6px;
    right: -6px;
  }
  
  .profile {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex-wrap: nowrap;
  }
  
  .profile-circle {
    background-color: #1A3E8D;
    color: white;
    border-radius: 50%;
    height: 2rem;
    width: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    text-transform: lowercase;
    font-size: 1rem;
  }
  
  .profile-name {
    font-size: 0.875rem;
    color: #4B5563;
  }
  
  .bottom-bar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 30px;
    background: #1A3E8D;
    margin-bottom: 20px;
    position: relative;
    height: 60px;
    border-bottom-right-radius: 48px;
  }
  
  .bottom-bar-title {
    font-family: 'Gilmer Bold', sans-serif;
    font-size: 23px;
    color: white;
    position: relative;
    left: 250px;
    bottom: 10px;
  }
  
  .bottom-bar-close {
    font-size: 1rem;
    font-weight: bold;
    cursor: pointer;
    position: relative;
    right: 300px;
  }
  
  .h-8 {
    position: absolute;
    width: 211.35px;
    height: 45px;
    left: 36px;
    top: 16px;
    max-width: 90%;
  }
  
  .vertical-line {
    width: 1px;
    height: 50px;
    background-color: #8691a8;
  }
  
  .svg {
    position: relative;
    right: 980px;
    top: 15px;
    max-width: 100%;
    height: auto;
    display: inline-block;
  }
  
  /* When screen is small (like mobile), make sure it stays block and responsive */
  @media (max-width: 768px) {
    .svg {
      display: block;
      right: auto;
      left: 0;
      margin: 0 auto;
      top: 15px;
    }
  }
  
  
  /* === Responsive Media Queries === */
  @media (max-width: 1024px) {
    .bottom-bar-title {
      left: 100px;
      bottom: 0;
      font-size: 1.2rem;
    }
    .bottom-bar-close {
      right: 100px;
    }
    .svg {
      right: 200px;
      top: 10px;
    }
  }
  
  @media (max-width: 768px) {
    .top-navbar {
      flex-direction: column;
      align-items: flex-start;
      gap: 1rem;
    }
  
    .actions {
      width: 100%;
      justify-content: space-between;
    }
  
    .bottom-bar {
      flex-direction: column;
      gap: 1rem;
      text-align: center;
    }
  
    .bottom-bar-title,
    .bottom-bar-close {
      position: static;
      text-align: center;
    }
  
    .svg {
      position: static;
      margin-top: 10px;
    }
  }
  
  @media (max-width: 480px) {
    .profile-name {
      font-size: 0.75rem;
    }
  
    .bottom-bar-title {
      font-size: 1rem;
    }
  
    .notification-icon,
    .notification-badge {
      transform: scale(0.85);
    }
  
    .h-8 {
      position: static;
      width: 180px;
      height: auto;
    }
  }
  
  body {
    background-color: #fbfafa;
    margin: 0;
    padding: 0;
    width: 100%;
    min-height: 200vh;
    font-family: 'Gilmer Bold', sans-serif;
  }
.form-container {
  display: flex;
  max-width: 2000px;
  margin: 1px;
  padding: 30px;

}

@media (max-width: 900px) {
  .form-container {
    padding: 10px;
    overflow-x: auto;
  }
}

.header {
    margin-bottom: 30px;
}

.header h1 {
    color: #1C3F93;
    font-family: 'Gilmer Bold', sans-serif;
    font-size: 32px;
    font-weight: 600;
    margin: 0;
}

@media (max-width: 768px) {
    .header h1 {
        font-size: 24px;
    }
    .header {
        margin-bottom: 20px;
    }
}

.header h1 {
    font-family: 'Gilmer Bold', sans-serif;
    font-size: 25px;
    color: #0A1633;
    position: relative;
    left: 300px;
    bottom: 20px;
  }

.page-title {
    color: #1C3F93;
    font-family: 'Gilmer Bold', sans-serif;
    font-size: 32px;
    font-weight: 600;
    margin: 0 0 24px 40px;
    position: relative;
    left: 460px;
    top: 30px;
}

@media (max-width: 768px) {
    .page-title {
        font-size: 24px;
        margin: 0 0 16px 20px;
    }
}

/* Form content styles */
.form-grid {
  padding: 2rem;
  margin: 20px;
}

.section-title {
  color: #1C3F93;
  font-size: 20px;
  font-family: 'Gilmer Bold';
  
  margin: 30px;
  margin-bottom: 50px;
  font-family: 'Gilmer Bold', sans-serif;
}
.section-title h2{
  position: relative;
  top: 30px;
  
}

.form-row {
  display: flex;
  gap: 36px;
  margin-bottom: 24px;
  padding: 0 20px;
}

.form-group {
  flex: 1;
}

.form-group label {
  display: block;
  font-family: 'Gilmer Bold';
  font-size: 14px;
  line-height: 120%;
  letter-spacing: 0%;
  margin-bottom: 8px;
  color: #1C3F93;
}

.required {
  color: #FF0000;
}

.progress-steps {
    width: 250px;
    margin-right: 10px;
    position: relative;
    right: 350px;
    display: block;
  }
  
  .step {
    position: relative;
    margin-bottom: 30px;
    padding-left: 40px;
  }
  
  .step-number {
    position: absolute;
    left: 0;
    top: 0;
    width: 24px;
    height: 24px;
    background-color: #B9C3DE;
    border-radius: 50%;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: 'Gilmer Bold', sans-serif;
    font-size: 16px;
  }

  .step-number svg {
    width: 24px;
    height: 24px;
    position: absolute;
    top: 0;
    left: 0;
  }

  .step.active .step-number {
    background-color: transparent;
  }
  
  .step.active .step-number {
    background-color: #1C3F93;
  }
  
  .step-title {
    font-family: 'Gilmer Bold', sans-serif;
    font-size: 16px;
    color: #B9C3DE;
    margin-bottom: 5px;
  }
  
  .step.active .step-title.active {
    color: #1C3F93;
  }
  


  .main-form-content{
  width: 880px;
  height: 500px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: relative;
  left: 320px;
  bottom: 380px;
}

.form-group{
  position: relative;
  right: 40px;
  bottom: 30px;
  
}

/* Select styles */
.form-group select {
  width: 398px;
  height: 56px;
  border-width: 1px;
  gap: 5px;
  border-top-right-radius: 24px;
  border-bottom-left-radius: 24px;
  padding: 16px;
  border: 1px solid #DCDCDC;
  appearance: none;
  background: white;
  color: #6B6B6B;
  font-size: 14px;
  font-family: 'Gilmer Bold', sans-serif;
}

.form-group label {
  font-family: 'Gilmer Bold', sans-serif;
  color: #6B6B6B;
  
}

.input-wrapper {
  position: relative;
}

.arrow-icon {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
}

/* Address input styles */
.form-group input[type="text"].address-input {
  width: 832px;
  height: 56px;
  border-width: 1px;
  gap: 5px;
  border-top-right-radius: 24px;
  border-bottom-left-radius: 24px;
  padding: 16px;
  border: 1px solid #DCDCDC;
  
  
}


.toggle-group {
  width: 336px;
  height: 55px;
  position: relative;
  right: 35px;
  bottom: 40px;
  font-family: 'Gilmer Bold';
  
}

.toggle-group label {
  display: block;
  margin-bottom: 12px;
  color: #6B6B6B;
  font-family: 'Gilmer Bold', sans-serif;
  font-size: 14px;
}

.toggle-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.toggle-switch {
  width: 40px;
  height: 20px;
  background: #E0E0E0;
  border-radius: 10px;
  position: relative;
  cursor: pointer;
  transition: background-color 0.3s;
}

.toggle-switch.active {
  background: #1C3F93;
}

.toggle-circle {
  width: 16px;
  height: 16px;
  background: white;
  border-radius: 50%;
  position: absolute;
  top: 2px;
  left: 2px;
  transition: transform 0.3s;
}

.toggle-switch.active .toggle-circle {
  transform: translateX(20px);
}

.toggle-container span {
  color: #6B6B6B;
  font-size: 14px;
  font-family: 'Gilmer Bold', sans-serif;
}

.error-message {
  color: #dc3545;
  font-size: 12px;
  margin-top: 5px;
  font-family: 'Gilmer Bold', sans-serif;
}

.submit-button {
  background-color: #1C3F93;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 24px;
  font-family: 'Gilmer Bold', sans-serif;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s ease;
  margin-top: 20px;
  margin-left: auto;
  display: block;
}

.submit-button:hover {
  background-color: #152f6e;
}

.form-group select.ng-invalid.ng-touched,
.form-group input.ng-invalid.ng-touched {
  border-color: #dc3545;
}

/* Footer Action Bar */
.footer-action-bar-container {
    width: 820px;
    max-width: 880px;
    min-height: 92px;
    background: #fff;
    border-radius: 24px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 24px;
    margin-top: 2px;
    box-shadow: 0 2px 8px #F4F4F4;
    padding: 0 32px 0 0;
    position: relative;
    right: 40px;
    top: 60px;
    border-bottom-right-radius: 16px;
    border-bottom-left-radius: 16px;
    background: var(--white, #FFFFFF);
}

@media (max-width: 900px) {
    .footer-action-bar-container {
        width: 98vw;
        min-height: auto;
        border-radius: 18px;
        padding: 0 8px;
        justify-content: center;
    }
    .footer-btn {
        width: 90vw;
        justify-content: center;
    }
}

.footer-btn {
    font-family: 'Gilmer Bold', sans-serif;
    font-size: 18px;
    border: none;
    outline: none;
    background: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    transition: color 0.2s, background 0.2s;
    height: 48px;
    padding: 0 16px;
    margin: 0;
}

.footer-btn-retour {
    color: #1C3F93;
    background: none;
    border-radius: 0 24px 24px 0;
    font-weight: 600;
}

.footer-btn-retour:hover {
    text-decoration: underline;
    background: #F4F4F4;
}

.footer-btn-continuer {
    color: #fff;
    background: #00A887;
    width: 135px;
    height: 48px;
    border-top-right-radius: 24px;
    border-bottom-left-radius: 24px;
    border-top-left-radius: 0;
    border-bottom-right-radius: 0;
    font-weight: 600;
}

.footer-btn-continuer:hover {
    background: #00916f;
}

.footer-btn-icon-left {
    font-size: 22px;
    margin-right: 6px;
    line-height: 1;
}

.footer-btn-icon-right {
    font-size: 22px;
    margin-left: 6px;
    line-height: 1;
}

@media (max-width: 480px) {
    .footer-action-bar-container {
        padding: 12px;
    }

    .footer-btn {
        padding: 8px 12px;
        font-size: 14px;
    }
}