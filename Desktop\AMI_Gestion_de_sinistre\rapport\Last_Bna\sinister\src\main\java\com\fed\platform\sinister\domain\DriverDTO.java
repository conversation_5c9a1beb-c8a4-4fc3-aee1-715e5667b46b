package com.fed.platform.sinister.domain;

public class DriverDTO {
    private Long id;
    private String name;
    private String permisCategory;
    private String etatSinistre;
    private String typePersonne;
    private String sexe;
    private String natureTiers;
    private String typeTiers;
    private String compagnieTiers;
    private String statutTiers;
    private String organismeTiers;
    private String typeDegats;
    private String roleUtilisateur;
    private String typeNotification;
    private String typeDocument;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPermisCategory() {
        return permisCategory;
    }

    public void setPermisCategory(String permisCategory) {
        this.permisCategory = permisCategory;
    }

    public String getEtatSinistre() {
        return etatSinistre;
    }

    public void setEtatSinistre(String etatSinistre) {
        this.etatSinistre = etatSinistre;
    }

    public String getTypePersonne() {
        return typePersonne;
    }

    public void setTypePersonne(String typePersonne) {
        this.typePersonne = typePersonne;
    }

    public String getSexe() {
        return sexe;
    }

    public void setSexe(String sexe) {
        this.sexe = sexe;
    }

    public String getNatureTiers() {
        return natureTiers;
    }

    public void setNatureTiers(String natureTiers) {
        this.natureTiers = natureTiers;
    }

    public String getTypeTiers() {
        return typeTiers;
    }

    public void setTypeTiers(String typeTiers) {
        this.typeTiers = typeTiers;
    }

    public String getCompagnieTiers() {
        return compagnieTiers;
    }

    public void setCompagnieTiers(String compagnieTiers) {
        this.compagnieTiers = compagnieTiers;
    }

    public String getStatutTiers() {
        return statutTiers;
    }

    public void setStatutTiers(String statutTiers) {
        this.statutTiers = statutTiers;
    }

    public String getOrganismeTiers() {
        return organismeTiers;
    }

    public void setOrganismeTiers(String organismeTiers) {
        this.organismeTiers = organismeTiers;
    }

    public String getTypeDegats() {
        return typeDegats;
    }

    public void setTypeDegats(String typeDegats) {
        this.typeDegats = typeDegats;
    }

    public String getRoleUtilisateur() {
        return roleUtilisateur;
    }

    public void setRoleUtilisateur(String roleUtilisateur) {
        this.roleUtilisateur = roleUtilisateur;
    }

    public String getTypeNotification() {
        return typeNotification;
    }

    public void setTypeNotification(String typeNotification) {
        this.typeNotification = typeNotification;
    }

    public String getTypeDocument() {
        return typeDocument;
    }

    public void setTypeDocument(String typeDocument) {
        this.typeDocument = typeDocument;
    }
}
