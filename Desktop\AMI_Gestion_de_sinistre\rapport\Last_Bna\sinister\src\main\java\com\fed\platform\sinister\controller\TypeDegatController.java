package com.fed.platform.sinister.controller;

import java.util.List;

import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.fed.platform.sinister.domain.TypeDegat;
import com.fed.platform.sinister.repository.TypeDegatRepository;

@RestController
@RequestMapping("/api/type-degats")
@CrossOrigin(origins = "http://localhost:4200")
public class TypeDegatController {

    private final TypeDegatRepository typeDegatRepository;

    public TypeDegatController(TypeDegatRepository typeDegatRepository) {
        this.typeDegatRepository = typeDegatRepository;
    }

    @GetMapping
    public List<TypeDegat> getAllTypeDegats() {
        return typeDegatRepository.findAll();
    }

    @PostMapping
    public TypeDegat createTypeDegat(@RequestBody TypeDegat typeDegat) {
        return typeDegatRepository.save(typeDegat);
    }

    @PutMapping("/{id}")
    public TypeDegat updateTypeDegat(@PathVariable Long id, @RequestBody TypeDegat typeDegat) {
        typeDegat.setId(id);
        return typeDegatRepository.save(typeDegat);
    }

    @DeleteMapping("/{id}")
    public void deleteTypeDegat(@PathVariable Long id) {
        typeDegatRepository.deleteById(id);
    }

}
