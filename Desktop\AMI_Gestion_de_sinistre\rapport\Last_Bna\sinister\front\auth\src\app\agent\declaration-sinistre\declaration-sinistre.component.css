/* === Base styles (unchanged) === */


.top-navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 1.5rem;
  background-color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  flex-wrap: wrap;
}

img {
  width: 225px;
  max-width: 100%;
}

.actions {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.icon-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
}

.notification-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.notification-icon-wrapper {
  position: relative;
  display: inline-flex;
}

.notification-icon {
  width: 24px;
  height: 24px;
  color: #1A3E8D;
}

.notification-badge {
  background-color: #1A3E8D;
  color: white;
  border-radius: 9999px;
  height: 16px;
  width: 16px;
  font-size: 0.625rem;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: -6px;
  right: -6px;
}

.profile {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: nowrap;
}

.profile-circle {
  background-color: #1A3E8D;
  color: white;
  border-radius: 50%;
  height: 2rem;
  width: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  text-transform: lowercase;
  font-size: 1rem;
}

.profile-name {
  font-size: 0.875rem;
  color: #4B5563;
}

.bottom-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 30px;
  background: #1A3E8D;
  margin-bottom: 20px;
  position: relative;
  height: 60px;
  border-bottom-right-radius: 48px;
}

.bottom-bar-title {
  font-family: 'Gilmer Bold', sans-serif;
  font-size: 23px;
  color: white;
  position: relative;
  left: 250px;
  bottom: 10px;
}

.bottom-bar-close {
  font-size: 1rem;
  font-weight: bold;
  cursor: pointer;
  position: relative;
  right: 300px;
}

.h-8 {
  position: absolute;
  width: 211.35px;
  height: 45px;
  left: 36px;
  top: 16px;
  max-width: 90%;
}

.vertical-line {
  width: 1px;
  height: 50px;
  background-color: #8691a8;
}

.svg {
  position: relative;
  right: 980px;
  top: 15px;
  max-width: 100%;
  height: auto;
  display: inline-block;
}

/* When screen is small (like mobile), make sure it stays block and responsive */
@media (max-width: 768px) {
  .svg {
    display: block;
    right: auto;
    left: 0;
    margin: 0 auto;
    top: 15px;
  }
}


/* === Responsive Media Queries === */
@media (max-width: 1024px) {
  .bottom-bar-title {
    left: 100px;
    bottom: 0;
    font-size: 1.2rem;
  }
  .bottom-bar-close {
    right: 100px;
  }
  .svg {
    right: 200px;
    top: 10px;
  }
}

@media (max-width: 768px) {
  .top-navbar {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .actions {
    width: 100%;
    justify-content: space-between;
  }

  .bottom-bar {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .bottom-bar-title,
  .bottom-bar-close {
    position: static;
    text-align: center;
  }

  .svg {
    position: static;
    margin-top: 10px;
  }
}

@media (max-width: 480px) {
  .profile-name {
    font-size: 0.75rem;
  }

  .bottom-bar-title {
    font-size: 1rem;
  }

  .notification-icon,
  .notification-badge {
    transform: scale(0.85);
  }

  .h-8 {
    position: static;
    width: 180px;
    height: auto;
  }
}
