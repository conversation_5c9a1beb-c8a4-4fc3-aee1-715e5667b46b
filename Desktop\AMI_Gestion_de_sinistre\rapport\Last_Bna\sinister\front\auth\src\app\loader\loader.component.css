.loader-canvas {
  width: 100%;
  height: 100vh;
  overflow: hidden;
  background-color: white;
  transition: opacity 1s ease-in-out, transform 1s ease-in-out;
  transform-origin: center;
  display: block;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  box-sizing: border-box;
}

:host {
  display: block;
  opacity: 0;
  animation: fadeIn 1.5s ease forwards;
}

@keyframes fadeIn {
  to {
    opacity: 1;
  }
}

/* Responsive Fixes */
@media (max-width: 768px) {
  .loader-canvas {
    padding: 0.5rem;
    height: auto;
    min-height: 100vh;
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .loader-canvas {
    padding: 0.5rem;
    height: auto;
    flex-direction: column;
  }
}
