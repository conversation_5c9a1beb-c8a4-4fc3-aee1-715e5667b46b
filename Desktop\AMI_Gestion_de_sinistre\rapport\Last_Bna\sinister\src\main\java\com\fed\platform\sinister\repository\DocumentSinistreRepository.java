package com.fed.platform.sinister.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import com.fed.platform.sinister.domain.DocumentSinistre;

@Repository
public interface DocumentSinistreRepository extends JpaRepository<DocumentSinistre, Long> {

    List<DocumentSinistre> findByLibelleEvenement(String libelleEvenement);

    List<DocumentSinistre> findByLibelleEvenementContainingIgnoreCase(String libelleEvenement);

    List<DocumentSinistre> findByDocument(String document);

    boolean existsByLibelleEvenementAndDocument(String libelleEvenement, String document);

    @Query("SELECT DISTINCT d.libelleEvenement FROM DocumentSinistre d ORDER BY d.libelleEvenement")
    List<String> findDistinctLibelleEvenement();
}
