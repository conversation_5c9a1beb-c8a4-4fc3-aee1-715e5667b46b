import { Component } from '@angular/core';

@Component({
  selector: 'app-documents',
  standalone: true,
  imports: [],
  templateUrl: './documents.component.html',
  styleUrls: ['./documents.component.css']
})
export class DocumentsComponent {
  documents: { [key: number]: File | null } = {
    1: null,
    2: null,
    3: null
  };

  onFileSelected(event: any, documentNumber: number) {
    const file = event.target.files[0];
    if (file) {
      // Check file type
      const allowedTypes = ['image/jpeg', 'image/png', 'application/pdf'];
      if (!allowedTypes.includes(file.type)) {
        alert('Type de fichier non valide. Veuillez sélectionner un fichier JPEG, PNG ou PDF.');
        return;
      }

      // Check file size (max 5MB)
      const maxSize = 5 * 1024 * 1024; // 5MB in bytes
      if (file.size > maxSize) {
        alert('Le fichier est trop volumineux. La taille maximale est de 5MB.');
        return;
      }

      this.documents[documentNumber] = file;
    }
  }

  getFileName(documentNumber: number): string {
    const file = this.documents[documentNumber];
    return file ? file.name : 'Aucun fichier disponible (JPEG,PNG,PDF)';
  }
}
