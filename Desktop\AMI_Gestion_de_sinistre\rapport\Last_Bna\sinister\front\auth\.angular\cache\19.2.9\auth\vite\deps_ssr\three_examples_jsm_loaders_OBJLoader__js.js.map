{"version": 3, "sources": ["../../../../../../node_modules/three/examples/jsm/loaders/OBJLoader.js"], "sourcesContent": ["import { BufferGeometry, FileLoader, Float32BufferAttribute, Group, LineBasicMaterial, LineSegments, Loader, Material, Mesh, MeshPhongMaterial, Points, PointsMaterial, Vector3, Color, SRGBColorSpace } from 'three';\n\n// o object_name | g group_name\nconst _object_pattern = /^[og]\\s*(.+)?/;\n// mtllib file_reference\nconst _material_library_pattern = /^mtllib /;\n// usemtl material_name\nconst _material_use_pattern = /^usemtl /;\n// usemap map_name\nconst _map_use_pattern = /^usemap /;\nconst _face_vertex_data_separator_pattern = /\\s+/;\nconst _vA = new Vector3();\nconst _vB = new Vector3();\nconst _vC = new Vector3();\nconst _ab = new Vector3();\nconst _cb = new Vector3();\nconst _color = new Color();\nfunction ParserState() {\n  const state = {\n    objects: [],\n    object: {},\n    vertices: [],\n    normals: [],\n    colors: [],\n    uvs: [],\n    materials: {},\n    materialLibraries: [],\n    startObject: function (name, fromDeclaration) {\n      // If the current object (initial from reset) is not from a g/o declaration in the parsed\n      // file. We need to use it for the first parsed g/o to keep things in sync.\n      if (this.object && this.object.fromDeclaration === false) {\n        this.object.name = name;\n        this.object.fromDeclaration = fromDeclaration !== false;\n        return;\n      }\n      const previousMaterial = this.object && typeof this.object.currentMaterial === 'function' ? this.object.currentMaterial() : undefined;\n      if (this.object && typeof this.object._finalize === 'function') {\n        this.object._finalize(true);\n      }\n      this.object = {\n        name: name || '',\n        fromDeclaration: fromDeclaration !== false,\n        geometry: {\n          vertices: [],\n          normals: [],\n          colors: [],\n          uvs: [],\n          hasUVIndices: false\n        },\n        materials: [],\n        smooth: true,\n        startMaterial: function (name, libraries) {\n          const previous = this._finalize(false);\n\n          // New usemtl declaration overwrites an inherited material, except if faces were declared\n          // after the material, then it must be preserved for proper MultiMaterial continuation.\n          if (previous && (previous.inherited || previous.groupCount <= 0)) {\n            this.materials.splice(previous.index, 1);\n          }\n          const material = {\n            index: this.materials.length,\n            name: name || '',\n            mtllib: Array.isArray(libraries) && libraries.length > 0 ? libraries[libraries.length - 1] : '',\n            smooth: previous !== undefined ? previous.smooth : this.smooth,\n            groupStart: previous !== undefined ? previous.groupEnd : 0,\n            groupEnd: -1,\n            groupCount: -1,\n            inherited: false,\n            clone: function (index) {\n              const cloned = {\n                index: typeof index === 'number' ? index : this.index,\n                name: this.name,\n                mtllib: this.mtllib,\n                smooth: this.smooth,\n                groupStart: 0,\n                groupEnd: -1,\n                groupCount: -1,\n                inherited: false\n              };\n              cloned.clone = this.clone.bind(cloned);\n              return cloned;\n            }\n          };\n          this.materials.push(material);\n          return material;\n        },\n        currentMaterial: function () {\n          if (this.materials.length > 0) {\n            return this.materials[this.materials.length - 1];\n          }\n          return undefined;\n        },\n        _finalize: function (end) {\n          const lastMultiMaterial = this.currentMaterial();\n          if (lastMultiMaterial && lastMultiMaterial.groupEnd === -1) {\n            lastMultiMaterial.groupEnd = this.geometry.vertices.length / 3;\n            lastMultiMaterial.groupCount = lastMultiMaterial.groupEnd - lastMultiMaterial.groupStart;\n            lastMultiMaterial.inherited = false;\n          }\n\n          // Ignore objects tail materials if no face declarations followed them before a new o/g started.\n          if (end && this.materials.length > 1) {\n            for (let mi = this.materials.length - 1; mi >= 0; mi--) {\n              if (this.materials[mi].groupCount <= 0) {\n                this.materials.splice(mi, 1);\n              }\n            }\n          }\n\n          // Guarantee at least one empty material, this makes the creation later more straight forward.\n          if (end && this.materials.length === 0) {\n            this.materials.push({\n              name: '',\n              smooth: this.smooth\n            });\n          }\n          return lastMultiMaterial;\n        }\n      };\n\n      // Inherit previous objects material.\n      // Spec tells us that a declared material must be set to all objects until a new material is declared.\n      // If a usemtl declaration is encountered while this new object is being parsed, it will\n      // overwrite the inherited material. Exception being that there was already face declarations\n      // to the inherited material, then it will be preserved for proper MultiMaterial continuation.\n\n      if (previousMaterial && previousMaterial.name && typeof previousMaterial.clone === 'function') {\n        const declared = previousMaterial.clone(0);\n        declared.inherited = true;\n        this.object.materials.push(declared);\n      }\n      this.objects.push(this.object);\n    },\n    finalize: function () {\n      if (this.object && typeof this.object._finalize === 'function') {\n        this.object._finalize(true);\n      }\n    },\n    parseVertexIndex: function (value, len) {\n      const index = parseInt(value, 10);\n      return (index >= 0 ? index - 1 : index + len / 3) * 3;\n    },\n    parseNormalIndex: function (value, len) {\n      const index = parseInt(value, 10);\n      return (index >= 0 ? index - 1 : index + len / 3) * 3;\n    },\n    parseUVIndex: function (value, len) {\n      const index = parseInt(value, 10);\n      return (index >= 0 ? index - 1 : index + len / 2) * 2;\n    },\n    addVertex: function (a, b, c) {\n      const src = this.vertices;\n      const dst = this.object.geometry.vertices;\n      dst.push(src[a + 0], src[a + 1], src[a + 2]);\n      dst.push(src[b + 0], src[b + 1], src[b + 2]);\n      dst.push(src[c + 0], src[c + 1], src[c + 2]);\n    },\n    addVertexPoint: function (a) {\n      const src = this.vertices;\n      const dst = this.object.geometry.vertices;\n      dst.push(src[a + 0], src[a + 1], src[a + 2]);\n    },\n    addVertexLine: function (a) {\n      const src = this.vertices;\n      const dst = this.object.geometry.vertices;\n      dst.push(src[a + 0], src[a + 1], src[a + 2]);\n    },\n    addNormal: function (a, b, c) {\n      const src = this.normals;\n      const dst = this.object.geometry.normals;\n      dst.push(src[a + 0], src[a + 1], src[a + 2]);\n      dst.push(src[b + 0], src[b + 1], src[b + 2]);\n      dst.push(src[c + 0], src[c + 1], src[c + 2]);\n    },\n    addFaceNormal: function (a, b, c) {\n      const src = this.vertices;\n      const dst = this.object.geometry.normals;\n      _vA.fromArray(src, a);\n      _vB.fromArray(src, b);\n      _vC.fromArray(src, c);\n      _cb.subVectors(_vC, _vB);\n      _ab.subVectors(_vA, _vB);\n      _cb.cross(_ab);\n      _cb.normalize();\n      dst.push(_cb.x, _cb.y, _cb.z);\n      dst.push(_cb.x, _cb.y, _cb.z);\n      dst.push(_cb.x, _cb.y, _cb.z);\n    },\n    addColor: function (a, b, c) {\n      const src = this.colors;\n      const dst = this.object.geometry.colors;\n      if (src[a] !== undefined) dst.push(src[a + 0], src[a + 1], src[a + 2]);\n      if (src[b] !== undefined) dst.push(src[b + 0], src[b + 1], src[b + 2]);\n      if (src[c] !== undefined) dst.push(src[c + 0], src[c + 1], src[c + 2]);\n    },\n    addUV: function (a, b, c) {\n      const src = this.uvs;\n      const dst = this.object.geometry.uvs;\n      dst.push(src[a + 0], src[a + 1]);\n      dst.push(src[b + 0], src[b + 1]);\n      dst.push(src[c + 0], src[c + 1]);\n    },\n    addDefaultUV: function () {\n      const dst = this.object.geometry.uvs;\n      dst.push(0, 0);\n      dst.push(0, 0);\n      dst.push(0, 0);\n    },\n    addUVLine: function (a) {\n      const src = this.uvs;\n      const dst = this.object.geometry.uvs;\n      dst.push(src[a + 0], src[a + 1]);\n    },\n    addFace: function (a, b, c, ua, ub, uc, na, nb, nc) {\n      const vLen = this.vertices.length;\n      let ia = this.parseVertexIndex(a, vLen);\n      let ib = this.parseVertexIndex(b, vLen);\n      let ic = this.parseVertexIndex(c, vLen);\n      this.addVertex(ia, ib, ic);\n      this.addColor(ia, ib, ic);\n\n      // normals\n\n      if (na !== undefined && na !== '') {\n        const nLen = this.normals.length;\n        ia = this.parseNormalIndex(na, nLen);\n        ib = this.parseNormalIndex(nb, nLen);\n        ic = this.parseNormalIndex(nc, nLen);\n        this.addNormal(ia, ib, ic);\n      } else {\n        this.addFaceNormal(ia, ib, ic);\n      }\n\n      // uvs\n\n      if (ua !== undefined && ua !== '') {\n        const uvLen = this.uvs.length;\n        ia = this.parseUVIndex(ua, uvLen);\n        ib = this.parseUVIndex(ub, uvLen);\n        ic = this.parseUVIndex(uc, uvLen);\n        this.addUV(ia, ib, ic);\n        this.object.geometry.hasUVIndices = true;\n      } else {\n        // add placeholder values (for inconsistent face definitions)\n\n        this.addDefaultUV();\n      }\n    },\n    addPointGeometry: function (vertices) {\n      this.object.geometry.type = 'Points';\n      const vLen = this.vertices.length;\n      for (let vi = 0, l = vertices.length; vi < l; vi++) {\n        const index = this.parseVertexIndex(vertices[vi], vLen);\n        this.addVertexPoint(index);\n        this.addColor(index);\n      }\n    },\n    addLineGeometry: function (vertices, uvs) {\n      this.object.geometry.type = 'Line';\n      const vLen = this.vertices.length;\n      const uvLen = this.uvs.length;\n      for (let vi = 0, l = vertices.length; vi < l; vi++) {\n        this.addVertexLine(this.parseVertexIndex(vertices[vi], vLen));\n      }\n      for (let uvi = 0, l = uvs.length; uvi < l; uvi++) {\n        this.addUVLine(this.parseUVIndex(uvs[uvi], uvLen));\n      }\n    }\n  };\n  state.startObject('', false);\n  return state;\n}\n\n/**\n * A loader for the OBJ format.\n *\n * The [OBJ format]{@link https://en.wikipedia.org/wiki/Wavefront_.obj_file} is a simple data-format that\n * represents 3D geometry in a human readable format as the position of each vertex, the UV position of\n * each texture coordinate vertex, vertex normals, and the faces that make each polygon defined as a list\n * of vertices, and texture vertices.\n *\n * ```js\n * const loader = new OBJLoader();\n * const object = await loader.loadAsync( 'models/monster.obj' );\n * scene.add( object );\n * ```\n *\n * @augments Loader\n */\nclass OBJLoader extends Loader {\n  /**\n   * Constructs a new OBJ loader.\n   *\n   * @param {LoadingManager} [manager] - The loading manager.\n   */\n  constructor(manager) {\n    super(manager);\n\n    /**\n     * A reference to a material creator.\n     *\n     * @type {?MaterialCreator}\n     * @default null\n     */\n    this.materials = null;\n  }\n\n  /**\n   * Starts loading from the given URL and passes the loaded OBJ asset\n   * to the `onLoad()` callback.\n   *\n   * @param {string} url - The path/URL of the file to be loaded. This can also be a data URI.\n   * @param {function(Group)} onLoad - Executed when the loading process has been finished.\n   * @param {onProgressCallback} onProgress - Executed while the loading is in progress.\n   * @param {onErrorCallback} onError - Executed when errors occur.\n   */\n  load(url, onLoad, onProgress, onError) {\n    const scope = this;\n    const loader = new FileLoader(this.manager);\n    loader.setPath(this.path);\n    loader.setRequestHeader(this.requestHeader);\n    loader.setWithCredentials(this.withCredentials);\n    loader.load(url, function (text) {\n      try {\n        onLoad(scope.parse(text));\n      } catch (e) {\n        if (onError) {\n          onError(e);\n        } else {\n          console.error(e);\n        }\n        scope.manager.itemError(url);\n      }\n    }, onProgress, onError);\n  }\n\n  /**\n   * Sets the material creator for this OBJ. This object is loaded via {@link MTLLoader}.\n   *\n   * @param {MaterialCreator} materials - An object that creates the materials for this OBJ.\n   * @return {OBJLoader} A reference to this loader.\n   */\n  setMaterials(materials) {\n    this.materials = materials;\n    return this;\n  }\n\n  /**\n   * Parses the given OBJ data and returns the resulting group.\n   *\n   * @param {string} text - The raw OBJ data as a string.\n   * @return {Group} The parsed OBJ.\n   */\n  parse(text) {\n    const state = new ParserState();\n    if (text.indexOf('\\r\\n') !== -1) {\n      // This is faster than String.split with regex that splits on both\n      text = text.replace(/\\r\\n/g, '\\n');\n    }\n    if (text.indexOf('\\\\\\n') !== -1) {\n      // join lines separated by a line continuation character (\\)\n      text = text.replace(/\\\\\\n/g, '');\n    }\n    const lines = text.split('\\n');\n    let result = [];\n    for (let i = 0, l = lines.length; i < l; i++) {\n      const line = lines[i].trimStart();\n      if (line.length === 0) continue;\n      const lineFirstChar = line.charAt(0);\n\n      // @todo invoke passed in handler if any\n      if (lineFirstChar === '#') continue; // skip comments\n\n      if (lineFirstChar === 'v') {\n        const data = line.split(_face_vertex_data_separator_pattern);\n        switch (data[0]) {\n          case 'v':\n            state.vertices.push(parseFloat(data[1]), parseFloat(data[2]), parseFloat(data[3]));\n            if (data.length >= 7) {\n              _color.setRGB(parseFloat(data[4]), parseFloat(data[5]), parseFloat(data[6]), SRGBColorSpace);\n              state.colors.push(_color.r, _color.g, _color.b);\n            } else {\n              // if no colors are defined, add placeholders so color and vertex indices match\n\n              state.colors.push(undefined, undefined, undefined);\n            }\n            break;\n          case 'vn':\n            state.normals.push(parseFloat(data[1]), parseFloat(data[2]), parseFloat(data[3]));\n            break;\n          case 'vt':\n            state.uvs.push(parseFloat(data[1]), parseFloat(data[2]));\n            break;\n        }\n      } else if (lineFirstChar === 'f') {\n        const lineData = line.slice(1).trim();\n        const vertexData = lineData.split(_face_vertex_data_separator_pattern);\n        const faceVertices = [];\n\n        // Parse the face vertex data into an easy to work with format\n\n        for (let j = 0, jl = vertexData.length; j < jl; j++) {\n          const vertex = vertexData[j];\n          if (vertex.length > 0) {\n            const vertexParts = vertex.split('/');\n            faceVertices.push(vertexParts);\n          }\n        }\n\n        // Draw an edge between the first vertex and all subsequent vertices to form an n-gon\n\n        const v1 = faceVertices[0];\n        for (let j = 1, jl = faceVertices.length - 1; j < jl; j++) {\n          const v2 = faceVertices[j];\n          const v3 = faceVertices[j + 1];\n          state.addFace(v1[0], v2[0], v3[0], v1[1], v2[1], v3[1], v1[2], v2[2], v3[2]);\n        }\n      } else if (lineFirstChar === 'l') {\n        const lineParts = line.substring(1).trim().split(' ');\n        let lineVertices = [];\n        const lineUVs = [];\n        if (line.indexOf('/') === -1) {\n          lineVertices = lineParts;\n        } else {\n          for (let li = 0, llen = lineParts.length; li < llen; li++) {\n            const parts = lineParts[li].split('/');\n            if (parts[0] !== '') lineVertices.push(parts[0]);\n            if (parts[1] !== '') lineUVs.push(parts[1]);\n          }\n        }\n        state.addLineGeometry(lineVertices, lineUVs);\n      } else if (lineFirstChar === 'p') {\n        const lineData = line.slice(1).trim();\n        const pointData = lineData.split(' ');\n        state.addPointGeometry(pointData);\n      } else if ((result = _object_pattern.exec(line)) !== null) {\n        // o object_name\n        // or\n        // g group_name\n\n        // WORKAROUND: https://bugs.chromium.org/p/v8/issues/detail?id=2869\n        // let name = result[ 0 ].slice( 1 ).trim();\n        const name = (' ' + result[0].slice(1).trim()).slice(1);\n        state.startObject(name);\n      } else if (_material_use_pattern.test(line)) {\n        // material\n\n        state.object.startMaterial(line.substring(7).trim(), state.materialLibraries);\n      } else if (_material_library_pattern.test(line)) {\n        // mtl file\n\n        state.materialLibraries.push(line.substring(7).trim());\n      } else if (_map_use_pattern.test(line)) {\n        // the line is parsed but ignored since the loader assumes textures are defined MTL files\n        // (according to https://www.okino.com/conv/imp_wave.htm, 'usemap' is the old-style Wavefront texture reference method)\n\n        console.warn('THREE.OBJLoader: Rendering identifier \"usemap\" not supported. Textures must be defined in MTL files.');\n      } else if (lineFirstChar === 's') {\n        result = line.split(' ');\n\n        // smooth shading\n\n        // @todo Handle files that have varying smooth values for a set of faces inside one geometry,\n        // but does not define a usemtl for each face set.\n        // This should be detected and a dummy material created (later MultiMaterial and geometry groups).\n        // This requires some care to not create extra material on each smooth value for \"normal\" obj files.\n        // where explicit usemtl defines geometry groups.\n        // Example asset: examples/models/obj/cerberus/Cerberus.obj\n\n        /*\n        \t * http://paulbourke.net/dataformats/obj/\n        \t *\n        \t * From chapter \"Grouping\" Syntax explanation \"s group_number\":\n        \t * \"group_number is the smoothing group number. To turn off smoothing groups, use a value of 0 or off.\n        \t * Polygonal elements use group numbers to put elements in different smoothing groups. For free-form\n        \t * surfaces, smoothing groups are either turned on or off; there is no difference between values greater\n        \t * than 0.\"\n        \t */\n        if (result.length > 1) {\n          const value = result[1].trim().toLowerCase();\n          state.object.smooth = value !== '0' && value !== 'off';\n        } else {\n          // ZBrush can produce \"s\" lines #11707\n          state.object.smooth = true;\n        }\n        const material = state.object.currentMaterial();\n        if (material) material.smooth = state.object.smooth;\n      } else {\n        // Handle null terminated files without exception\n        if (line === '\\0') continue;\n        console.warn('THREE.OBJLoader: Unexpected line: \"' + line + '\"');\n      }\n    }\n    state.finalize();\n    const container = new Group();\n    container.materialLibraries = [].concat(state.materialLibraries);\n    const hasPrimitives = !(state.objects.length === 1 && state.objects[0].geometry.vertices.length === 0);\n    if (hasPrimitives === true) {\n      for (let i = 0, l = state.objects.length; i < l; i++) {\n        const object = state.objects[i];\n        const geometry = object.geometry;\n        const materials = object.materials;\n        const isLine = geometry.type === 'Line';\n        const isPoints = geometry.type === 'Points';\n        let hasVertexColors = false;\n\n        // Skip o/g line declarations that did not follow with any faces\n        if (geometry.vertices.length === 0) continue;\n        const buffergeometry = new BufferGeometry();\n        buffergeometry.setAttribute('position', new Float32BufferAttribute(geometry.vertices, 3));\n        if (geometry.normals.length > 0) {\n          buffergeometry.setAttribute('normal', new Float32BufferAttribute(geometry.normals, 3));\n        }\n        if (geometry.colors.length > 0) {\n          hasVertexColors = true;\n          buffergeometry.setAttribute('color', new Float32BufferAttribute(geometry.colors, 3));\n        }\n        if (geometry.hasUVIndices === true) {\n          buffergeometry.setAttribute('uv', new Float32BufferAttribute(geometry.uvs, 2));\n        }\n\n        // Create materials\n\n        const createdMaterials = [];\n        for (let mi = 0, miLen = materials.length; mi < miLen; mi++) {\n          const sourceMaterial = materials[mi];\n          const materialHash = sourceMaterial.name + '_' + sourceMaterial.smooth + '_' + hasVertexColors;\n          let material = state.materials[materialHash];\n          if (this.materials !== null) {\n            material = this.materials.create(sourceMaterial.name);\n\n            // mtl etc. loaders probably can't create line materials correctly, copy properties to a line material.\n            if (isLine && material && !(material instanceof LineBasicMaterial)) {\n              const materialLine = new LineBasicMaterial();\n              Material.prototype.copy.call(materialLine, material);\n              materialLine.color.copy(material.color);\n              material = materialLine;\n            } else if (isPoints && material && !(material instanceof PointsMaterial)) {\n              const materialPoints = new PointsMaterial({\n                size: 10,\n                sizeAttenuation: false\n              });\n              Material.prototype.copy.call(materialPoints, material);\n              materialPoints.color.copy(material.color);\n              materialPoints.map = material.map;\n              material = materialPoints;\n            }\n          }\n          if (material === undefined) {\n            if (isLine) {\n              material = new LineBasicMaterial();\n            } else if (isPoints) {\n              material = new PointsMaterial({\n                size: 1,\n                sizeAttenuation: false\n              });\n            } else {\n              material = new MeshPhongMaterial();\n            }\n            material.name = sourceMaterial.name;\n            material.flatShading = sourceMaterial.smooth ? false : true;\n            material.vertexColors = hasVertexColors;\n            state.materials[materialHash] = material;\n          }\n          createdMaterials.push(material);\n        }\n\n        // Create mesh\n\n        let mesh;\n        if (createdMaterials.length > 1) {\n          for (let mi = 0, miLen = materials.length; mi < miLen; mi++) {\n            const sourceMaterial = materials[mi];\n            buffergeometry.addGroup(sourceMaterial.groupStart, sourceMaterial.groupCount, mi);\n          }\n          if (isLine) {\n            mesh = new LineSegments(buffergeometry, createdMaterials);\n          } else if (isPoints) {\n            mesh = new Points(buffergeometry, createdMaterials);\n          } else {\n            mesh = new Mesh(buffergeometry, createdMaterials);\n          }\n        } else {\n          if (isLine) {\n            mesh = new LineSegments(buffergeometry, createdMaterials[0]);\n          } else if (isPoints) {\n            mesh = new Points(buffergeometry, createdMaterials[0]);\n          } else {\n            mesh = new Mesh(buffergeometry, createdMaterials[0]);\n          }\n        }\n        mesh.name = object.name;\n        container.add(mesh);\n      }\n    } else {\n      // if there is only the default parser state object with no geometry data, interpret data as point cloud\n\n      if (state.vertices.length > 0) {\n        const material = new PointsMaterial({\n          size: 1,\n          sizeAttenuation: false\n        });\n        const buffergeometry = new BufferGeometry();\n        buffergeometry.setAttribute('position', new Float32BufferAttribute(state.vertices, 3));\n        if (state.colors.length > 0 && state.colors[0] !== undefined) {\n          buffergeometry.setAttribute('color', new Float32BufferAttribute(state.colors, 3));\n          material.vertexColors = true;\n        }\n        const points = new Points(buffergeometry, material);\n        container.add(points);\n      }\n    }\n    return container;\n  }\n}\nexport { OBJLoader };"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAGA,IAAM,kBAAkB;AAExB,IAAM,4BAA4B;AAElC,IAAM,wBAAwB;AAE9B,IAAM,mBAAmB;AACzB,IAAM,sCAAsC;AAC5C,IAAM,MAAM,IAAI,QAAQ;AACxB,IAAM,MAAM,IAAI,QAAQ;AACxB,IAAM,MAAM,IAAI,QAAQ;AACxB,IAAM,MAAM,IAAI,QAAQ;AACxB,IAAM,MAAM,IAAI,QAAQ;AACxB,IAAM,SAAS,IAAI,MAAM;AACzB,SAAS,cAAc;AACrB,QAAM,QAAQ;AAAA,IACZ,SAAS,CAAC;AAAA,IACV,QAAQ,CAAC;AAAA,IACT,UAAU,CAAC;AAAA,IACX,SAAS,CAAC;AAAA,IACV,QAAQ,CAAC;AAAA,IACT,KAAK,CAAC;AAAA,IACN,WAAW,CAAC;AAAA,IACZ,mBAAmB,CAAC;AAAA,IACpB,aAAa,SAAU,MAAM,iBAAiB;AAG5C,UAAI,KAAK,UAAU,KAAK,OAAO,oBAAoB,OAAO;AACxD,aAAK,OAAO,OAAO;AACnB,aAAK,OAAO,kBAAkB,oBAAoB;AAClD;AAAA,MACF;AACA,YAAM,mBAAmB,KAAK,UAAU,OAAO,KAAK,OAAO,oBAAoB,aAAa,KAAK,OAAO,gBAAgB,IAAI;AAC5H,UAAI,KAAK,UAAU,OAAO,KAAK,OAAO,cAAc,YAAY;AAC9D,aAAK,OAAO,UAAU,IAAI;AAAA,MAC5B;AACA,WAAK,SAAS;AAAA,QACZ,MAAM,QAAQ;AAAA,QACd,iBAAiB,oBAAoB;AAAA,QACrC,UAAU;AAAA,UACR,UAAU,CAAC;AAAA,UACX,SAAS,CAAC;AAAA,UACV,QAAQ,CAAC;AAAA,UACT,KAAK,CAAC;AAAA,UACN,cAAc;AAAA,QAChB;AAAA,QACA,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,eAAe,SAAUA,OAAM,WAAW;AACxC,gBAAM,WAAW,KAAK,UAAU,KAAK;AAIrC,cAAI,aAAa,SAAS,aAAa,SAAS,cAAc,IAAI;AAChE,iBAAK,UAAU,OAAO,SAAS,OAAO,CAAC;AAAA,UACzC;AACA,gBAAM,WAAW;AAAA,YACf,OAAO,KAAK,UAAU;AAAA,YACtB,MAAMA,SAAQ;AAAA,YACd,QAAQ,MAAM,QAAQ,SAAS,KAAK,UAAU,SAAS,IAAI,UAAU,UAAU,SAAS,CAAC,IAAI;AAAA,YAC7F,QAAQ,aAAa,SAAY,SAAS,SAAS,KAAK;AAAA,YACxD,YAAY,aAAa,SAAY,SAAS,WAAW;AAAA,YACzD,UAAU;AAAA,YACV,YAAY;AAAA,YACZ,WAAW;AAAA,YACX,OAAO,SAAU,OAAO;AACtB,oBAAM,SAAS;AAAA,gBACb,OAAO,OAAO,UAAU,WAAW,QAAQ,KAAK;AAAA,gBAChD,MAAM,KAAK;AAAA,gBACX,QAAQ,KAAK;AAAA,gBACb,QAAQ,KAAK;AAAA,gBACb,YAAY;AAAA,gBACZ,UAAU;AAAA,gBACV,YAAY;AAAA,gBACZ,WAAW;AAAA,cACb;AACA,qBAAO,QAAQ,KAAK,MAAM,KAAK,MAAM;AACrC,qBAAO;AAAA,YACT;AAAA,UACF;AACA,eAAK,UAAU,KAAK,QAAQ;AAC5B,iBAAO;AAAA,QACT;AAAA,QACA,iBAAiB,WAAY;AAC3B,cAAI,KAAK,UAAU,SAAS,GAAG;AAC7B,mBAAO,KAAK,UAAU,KAAK,UAAU,SAAS,CAAC;AAAA,UACjD;AACA,iBAAO;AAAA,QACT;AAAA,QACA,WAAW,SAAU,KAAK;AACxB,gBAAM,oBAAoB,KAAK,gBAAgB;AAC/C,cAAI,qBAAqB,kBAAkB,aAAa,IAAI;AAC1D,8BAAkB,WAAW,KAAK,SAAS,SAAS,SAAS;AAC7D,8BAAkB,aAAa,kBAAkB,WAAW,kBAAkB;AAC9E,8BAAkB,YAAY;AAAA,UAChC;AAGA,cAAI,OAAO,KAAK,UAAU,SAAS,GAAG;AACpC,qBAAS,KAAK,KAAK,UAAU,SAAS,GAAG,MAAM,GAAG,MAAM;AACtD,kBAAI,KAAK,UAAU,EAAE,EAAE,cAAc,GAAG;AACtC,qBAAK,UAAU,OAAO,IAAI,CAAC;AAAA,cAC7B;AAAA,YACF;AAAA,UACF;AAGA,cAAI,OAAO,KAAK,UAAU,WAAW,GAAG;AACtC,iBAAK,UAAU,KAAK;AAAA,cAClB,MAAM;AAAA,cACN,QAAQ,KAAK;AAAA,YACf,CAAC;AAAA,UACH;AACA,iBAAO;AAAA,QACT;AAAA,MACF;AAQA,UAAI,oBAAoB,iBAAiB,QAAQ,OAAO,iBAAiB,UAAU,YAAY;AAC7F,cAAM,WAAW,iBAAiB,MAAM,CAAC;AACzC,iBAAS,YAAY;AACrB,aAAK,OAAO,UAAU,KAAK,QAAQ;AAAA,MACrC;AACA,WAAK,QAAQ,KAAK,KAAK,MAAM;AAAA,IAC/B;AAAA,IACA,UAAU,WAAY;AACpB,UAAI,KAAK,UAAU,OAAO,KAAK,OAAO,cAAc,YAAY;AAC9D,aAAK,OAAO,UAAU,IAAI;AAAA,MAC5B;AAAA,IACF;AAAA,IACA,kBAAkB,SAAU,OAAO,KAAK;AACtC,YAAM,QAAQ,SAAS,OAAO,EAAE;AAChC,cAAQ,SAAS,IAAI,QAAQ,IAAI,QAAQ,MAAM,KAAK;AAAA,IACtD;AAAA,IACA,kBAAkB,SAAU,OAAO,KAAK;AACtC,YAAM,QAAQ,SAAS,OAAO,EAAE;AAChC,cAAQ,SAAS,IAAI,QAAQ,IAAI,QAAQ,MAAM,KAAK;AAAA,IACtD;AAAA,IACA,cAAc,SAAU,OAAO,KAAK;AAClC,YAAM,QAAQ,SAAS,OAAO,EAAE;AAChC,cAAQ,SAAS,IAAI,QAAQ,IAAI,QAAQ,MAAM,KAAK;AAAA,IACtD;AAAA,IACA,WAAW,SAAU,GAAG,GAAG,GAAG;AAC5B,YAAM,MAAM,KAAK;AACjB,YAAM,MAAM,KAAK,OAAO,SAAS;AACjC,UAAI,KAAK,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC;AAC3C,UAAI,KAAK,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC;AAC3C,UAAI,KAAK,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC;AAAA,IAC7C;AAAA,IACA,gBAAgB,SAAU,GAAG;AAC3B,YAAM,MAAM,KAAK;AACjB,YAAM,MAAM,KAAK,OAAO,SAAS;AACjC,UAAI,KAAK,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC;AAAA,IAC7C;AAAA,IACA,eAAe,SAAU,GAAG;AAC1B,YAAM,MAAM,KAAK;AACjB,YAAM,MAAM,KAAK,OAAO,SAAS;AACjC,UAAI,KAAK,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC;AAAA,IAC7C;AAAA,IACA,WAAW,SAAU,GAAG,GAAG,GAAG;AAC5B,YAAM,MAAM,KAAK;AACjB,YAAM,MAAM,KAAK,OAAO,SAAS;AACjC,UAAI,KAAK,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC;AAC3C,UAAI,KAAK,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC;AAC3C,UAAI,KAAK,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC;AAAA,IAC7C;AAAA,IACA,eAAe,SAAU,GAAG,GAAG,GAAG;AAChC,YAAM,MAAM,KAAK;AACjB,YAAM,MAAM,KAAK,OAAO,SAAS;AACjC,UAAI,UAAU,KAAK,CAAC;AACpB,UAAI,UAAU,KAAK,CAAC;AACpB,UAAI,UAAU,KAAK,CAAC;AACpB,UAAI,WAAW,KAAK,GAAG;AACvB,UAAI,WAAW,KAAK,GAAG;AACvB,UAAI,MAAM,GAAG;AACb,UAAI,UAAU;AACd,UAAI,KAAK,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC;AAC5B,UAAI,KAAK,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC;AAC5B,UAAI,KAAK,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC;AAAA,IAC9B;AAAA,IACA,UAAU,SAAU,GAAG,GAAG,GAAG;AAC3B,YAAM,MAAM,KAAK;AACjB,YAAM,MAAM,KAAK,OAAO,SAAS;AACjC,UAAI,IAAI,CAAC,MAAM,OAAW,KAAI,KAAK,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC;AACrE,UAAI,IAAI,CAAC,MAAM,OAAW,KAAI,KAAK,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC;AACrE,UAAI,IAAI,CAAC,MAAM,OAAW,KAAI,KAAK,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC;AAAA,IACvE;AAAA,IACA,OAAO,SAAU,GAAG,GAAG,GAAG;AACxB,YAAM,MAAM,KAAK;AACjB,YAAM,MAAM,KAAK,OAAO,SAAS;AACjC,UAAI,KAAK,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC;AAC/B,UAAI,KAAK,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC;AAC/B,UAAI,KAAK,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC;AAAA,IACjC;AAAA,IACA,cAAc,WAAY;AACxB,YAAM,MAAM,KAAK,OAAO,SAAS;AACjC,UAAI,KAAK,GAAG,CAAC;AACb,UAAI,KAAK,GAAG,CAAC;AACb,UAAI,KAAK,GAAG,CAAC;AAAA,IACf;AAAA,IACA,WAAW,SAAU,GAAG;AACtB,YAAM,MAAM,KAAK;AACjB,YAAM,MAAM,KAAK,OAAO,SAAS;AACjC,UAAI,KAAK,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC;AAAA,IACjC;AAAA,IACA,SAAS,SAAU,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AAClD,YAAM,OAAO,KAAK,SAAS;AAC3B,UAAI,KAAK,KAAK,iBAAiB,GAAG,IAAI;AACtC,UAAI,KAAK,KAAK,iBAAiB,GAAG,IAAI;AACtC,UAAI,KAAK,KAAK,iBAAiB,GAAG,IAAI;AACtC,WAAK,UAAU,IAAI,IAAI,EAAE;AACzB,WAAK,SAAS,IAAI,IAAI,EAAE;AAIxB,UAAI,OAAO,UAAa,OAAO,IAAI;AACjC,cAAM,OAAO,KAAK,QAAQ;AAC1B,aAAK,KAAK,iBAAiB,IAAI,IAAI;AACnC,aAAK,KAAK,iBAAiB,IAAI,IAAI;AACnC,aAAK,KAAK,iBAAiB,IAAI,IAAI;AACnC,aAAK,UAAU,IAAI,IAAI,EAAE;AAAA,MAC3B,OAAO;AACL,aAAK,cAAc,IAAI,IAAI,EAAE;AAAA,MAC/B;AAIA,UAAI,OAAO,UAAa,OAAO,IAAI;AACjC,cAAM,QAAQ,KAAK,IAAI;AACvB,aAAK,KAAK,aAAa,IAAI,KAAK;AAChC,aAAK,KAAK,aAAa,IAAI,KAAK;AAChC,aAAK,KAAK,aAAa,IAAI,KAAK;AAChC,aAAK,MAAM,IAAI,IAAI,EAAE;AACrB,aAAK,OAAO,SAAS,eAAe;AAAA,MACtC,OAAO;AAGL,aAAK,aAAa;AAAA,MACpB;AAAA,IACF;AAAA,IACA,kBAAkB,SAAU,UAAU;AACpC,WAAK,OAAO,SAAS,OAAO;AAC5B,YAAM,OAAO,KAAK,SAAS;AAC3B,eAAS,KAAK,GAAG,IAAI,SAAS,QAAQ,KAAK,GAAG,MAAM;AAClD,cAAM,QAAQ,KAAK,iBAAiB,SAAS,EAAE,GAAG,IAAI;AACtD,aAAK,eAAe,KAAK;AACzB,aAAK,SAAS,KAAK;AAAA,MACrB;AAAA,IACF;AAAA,IACA,iBAAiB,SAAU,UAAU,KAAK;AACxC,WAAK,OAAO,SAAS,OAAO;AAC5B,YAAM,OAAO,KAAK,SAAS;AAC3B,YAAM,QAAQ,KAAK,IAAI;AACvB,eAAS,KAAK,GAAG,IAAI,SAAS,QAAQ,KAAK,GAAG,MAAM;AAClD,aAAK,cAAc,KAAK,iBAAiB,SAAS,EAAE,GAAG,IAAI,CAAC;AAAA,MAC9D;AACA,eAAS,MAAM,GAAG,IAAI,IAAI,QAAQ,MAAM,GAAG,OAAO;AAChD,aAAK,UAAU,KAAK,aAAa,IAAI,GAAG,GAAG,KAAK,CAAC;AAAA,MACnD;AAAA,IACF;AAAA,EACF;AACA,QAAM,YAAY,IAAI,KAAK;AAC3B,SAAO;AACT;AAkBA,IAAM,YAAN,cAAwB,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM7B,YAAY,SAAS;AACnB,UAAM,OAAO;AAQb,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,KAAK,KAAK,QAAQ,YAAY,SAAS;AACrC,UAAM,QAAQ;AACd,UAAM,SAAS,IAAI,WAAW,KAAK,OAAO;AAC1C,WAAO,QAAQ,KAAK,IAAI;AACxB,WAAO,iBAAiB,KAAK,aAAa;AAC1C,WAAO,mBAAmB,KAAK,eAAe;AAC9C,WAAO,KAAK,KAAK,SAAU,MAAM;AAC/B,UAAI;AACF,eAAO,MAAM,MAAM,IAAI,CAAC;AAAA,MAC1B,SAAS,GAAG;AACV,YAAI,SAAS;AACX,kBAAQ,CAAC;AAAA,QACX,OAAO;AACL,kBAAQ,MAAM,CAAC;AAAA,QACjB;AACA,cAAM,QAAQ,UAAU,GAAG;AAAA,MAC7B;AAAA,IACF,GAAG,YAAY,OAAO;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,aAAa,WAAW;AACtB,SAAK,YAAY;AACjB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,MAAM;AACV,UAAM,QAAQ,IAAI,YAAY;AAC9B,QAAI,KAAK,QAAQ,MAAM,MAAM,IAAI;AAE/B,aAAO,KAAK,QAAQ,SAAS,IAAI;AAAA,IACnC;AACA,QAAI,KAAK,QAAQ,MAAM,MAAM,IAAI;AAE/B,aAAO,KAAK,QAAQ,SAAS,EAAE;AAAA,IACjC;AACA,UAAM,QAAQ,KAAK,MAAM,IAAI;AAC7B,QAAI,SAAS,CAAC;AACd,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAI,GAAG,KAAK;AAC5C,YAAM,OAAO,MAAM,CAAC,EAAE,UAAU;AAChC,UAAI,KAAK,WAAW,EAAG;AACvB,YAAM,gBAAgB,KAAK,OAAO,CAAC;AAGnC,UAAI,kBAAkB,IAAK;AAE3B,UAAI,kBAAkB,KAAK;AACzB,cAAM,OAAO,KAAK,MAAM,mCAAmC;AAC3D,gBAAQ,KAAK,CAAC,GAAG;AAAA,UACf,KAAK;AACH,kBAAM,SAAS,KAAK,WAAW,KAAK,CAAC,CAAC,GAAG,WAAW,KAAK,CAAC,CAAC,GAAG,WAAW,KAAK,CAAC,CAAC,CAAC;AACjF,gBAAI,KAAK,UAAU,GAAG;AACpB,qBAAO,OAAO,WAAW,KAAK,CAAC,CAAC,GAAG,WAAW,KAAK,CAAC,CAAC,GAAG,WAAW,KAAK,CAAC,CAAC,GAAG,cAAc;AAC3F,oBAAM,OAAO,KAAK,OAAO,GAAG,OAAO,GAAG,OAAO,CAAC;AAAA,YAChD,OAAO;AAGL,oBAAM,OAAO,KAAK,QAAW,QAAW,MAAS;AAAA,YACnD;AACA;AAAA,UACF,KAAK;AACH,kBAAM,QAAQ,KAAK,WAAW,KAAK,CAAC,CAAC,GAAG,WAAW,KAAK,CAAC,CAAC,GAAG,WAAW,KAAK,CAAC,CAAC,CAAC;AAChF;AAAA,UACF,KAAK;AACH,kBAAM,IAAI,KAAK,WAAW,KAAK,CAAC,CAAC,GAAG,WAAW,KAAK,CAAC,CAAC,CAAC;AACvD;AAAA,QACJ;AAAA,MACF,WAAW,kBAAkB,KAAK;AAChC,cAAM,WAAW,KAAK,MAAM,CAAC,EAAE,KAAK;AACpC,cAAM,aAAa,SAAS,MAAM,mCAAmC;AACrE,cAAM,eAAe,CAAC;AAItB,iBAAS,IAAI,GAAG,KAAK,WAAW,QAAQ,IAAI,IAAI,KAAK;AACnD,gBAAM,SAAS,WAAW,CAAC;AAC3B,cAAI,OAAO,SAAS,GAAG;AACrB,kBAAM,cAAc,OAAO,MAAM,GAAG;AACpC,yBAAa,KAAK,WAAW;AAAA,UAC/B;AAAA,QACF;AAIA,cAAM,KAAK,aAAa,CAAC;AACzB,iBAAS,IAAI,GAAG,KAAK,aAAa,SAAS,GAAG,IAAI,IAAI,KAAK;AACzD,gBAAM,KAAK,aAAa,CAAC;AACzB,gBAAM,KAAK,aAAa,IAAI,CAAC;AAC7B,gBAAM,QAAQ,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;AAAA,QAC7E;AAAA,MACF,WAAW,kBAAkB,KAAK;AAChC,cAAM,YAAY,KAAK,UAAU,CAAC,EAAE,KAAK,EAAE,MAAM,GAAG;AACpD,YAAI,eAAe,CAAC;AACpB,cAAM,UAAU,CAAC;AACjB,YAAI,KAAK,QAAQ,GAAG,MAAM,IAAI;AAC5B,yBAAe;AAAA,QACjB,OAAO;AACL,mBAAS,KAAK,GAAG,OAAO,UAAU,QAAQ,KAAK,MAAM,MAAM;AACzD,kBAAM,QAAQ,UAAU,EAAE,EAAE,MAAM,GAAG;AACrC,gBAAI,MAAM,CAAC,MAAM,GAAI,cAAa,KAAK,MAAM,CAAC,CAAC;AAC/C,gBAAI,MAAM,CAAC,MAAM,GAAI,SAAQ,KAAK,MAAM,CAAC,CAAC;AAAA,UAC5C;AAAA,QACF;AACA,cAAM,gBAAgB,cAAc,OAAO;AAAA,MAC7C,WAAW,kBAAkB,KAAK;AAChC,cAAM,WAAW,KAAK,MAAM,CAAC,EAAE,KAAK;AACpC,cAAM,YAAY,SAAS,MAAM,GAAG;AACpC,cAAM,iBAAiB,SAAS;AAAA,MAClC,YAAY,SAAS,gBAAgB,KAAK,IAAI,OAAO,MAAM;AAOzD,cAAM,QAAQ,MAAM,OAAO,CAAC,EAAE,MAAM,CAAC,EAAE,KAAK,GAAG,MAAM,CAAC;AACtD,cAAM,YAAY,IAAI;AAAA,MACxB,WAAW,sBAAsB,KAAK,IAAI,GAAG;AAG3C,cAAM,OAAO,cAAc,KAAK,UAAU,CAAC,EAAE,KAAK,GAAG,MAAM,iBAAiB;AAAA,MAC9E,WAAW,0BAA0B,KAAK,IAAI,GAAG;AAG/C,cAAM,kBAAkB,KAAK,KAAK,UAAU,CAAC,EAAE,KAAK,CAAC;AAAA,MACvD,WAAW,iBAAiB,KAAK,IAAI,GAAG;AAItC,gBAAQ,KAAK,sGAAsG;AAAA,MACrH,WAAW,kBAAkB,KAAK;AAChC,iBAAS,KAAK,MAAM,GAAG;AAoBvB,YAAI,OAAO,SAAS,GAAG;AACrB,gBAAM,QAAQ,OAAO,CAAC,EAAE,KAAK,EAAE,YAAY;AAC3C,gBAAM,OAAO,SAAS,UAAU,OAAO,UAAU;AAAA,QACnD,OAAO;AAEL,gBAAM,OAAO,SAAS;AAAA,QACxB;AACA,cAAM,WAAW,MAAM,OAAO,gBAAgB;AAC9C,YAAI,SAAU,UAAS,SAAS,MAAM,OAAO;AAAA,MAC/C,OAAO;AAEL,YAAI,SAAS,KAAM;AACnB,gBAAQ,KAAK,wCAAwC,OAAO,GAAG;AAAA,MACjE;AAAA,IACF;AACA,UAAM,SAAS;AACf,UAAM,YAAY,IAAI,MAAM;AAC5B,cAAU,oBAAoB,CAAC,EAAE,OAAO,MAAM,iBAAiB;AAC/D,UAAM,gBAAgB,EAAE,MAAM,QAAQ,WAAW,KAAK,MAAM,QAAQ,CAAC,EAAE,SAAS,SAAS,WAAW;AACpG,QAAI,kBAAkB,MAAM;AAC1B,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,QAAQ,IAAI,GAAG,KAAK;AACpD,cAAM,SAAS,MAAM,QAAQ,CAAC;AAC9B,cAAM,WAAW,OAAO;AACxB,cAAM,YAAY,OAAO;AACzB,cAAM,SAAS,SAAS,SAAS;AACjC,cAAM,WAAW,SAAS,SAAS;AACnC,YAAI,kBAAkB;AAGtB,YAAI,SAAS,SAAS,WAAW,EAAG;AACpC,cAAM,iBAAiB,IAAI,eAAe;AAC1C,uBAAe,aAAa,YAAY,IAAI,uBAAuB,SAAS,UAAU,CAAC,CAAC;AACxF,YAAI,SAAS,QAAQ,SAAS,GAAG;AAC/B,yBAAe,aAAa,UAAU,IAAI,uBAAuB,SAAS,SAAS,CAAC,CAAC;AAAA,QACvF;AACA,YAAI,SAAS,OAAO,SAAS,GAAG;AAC9B,4BAAkB;AAClB,yBAAe,aAAa,SAAS,IAAI,uBAAuB,SAAS,QAAQ,CAAC,CAAC;AAAA,QACrF;AACA,YAAI,SAAS,iBAAiB,MAAM;AAClC,yBAAe,aAAa,MAAM,IAAI,uBAAuB,SAAS,KAAK,CAAC,CAAC;AAAA,QAC/E;AAIA,cAAM,mBAAmB,CAAC;AAC1B,iBAAS,KAAK,GAAG,QAAQ,UAAU,QAAQ,KAAK,OAAO,MAAM;AAC3D,gBAAM,iBAAiB,UAAU,EAAE;AACnC,gBAAM,eAAe,eAAe,OAAO,MAAM,eAAe,SAAS,MAAM;AAC/E,cAAI,WAAW,MAAM,UAAU,YAAY;AAC3C,cAAI,KAAK,cAAc,MAAM;AAC3B,uBAAW,KAAK,UAAU,OAAO,eAAe,IAAI;AAGpD,gBAAI,UAAU,YAAY,EAAE,oBAAoB,oBAAoB;AAClE,oBAAM,eAAe,IAAI,kBAAkB;AAC3C,uBAAS,UAAU,KAAK,KAAK,cAAc,QAAQ;AACnD,2BAAa,MAAM,KAAK,SAAS,KAAK;AACtC,yBAAW;AAAA,YACb,WAAW,YAAY,YAAY,EAAE,oBAAoB,iBAAiB;AACxE,oBAAM,iBAAiB,IAAI,eAAe;AAAA,gBACxC,MAAM;AAAA,gBACN,iBAAiB;AAAA,cACnB,CAAC;AACD,uBAAS,UAAU,KAAK,KAAK,gBAAgB,QAAQ;AACrD,6BAAe,MAAM,KAAK,SAAS,KAAK;AACxC,6BAAe,MAAM,SAAS;AAC9B,yBAAW;AAAA,YACb;AAAA,UACF;AACA,cAAI,aAAa,QAAW;AAC1B,gBAAI,QAAQ;AACV,yBAAW,IAAI,kBAAkB;AAAA,YACnC,WAAW,UAAU;AACnB,yBAAW,IAAI,eAAe;AAAA,gBAC5B,MAAM;AAAA,gBACN,iBAAiB;AAAA,cACnB,CAAC;AAAA,YACH,OAAO;AACL,yBAAW,IAAI,kBAAkB;AAAA,YACnC;AACA,qBAAS,OAAO,eAAe;AAC/B,qBAAS,cAAc,eAAe,SAAS,QAAQ;AACvD,qBAAS,eAAe;AACxB,kBAAM,UAAU,YAAY,IAAI;AAAA,UAClC;AACA,2BAAiB,KAAK,QAAQ;AAAA,QAChC;AAIA,YAAI;AACJ,YAAI,iBAAiB,SAAS,GAAG;AAC/B,mBAAS,KAAK,GAAG,QAAQ,UAAU,QAAQ,KAAK,OAAO,MAAM;AAC3D,kBAAM,iBAAiB,UAAU,EAAE;AACnC,2BAAe,SAAS,eAAe,YAAY,eAAe,YAAY,EAAE;AAAA,UAClF;AACA,cAAI,QAAQ;AACV,mBAAO,IAAI,aAAa,gBAAgB,gBAAgB;AAAA,UAC1D,WAAW,UAAU;AACnB,mBAAO,IAAI,OAAO,gBAAgB,gBAAgB;AAAA,UACpD,OAAO;AACL,mBAAO,IAAI,KAAK,gBAAgB,gBAAgB;AAAA,UAClD;AAAA,QACF,OAAO;AACL,cAAI,QAAQ;AACV,mBAAO,IAAI,aAAa,gBAAgB,iBAAiB,CAAC,CAAC;AAAA,UAC7D,WAAW,UAAU;AACnB,mBAAO,IAAI,OAAO,gBAAgB,iBAAiB,CAAC,CAAC;AAAA,UACvD,OAAO;AACL,mBAAO,IAAI,KAAK,gBAAgB,iBAAiB,CAAC,CAAC;AAAA,UACrD;AAAA,QACF;AACA,aAAK,OAAO,OAAO;AACnB,kBAAU,IAAI,IAAI;AAAA,MACpB;AAAA,IACF,OAAO;AAGL,UAAI,MAAM,SAAS,SAAS,GAAG;AAC7B,cAAM,WAAW,IAAI,eAAe;AAAA,UAClC,MAAM;AAAA,UACN,iBAAiB;AAAA,QACnB,CAAC;AACD,cAAM,iBAAiB,IAAI,eAAe;AAC1C,uBAAe,aAAa,YAAY,IAAI,uBAAuB,MAAM,UAAU,CAAC,CAAC;AACrF,YAAI,MAAM,OAAO,SAAS,KAAK,MAAM,OAAO,CAAC,MAAM,QAAW;AAC5D,yBAAe,aAAa,SAAS,IAAI,uBAAuB,MAAM,QAAQ,CAAC,CAAC;AAChF,mBAAS,eAAe;AAAA,QAC1B;AACA,cAAM,SAAS,IAAI,OAAO,gBAAgB,QAAQ;AAClD,kBAAU,IAAI,MAAM;AAAA,MACtB;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACF;", "names": ["name"]}