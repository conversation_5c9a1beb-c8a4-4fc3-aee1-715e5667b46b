// Compatibility layer - Re-export CSM service as Tripo service for backward compatibility
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { CsmCreateSessionResponse, CsmService, CsmSessionResponse } from './csm.service';

// Re-export types for backward compatibility
export type TripoCreateTaskResponse = CsmCreateSessionResponse;
export type TripoTaskResponse = CsmSessionResponse;
export type TripoUploadResponse = any; // Not used in CSM.ai API

// Service wrapper for backward compatibility
@Injectable({
  providedIn: 'root'
})
export class TripoService {
  constructor(private csmService: CsmService) {}

  // Delegate methods to CSM service
  uploadAndCreateTask(imageFile: File): Observable<TripoCreateTaskResponse> {
    return this.csmService.uploadAndCreateTask(imageFile);
  }

  getTaskStatus(taskId: string): Observable<TripoTaskResponse> {
    return this.csmService.getSessionStatus(taskId);
  }

  pollTaskUntilComplete(taskId: string): Observable<TripoTaskResponse> {
    return this.csmService.pollTaskUntilComplete(taskId);
  }

  pollSessionUntilComplete(sessionId: string): Observable<TripoTaskResponse> {
    return this.csmService.pollSessionUntilComplete(sessionId);
  }
}
