import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';

@Injectable({ providedIn: 'root' })
export class NatureTiersService {
  private baseUrl = 'http://localhost:8080/api/natures';

  constructor(private http: HttpClient) {}

  getAll(): Observable<any[]> {
    return this.http.get<any[]>(this.baseUrl);
  }

  create(data: any): Observable<any> {
    return this.http.post(this.baseUrl, data);
  }

  update(code: string, data: any): Observable<any> {
    return this.http.put(`${this.baseUrl}/${code}`, data);
  }

  delete(code: string): Observable<any> {
    return this.http.delete(`${this.baseUrl}/${code}`);
  }
}
