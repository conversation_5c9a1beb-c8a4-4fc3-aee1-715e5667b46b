import { Component } from '@angular/core';

@Component({
  selector: 'app-tiers',
  templateUrl: './tiers.component.html',
  styleUrls: ['./tiers.component.css']
})
export class TiersComponent {
  isPrejudiceProfessionnel: boolean = false;
  isExpanded1: boolean = false;
  isExpanded2: boolean = false;
  isExpanded3: boolean = false;
  isExpanded4: boolean = false;

  togglePrejudiceProfessionnel() {
    this.isPrejudiceProfessionnel = !this.isPrejudiceProfessionnel;
  }

  toggleExpand(formNumber: number) {
    switch(formNumber) {
      case 1:
        this.isExpanded1 = !this.isExpanded1;
        break;
      case 2:
        this.isExpanded2 = !this.isExpanded2;
        break;
      case 3:
        this.isExpanded3 = !this.isExpanded3;
        break;
      case 4:
        this.isExpanded4 = !this.isExpanded4;
        break;
    }
  }
}
