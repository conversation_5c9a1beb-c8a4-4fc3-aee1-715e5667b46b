package com.fed.platform.sinister.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.fed.platform.sinister.domain.Evenement;

@Repository
public interface EvenementRepository extends JpaRepository<Evenement, Long> {

    /**
     * Find evenements by code and type_de_degat
     */
    List<Evenement> findByCodeAndTypeDegat(String code, String typeDegat);

    /**
     * Find evenements by code only
     */
    List<Evenement> findByCode(String code);
}
