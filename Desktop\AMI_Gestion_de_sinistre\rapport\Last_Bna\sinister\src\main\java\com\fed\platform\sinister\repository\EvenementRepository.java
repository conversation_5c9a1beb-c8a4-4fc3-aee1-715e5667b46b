package com.fed.platform.sinister.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.fed.platform.sinister.domain.Evenement;

@Repository
public interface EvenementRepository extends JpaRepository<Evenement, Long> {

    /**
     * Find evenements by code and type_de_degat using custom query
     */
    @Query("SELECT e FROM Evenement e WHERE e.code = :code AND e.type_de_degat = :typeDeDegat")
    List<Evenement> findByCodeAndTypeDeDegat(@Param("code") String code, @Param("typeDeDegat") String typeDeDegat);

    /**
     * Find evenements by code only
     */
    List<Evenement> findByCode(String code);
}
