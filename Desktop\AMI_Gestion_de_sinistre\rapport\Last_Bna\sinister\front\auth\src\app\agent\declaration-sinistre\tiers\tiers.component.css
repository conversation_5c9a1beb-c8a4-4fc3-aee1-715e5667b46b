body {
  background-color: #fbfafa;
  margin: 0;
  padding: 0;
  width: 100%;
  min-height: 110vh;

  overflow-x: hidden;
}


.container {
  display: flex;
  flex-direction: column;
  width: 100%;
  min-height: 140vh;
  overflow-y: hidden;
  overflow-x: hidden;
}

/* Top navbar styles */
.top-navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background-color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.logo-container {
  display: flex;
  align-items: center;
}

.logo {
  height: 40px;
  margin-right: 1rem;
}

/* Bottom bar styles */
.bottom-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 30px;
  background: #1A3E8D;
  margin-bottom: 20px;
  position: relative;
  height: 60px;
  border-bottom-right-radius: 48px;
}

.bottom-bar-title {
  font-family: 'Gilmer Bold', sans-serif;
  font-size: 23px;
  color: white;
  position: relative;
  left: 250px;
  bottom: 10px;
}

.svg {
  position: relative;
  right: 980px;
  top: 15px;
  max-width: 100%;
  height: auto;
  display: inline-block;
}

/* Main title styles */
.main-title {
  font-family: 'Gilmer Bold', sans-serif;
  font-size: 24px;
  color: #000000;
  margin: 30px 0 20px 50px;
  position: relative;
  left: 280px;
  bottom: 15px;
}

/* Progress steps styles */
.progress-steps {
  width: 250px;
  margin-right: 10px;
  position: relative;
  left: 50px;
  display: block;
  bottom: 40px;
}

.step {
  position: relative;
  margin-bottom: 30px;
  padding-left: 40px;
}

.step-number {
  position: absolute;
  left: 0;
  top: 0;
  width: 24px;
  height: 24px;
  background-color: #B9C3DE;
  border-radius: 50%;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: 'Gilmer Bold', sans-serif;
  font-size: 16px;
}

.step-number svg {
  width: 24px;
  height: 24px;
  position: absolute;
  top: 0;
  left: 0;
}

.step.active .step-number {
  background-color: transparent;
}

.step.active .step-number {
  background-color: #1C3F93;
}

.step-title {
  font-family: 'Gilmer Bold', sans-serif;
  font-size: 16px;
  color: #B9C3DE;
  margin-bottom: 5px;
}

.step.active .step-title.active {
  color: #1C3F93;
}

/* Form container styles */
.form-container {
  display: flex;
  max-width: 2000px;
  margin: 1px;
  padding: 30px;
}

.form-content, .form-content2, .form-content3, .form-content4 {
  width: 900px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: relative;
  left: 280px;
  bottom: 400px;
  padding: 30px;
  height: 120px;
  overflow: hidden;
  transition: height 0.3s ease-in-out;
}

.form-content.expanded {
  height: 754px;
}

.form-content2.expanded {
  height: 1000px;
}

.form-content3.expanded {
  height: 800px;
}

.form-content4.expanded {
  height: 400px;
}

.form-content .form-group,
.form-content2 .form-group,
.form-content3 .form-group,
.form-content4 .form-group {
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
}

.form-content.expanded .form-group,
.form-content2.expanded .form-group,
.form-content3.expanded .form-group,
.form-content4.expanded .form-group {
  opacity: 1;
}

/* Form styles */
.form-row {
  display: flex;
  gap: 36px;
  margin-bottom: 24px;
}

.form-field {
  flex: 1;
  position: relative;
}

.form-field.full-width {
  width: 398px;
}

.form-field label {
  display: block;
  margin-bottom: 8px;
  font-family: 'Gilmer Bold', sans-serif;
  font-size: 14px;
  color: #374151;
}

.form-input,
.form-select {
  font-family: 'Gilmer Bold', sans-serif;
  width: 398px;
  height: 56px;
  padding: 16px;
  border: 1px solid #E5E7EB;
  border-radius: 0;
  border-top-right-radius: 24px;
  border-bottom-left-radius: 24px;
  font-size: 14px;
  color: #374151;
  background: white;
  appearance: none;
}

.date-input {
  position: relative;
  display: flex;
  align-items: center;
  width: 398px;
}

.date-input .form-input {
  width: 398px;
}

.calendar-btn {
  position: absolute;
  right: 16px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.calendar-btn svg {
  width: 20px;
  height: 20px;
}

/* Error message styles */
.error-message {
  font-family: 'Gilmer Bold', sans-serif;
  color: #dc3545;
  font-size: 12px;
  margin-top: 4px;
}

.required {
  color: #FF0000;
}

/* Footer action bar styles */
.footer-action-bar-container {
  display: flex;
  justify-content: space-between;
  margin-top: 2rem;
}

.footer-btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 24px;
  font-size: 1rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.footer-btn-retour {
  color: #1C3F93;
  background: none;
}

.footer-btn-retour:hover {
  text-decoration: underline;
}

.footer-btn-continuer {
  background-color: #1C3F93;
  color: white;
}

.footer-btn-continuer:hover {
  background-color: #15307A;
}

/* Profile styles */
.profile {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.profile-circle {
  background-color: #1A3E8D;
  color: white;
  border-radius: 50%;
  height: 2rem;
  width: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  text-transform: lowercase;
  font-size: 1rem;
}

.profile-name {
  font-family: 'Gilmer Bold', sans-serif;
  margin-left: 10px;
  color: #1C3F93;
}

.actions {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.icon-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
}

.notification-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.notification-badge {
  background-color: #1A3E8D;
  color: white;
  border-radius: 9999px;
  height: 16px;
  width: 16px;
  font-size: 0.625rem;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: -6px;
  right: -6px;
}

.profile {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: nowrap;
}

.profile-circle {
  background-color: #1A3E8D;
  color: white;
  border-radius: 50%;
  height: 2rem;
  width: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  text-transform: lowercase;
  font-size: 1rem;
}

.profile-name {
  font-family: 'Gilmer Bold', sans-serif;
  margin-left: 10px;
  color: #1C3F93;
}

.vertical-line {
  width: 1px;
  height: 24px;
  background-color: #E5E7EB;
  margin: 0 1rem;
}


.header {
  margin-bottom: 30px;
}

.header h1 {
  color: #1C3F93;
  font-family: 'Gilmer Bold', sans-serif;
  font-size: 20px;
  font-weight: 600;
  margin: 0;
}

.header svg {
  cursor: pointer;
  transition: transform 0.3s ease-in-out;
}

.expanded .header svg {
  transform: rotate(180deg);
}

@media (max-width: 768px) {
  .header h1 {
      font-size: 24px;
  }
  .header {
      margin-bottom: 20px;
  }
}

.toggle-group {
  width: 336px;
  height: 55px;
  position: relative;
  font-family: 'Gilmer Bold';
  
}

.toggle-group label {
  display: block;
  margin-bottom: 12px;
  color: #6B6B6B;
  font-family: 'Gilmer Bold', sans-serif;
  font-size: 14px;
}

.toggle-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.toggle-switch {
  width: 40px;
  height: 20px;
  background: #E0E0E0;
  border-radius: 10px;
  position: relative;
  cursor: pointer;
  transition: background-color 0.3s;
}

.toggle-switch.active {
  background: #1C3F93;
}

.toggle-circle {
  width: 16px;
  height: 16px;
  background: white;
  border-radius: 50%;
  position: absolute;
  top: 2px;
  left: 2px;
  transition: transform 0.3s;
}

.toggle-switch.active .toggle-circle {
  transform: translateX(20px);
}

.toggle-container span {
  color: #6B6B6B;
  font-size: 14px;
  font-family: 'Gilmer Bold', sans-serif;
}


.form-container2 {
  display: flex;
  max-width: 2000px;
  margin: 1px;
  padding: 30px;
}

.form-content2 {
  width: 900px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: relative;
  left: 280px;
  bottom: 400px;
  padding: 30px;
  transition: height 0.3s ease-in-out;
}

.form-container3 {
  display: flex;
  max-width: 2000px;
  margin: 1px;
  padding: 30px;
}

.form-content3 {
  width: 900px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: relative;
  left: 280px;
  bottom: 400px;
  padding: 30px;
  transition: height 0.3s ease-in-out;
}


.form-container4 {
  display: flex;
  max-width: 2000px;
  margin: 1px;
  padding: 30px;
}

.form-content4 {
  width: 900px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: relative;
  left: 280px;
  bottom: 400px;
  padding: 30px;
  transition: height 0.3s ease-in-out;
}

.form-content2.short {
  height: 900px;
}

.form-content2.tall {
  height: 1000px;
}

.additional-fields {
  max-height: 0;
  overflow: hidden;
  opacity: 0;
  transition: all 0.3s ease-in-out;
}

.additional-fields.show {
  max-height: 500px;
  opacity: 1;
  margin-top: 20px;
}

.footer-action-bar-container {
  width: 820px;
  max-width: 880px;
  min-height: 92px;
  background: #fff;
  border-radius: 24px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 24px;
  margin-top: 2px;
  box-shadow: 0 2px 8px #F4F4F4;
  padding: 0 32px 0 0;
  position: relative;
  left:320px;
  bottom: 400px;

  border-bottom-right-radius: 16px;
  border-bottom-left-radius: 16px;
  background: var(--white, #FFFFFF);
}

@media (max-width: 900px) {
  .footer-action-bar-container {
      width: 98vw;
      min-height: auto;
      border-radius: 18px;
      padding: 0 8px;
      justify-content: center;
  }
  .footer-btn {
      width: 90vw;
      justify-content: center;
  }
}

.footer-btn {
  font-family: 'Gilmer Bold', sans-serif;
  font-size: 18px;
  border: none;
  outline: none;
  background: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: color 0.2s, background 0.2s;
  height: 48px;
  padding: 0 16px;
  margin: 0;
}

.footer-btn-retour {
  color: #1C3F93;
  background: none;
  border-radius: 0 24px 24px 0;
  font-weight: 600;
}

.footer-btn-retour:hover {
  text-decoration: underline;
  background: #F4F4F4;
}

.footer-btn-continuer {
  color: #fff;
  background: #00A887;
  width: 135px;
  height: 48px;
  border-top-right-radius: 24px;
  border-bottom-left-radius: 24px;
  border-top-left-radius: 0;
  border-bottom-right-radius: 0;
  font-weight: 600;
}

.footer-btn-continuer:hover {
  background: #00916f;
}

.footer-btn-icon-left {
  font-size: 22px;
  margin-right: 6px;
  line-height: 1;
}

.footer-btn-icon-right {
  font-size: 22px;
  margin-left: 6px;
  line-height: 1;
}

@media (max-width: 480px) {
  .footer-action-bar-container {
      padding: 12px;
  }

  .footer-btn {
      padding: 8px 12px;
      font-size: 14px;
  }
}