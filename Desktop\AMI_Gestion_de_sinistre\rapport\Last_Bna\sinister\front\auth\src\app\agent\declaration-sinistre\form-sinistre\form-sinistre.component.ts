import { CommonModule } from '@angular/common';
import { HttpClient, HttpClientModule } from '@angular/common/http';
import { Component, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { CasDeBareme, CasDeBaremeService } from '../../../../environments/cas-de-bareme.service';
import { Evenement } from '../../../backoffice/backoffice/evenements/evenement.model';
import { EvenementService } from '../../../backoffice/backoffice/evenements/evenement.service';
import { TypeDegat } from '../../../backoffice/backoffice/type-de-degat/type-degat.model';
import { TypeDegatService } from '../../../backoffice/backoffice/type-de-degat/type-degat.service';
import { CsmCreateSessionResponse, CsmService } from '../../../services/csm.service';
import { OuvertureSinistreData, OuvertureSinistreService } from '../../../services/ouverture-sinistre.service';
import { TripoService } from '../../../services/tripo.service';


@Component({
  selector: 'app-form-sinistre',
  standalone: true,
  imports: [CommonModule, FormsModule, HttpClientModule],
  templateUrl: './form-sinistre.component.html',
  styleUrls: ['./form-sinistre.component.css']
})
export class FormSinistreComponent implements OnInit {
  // Platform check for browser-only operations
  private isBrowser: boolean = false;

  showNombreTiers: boolean = false;
  nombreTiers: number | null = null;
  tiersForms: any[] = [];
  evenements: Evenement[] = [];
  uniqueEvenements: Evenement[] = [];
  typeDegats: TypeDegat[] = [];
  filteredTypeDegats: TypeDegat[] = [];
  casDeBaremes: CasDeBareme[] = [];
  // Removed filteredCasDeBaremes - no more filtering needed



  isLoadingTypes = false;
  isLoadingEvents = false;
  isLoadingBaremes = false;

  errorMessageTypes: string | null = null;
  errorMessageEvents: string | null = null;
  errorMessageBaremes: string | null = null;

  // 3D Model properties
  uploadedVehiclePhotos: File[] = [];
  photoPreviewUrls: string[] = [];
  isGenerating3D: boolean = false;
  generationProgress: string = '';

  // Document properties
  availableDocuments: any[] = [];
  uploadedDocuments: File[] = [];
  isLoadingDocuments = false;
  errorMessageDocuments: string | null = null;

  // 3D Damage properties
  selectedDamagePoints: any[] = [];
  formId: string = '';
  callbackUrl: string = '';

  // Mapping of evenement codes to their corresponding type de dégâts
  private evenementToTypeDegatMap: { [key: string]: string[] } = {
    'COL_VEH': ['MATERIEL', 'CORPOREL', 'MAT_CORP'],
    'COL_OBJ': ['MATERIEL'],
    'COL_PIE': ['CORPOREL', 'MAT_CORP'],
    'CAT_NAT': ['MATERIEL'],
    'FOR_NAT': ['MATERIEL'],
    'INCEND': ['MATERIEL'],
    'VOL': ['MATERIEL'],
    'EMEUTE': ['MATERIEL'],
    'DEF_NAT': ['MATERIEL'],
    'DEG_VEH': ['MATERIEL'],
    'DERAP': ['MATERIEL', 'CORPOREL', 'MAT_CORP']
  };

  // Removed evenementToCasBaremeMap - no more filtering needed

  // Complex mapping: TypeDegat + Evenement + Tiers -> Responsabilité values
  private responsabiliteMapping: { [key: string]: number[] } = {
    // Matériels + Catastrophes naturelles + Non -> 0
    'MATERIEL_CAT_NAT_false': [0],

    // Matériels + Collision véhicule + Oui -> 0,1,2,3,4
    'MATERIEL_COL_VEH_true': [0, 1, 2, 3, 4],

    // Mat&corp + Collision véhicule + Oui -> 0,1,2,3,4
    'MAT_CORP_COL_VEH_true': [0, 1, 2, 3, 4],
    // Mat&corp + Collision véhicule + Non -> 0,1,2,3,4
    'MAT_CORP_COL_VEH_false': [0, 1, 2, 3, 4],

    // Corporel + Collision véhicule + Oui -> 0,1,2,3,4
    'CORPOREL_COL_VEH_true': [0, 1, 2, 3, 4],

    // Matériels + Collision objet + Oui -> 0,1,2,3,4
    'MATERIEL_COL_OBJ_true': [0, 1, 2, 3, 4],
    // Matériels + Collision objet + Non -> 4
    'MATERIEL_COL_OBJ_false': [4],

    // Corporels + Collision piéton + Non -> 0,1,2,3,4
    'CORPOREL_COL_PIE_false': [0, 1, 2, 3, 4],

    // Matériels + Défense nationale + Oui -> 0,1,2,3
    'MATERIEL_DEF_NAT_true': [0, 1, 2, 3],
    // Matériels + Défense nationale + Non -> 1,2,3,4
    'MATERIEL_DEF_NAT_false': [1, 2, 3, 4],

    // Matériels + Dégâts véhicules + Non -> 1,2,3,4
    'MATERIEL_DEG_VEH_false': [1, 2, 3, 4],

    // Matériels + Dérapage + Non -> 1,2,3,4
    'MATERIEL_DERAP_false': [1, 2, 3, 4],

    // Matériels + Emeutes + Non -> 0
    'MATERIEL_EMEUTE_false': [0],

    // Matériels + Force nature + Non -> 0
    'MATERIEL_FOR_NAT_false': [0],

    // Matériels + Incendie + Non -> 0
    'MATERIEL_INCEND_false': [0],
    // Matériels + Incendie + Oui -> 4
    'MATERIEL_INCEND_true': [4],

    // Matériels + Vol + Non -> 0
    'MATERIEL_VOL_false': [0]
  };



  sinistreData = {
    degatType: '',
    autreDegat: false,
    description: '',
    descriptionDegat: '', // Added for description_de_degat field
    lieu: '',
    date: '',
    heure: '',
    tiersPresent: false,
    blessurePresent: false,
    documents: [] as File[],
    photos: [] as File[],
    tiersExiste: false,
    nombreTiers: 0,
    tierType: '',
    tierType1: '',
    tierType2: '',
    tierType3: '',
    compagnieTier: '', // Added missing field for database constraint
    casBareme: '',
    degatEstimatif: '',
    evenement: '',
    responsabilite: '',
    linkVehicule: '' // Added to store 3D session ID
  };

  // Available responsabilites for current selection
  availableResponsabilites: number[] = [];

  // Type du Tiers enum options
  tierTypeOptions = [
    { value: 'VEHICULE', label: 'Véhicule' },
    { value: 'PIETON', label: 'Piéton' },
    { value: 'CYCLISTE', label: 'Cycliste' },
    { value: 'MOTOCYCLISTE', label: 'Motocycliste' },
    { value: 'OBJET_FIXE', label: 'Objet fixe' },
    { value: 'ANIMAL', label: 'Animal' },
    { value: 'AUTRE', label: 'Autre' }
  ];

  constructor(
    private evenementService: EvenementService,
    private typeDegatService: TypeDegatService,
    private casDeBaremeService: CasDeBaremeService,
    private tripoService: TripoService,
    private csmService: CsmService,
    private http: HttpClient,
    private router: Router,
    private ouvertureSinistreService: OuvertureSinistreService
  ) {
    // Check if we're in browser environment
    this.isBrowser = typeof window !== 'undefined';
  }

  ngOnInit(): void {
    // Always load data (works in both browser and SSR)
    this.loadTypeDegats();
    this.loadEvenements();
    this.loadCasDeBaremes();

    // Only run browser-specific operations when in browser
    if (this.isBrowser) {
      // Clear all stored data on app restart
      this.clearAllStoredData();

      this.initializeFormId();
      this.setupMessageListener();

      // Set up auto-save on form changes (removed auto-load since we want fresh start)
      this.setupAutoSave();
    }
  }

  ngOnDestroy(): void {
    // Clean up photo preview URLs to prevent memory leaks
    this.photoPreviewUrls.forEach(url => URL.revokeObjectURL(url));
  }

  initializeFormId(): void {
    // Generate unique form ID for this session
    this.formId = 'form_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);

    if (this.isBrowser) {
      this.callbackUrl = window.location.origin + window.location.pathname;
    } else {
      this.callbackUrl = 'http://localhost:4200/form-sinistre'; // fallback for SSR
    }

    console.log('🆔 Form ID initialized:', this.formId);
    console.log('🔗 Callback URL:', this.callbackUrl);
  }

  setupMessageListener(): void {
    // Listen for messages from 3D viewer (only in browser)
    if (this.isBrowser) {
      window.addEventListener('message', (event) => {
        if (event.origin !== window.location.origin) {
          return; // Only accept messages from same origin
        }

        if (event.data.type === '3D_DAMAGE_DATA' && event.data.formId === this.formId) {
          console.log('📨 Received 3D damage data:', event.data.damages);
          this.selectedDamagePoints = event.data.damages || [];
          console.log('✅ Updated damage points:', this.selectedDamagePoints);
        }
      });
    }

    // Also check localStorage for damage data (backup method)
    const checkLocalStorage = () => {
      if (this.isBrowser && window.localStorage) {
        const damageData = localStorage.getItem(`3d_damage_${this.formId}`);
        if (damageData) {
          try {
            const parsedData = JSON.parse(damageData);
            this.selectedDamagePoints = parsedData.damages || [];
            console.log('📦 Loaded damage data from localStorage:', this.selectedDamagePoints);
            // Clear the localStorage after loading
            localStorage.removeItem(`3d_damage_${this.formId}`);
          } catch (error) {
            console.error('❌ Error parsing damage data from localStorage:', error);
          }
        }
      }
    };

    // Check localStorage periodically
    setInterval(checkLocalStorage, 1000);
  }

  loadTypeDegats(): void {
    this.isLoadingTypes = true;
    this.errorMessageTypes = null;
    this.typeDegatService.getAll().subscribe({
      next: (types) => {
        this.typeDegats = types;
        this.filteredTypeDegats = []; // Start empty, will be filtered by evenement
        this.isLoadingTypes = false;
        console.log('Types de dégâts chargés:', types);
      },
      error: (error) => {
        console.error('Erreur lors du chargement des types de dégâts:', error);
        this.errorMessageTypes = 'Erreur lors du chargement des types de dégâts.';
        this.isLoadingTypes = false;
      }
    });
  }

  loadEvenements(): void {
    this.isLoadingEvents = true;
    this.errorMessageEvents = null;
    this.evenementService.getAll().subscribe({
      next: (events) => {
        this.evenements = events;

        // Remove duplicates based on code field and create unique list
        const uniqueEvents = events.filter((event, index, self) =>
          index === self.findIndex(e => String(e.code) === String(event.code))
        );

        this.uniqueEvenements = uniqueEvents;
        this.isLoadingEvents = false;

        console.log('Événements chargés (total):', events.length);
        console.log('Événements uniques (sans doublons):', uniqueEvents.length);
        console.log('Événements uniques:', uniqueEvents);
      },
      error: (error) => {
        console.error('Erreur lors du chargement des événements:', error);
        this.errorMessageEvents = 'Erreur lors du chargement des événements.';
        this.isLoadingEvents = false;
      }
    });
  }

  loadCasDeBaremes(): void {
    this.isLoadingBaremes = true;
    this.errorMessageBaremes = null;
    this.casDeBaremeService.getAll().subscribe({
      next: (baremes: CasDeBareme[]) => {
        this.casDeBaremes = baremes;
        // No more filtering - display all cas de barème
        this.isLoadingBaremes = false;
        console.log('Cas de barème chargés (tous affichés):', baremes);
      },
      error: (error: Error) => {
        console.error('Erreur lors du chargement des cas de barème:', error);
        this.errorMessageBaremes = 'Erreur lors du chargement des cas de barème.';
        this.isLoadingBaremes = false;
      }
    });
  }

  onEvenementChange(): void {
    console.log('Événement sélectionné:', this.sinistreData.evenement);

    // Add current date when user starts filling
    if (!this.sinistreData.date) {
      this.sinistreData.date = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format
      console.log('📅 Date automatically set:', this.sinistreData.date);
    }

    // Reset dependent selections
    this.sinistreData.degatType = '';
    this.sinistreData.casBareme = '';

    // Trigger auto-save when user starts filling
    this.triggerAutoSave();

    if (this.sinistreData.evenement) {
      // Filter TypeDegat based on selected evenement
      const allowedTypeDegatCodes = this.evenementToTypeDegatMap[this.sinistreData.evenement] || [];
      const filteredTypes = this.typeDegats.filter(td => {
        const codeToCheck = String(td.code);
        return allowedTypeDegatCodes.includes(codeToCheck);
      });

      // Remove duplicates based on code field
      const uniqueTypes = filteredTypes.filter((type, index, self) =>
        index === self.findIndex(t => String(t.code) === String(type.code))
      );

      this.filteredTypeDegats = uniqueTypes;
      console.log('Types de dégâts filtrés (sans doublons):', this.filteredTypeDegats);

      // Load documents for selected event
      this.loadDocumentsForEvent();

      // Update responsabilite when evenement changes
      this.updateResponsabiliteFromDatabase();
    } else {
      // No evenement selected, clear type degat filter only
      this.filteredTypeDegats = [];
      this.availableDocuments = [];
      // Reset responsabilite
      this.availableResponsabilites = [];
      this.sinistreData.responsabilite = '';
    }
  }

  onTypeDegatChange(): void {
    console.log('Type de dégât sélectionné:', this.sinistreData.degatType);
    this.updateResponsabiliteFromDatabase();
  }

  onTiersExisteChange(): void {
    this.showNombreTiers = this.sinistreData.tiersExiste;

    // Reset cas de bareme when tiers = Non (like nombre de tiers)
    if (!this.sinistreData.tiersExiste) {
      this.sinistreData.casBareme = '';
    }

    this.updateResponsabiliteFromDatabase();
  }

  onNombreTiersChange(): void {
    if (this.sinistreData.nombreTiers < 1) this.sinistreData.nombreTiers = 1;
    else if (this.sinistreData.nombreTiers > 4) this.sinistreData.nombreTiers = 4;
    this.tiersForms = Array(this.sinistreData.nombreTiers).fill(0);
  }

  /**
   * Remove a tier from the list
   */
  removeTier(index: number): void {
    if (this.sinistreData.nombreTiers > 1) {
      this.sinistreData.nombreTiers--;
      this.tiersForms = Array(this.sinistreData.nombreTiers).fill(0);
      console.log(`🗑️ Removed tier at index ${index}. New count: ${this.sinistreData.nombreTiers}`);
    } else {
      console.log('❌ Cannot remove tier - minimum 1 tier required');
      alert('Au moins un tiers doit être présent.');
    }
  }

  toggleAutreDegat(): void {
    this.sinistreData.autreDegat = !this.sinistreData.autreDegat;
  }

  toggleTiersPresent(): void {
    this.sinistreData.tiersPresent = !this.sinistreData.tiersPresent;
    console.log('Tiers présent changé:', this.sinistreData.tiersPresent);
    // Responsabilité will update automatically via getResponsabiliteDisplay()
  }

  toggleBlessurePresent(): void {
    this.sinistreData.blessurePresent = !this.sinistreData.blessurePresent;
  }

  onDocumentUpload(event: any): void {
    const files = event.target.files;
    if (files && files.length > 0) {
      // Validate file size (24MB max) and type (PDF only)
      const validFiles: File[] = [];
      const maxSize = 24 * 1024 * 1024; // 24MB in bytes

      for (let file of Array.from(files) as File[]) {
        if (file.size > maxSize) {
          alert(`Le fichier "${file.name}" dépasse la taille maximale de 24MB.`);
          continue;
        }
        if (file.type !== 'application/pdf') {
          alert(`Le fichier "${file.name}" doit être un PDF.`);
          continue;
        }
        validFiles.push(file);
      }

      if (validFiles.length > 0) {
        this.uploadedDocuments = [...this.uploadedDocuments, ...validFiles];
        this.sinistreData.documents = this.uploadedDocuments;
        console.log('Documents uploadés:', this.uploadedDocuments);
      }
    }
  }

  loadDocumentsForEvent(): void {
    if (!this.sinistreData.evenement) {
      this.availableDocuments = [];
      return;
    }

    // Find the selected event to get its libelle
    const selectedEvent = this.uniqueEvenements.find(e => e.code === this.sinistreData.evenement);
    if (!selectedEvent) {
      console.log('Event not found for code:', this.sinistreData.evenement);
      return;
    }

    this.isLoadingDocuments = true;
    this.errorMessageDocuments = null;

    // Use environment.apiUrl if available, otherwise fallback to localhost
    const apiUrl = 'http://localhost:8080'; // environment?.apiUrl ||

    this.http.get<any[]>(`${apiUrl}/api/documents-sinistre`).subscribe({
      next: (documents: any[]) => {
        // Filter documents by event libelle
        this.availableDocuments = documents.filter(doc =>
          doc.libelleEvenement === selectedEvent.libelle
        );
        this.isLoadingDocuments = false;
        console.log('Documents disponibles pour cet événement:', this.availableDocuments);
      },
      error: (error) => {
        console.error('Erreur lors du chargement des documents:', error);
        this.errorMessageDocuments = 'Erreur lors du chargement des documents.';
        this.isLoadingDocuments = false;
      }
    });
  }

  removeUploadedDocument(index: number): void {
    this.uploadedDocuments.splice(index, 1);
    this.sinistreData.documents = this.uploadedDocuments;
  }

  getDocumentSizeInMB(file: File): string {
    const sizeInMB = file.size / (1024 * 1024);
    return sizeInMB.toFixed(1) + ' MB';
  }

  triggerFileUpload(): void {
    const fileInput = document.getElementById('documentUpload') as HTMLInputElement;
    if (fileInput) {
      fileInput.click();
    }
  }

  onPhotoUpload(event: any): void {
    const files = event.target.files;
    if (files && files.length > 0) {
      this.sinistreData.photos = Array.from(files);
    }
  }

  updateResponsabiliteFromDatabase(): void {
    // Reset available responsabilites
    this.availableResponsabilites = [];
    this.sinistreData.responsabilite = '';

    // Check if we have all required data
    if (!this.sinistreData.evenement || !this.sinistreData.degatType) {
      console.log('🔍 Missing evenement or degatType for responsabilite lookup');
      return;
    }

    console.log('🔍 Looking up responsabilites for:');
    console.log('  - Evenement code:', this.sinistreData.evenement);
    console.log('  - Type de degat:', this.sinistreData.degatType);
    console.log('  - Tiers existe:', this.sinistreData.tiersExiste);

    // Find all matching evenements for this combination
    const matchingEvents = this.evenements.filter(event =>
      event.code === this.sinistreData.evenement &&
      event.type_de_degat === this.sinistreData.degatType &&
      event.tier === this.sinistreData.tiersExiste
    );

    if (matchingEvents.length > 0) {
      // Extract unique responsabilite values
      const responsabilites = matchingEvents
        .map(event => event.responsabilite)
        .filter(resp => resp !== null && resp !== undefined)
        .filter((value, index, self) => self.indexOf(value) === index) // Remove duplicates
        .sort((a, b) => a - b); // Sort numerically

      this.availableResponsabilites = responsabilites;
      console.log('✅ Found responsabilites:', this.availableResponsabilites);

      // If only one option, auto-select it
      if (this.availableResponsabilites.length === 1) {
        this.sinistreData.responsabilite = this.availableResponsabilites[0].toString();
        console.log('🔄 Auto-selected single responsabilite:', this.sinistreData.responsabilite);
      }
    } else {
      console.log('❌ No matching evenements found for responsabilite');
      console.log('Available evenements:', this.evenements.map(e => ({
        code: e.code,
        type_de_degat: e.type_de_degat,
        tier: e.tier,
        responsabilite: e.responsabilite
      })));
    }
  }



  // Vehicle photo upload methods
  onVehiclePhotoUpload(event: any): void {
    const files = event.target.files;
    if (files && files.length > 0) {
      // Clean up previous URLs to prevent memory leaks
      this.photoPreviewUrls.forEach(url => URL.revokeObjectURL(url));

      // Validate files before adding them
      const validFiles: File[] = [];
      const maxSize = 50 * 1024 * 1024; // 50MB in bytes

      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        console.log('📸 Vehicle photo selected:', file.name, file.size, 'bytes');

        // Check file size
        if (file.size > maxSize) {
          alert(`Fichier trop volumineux: ${file.name}\nTaille maximum autorisée: 50MB\nTaille du fichier: ${(file.size / 1024 / 1024).toFixed(2)}MB`);
          continue; // Skip this file
        }

        // Check file type
        if (!file.type.startsWith('image/')) {
          alert(`Type de fichier non supporté: ${file.name}\nVeuillez sélectionner une image (JPG, PNG, etc.)`);
          continue; // Skip this file
        }

        validFiles.push(file);
        console.log('✅ Vehicle photo validated and added');
      }

      this.uploadedVehiclePhotos = validFiles;

      // Create preview URLs for valid files only
      this.photoPreviewUrls = this.uploadedVehiclePhotos.map(file =>
        URL.createObjectURL(file)
      );

      console.log('Photos de véhicule uploadées:', this.uploadedVehiclePhotos.length, 'fichiers valides');
    }
  }

  // Generate 3D model from uploaded photos using CSM.ai API
  generate3DModel(event?: Event): void {
    console.log('🎯 generate3DModel() called');

    // Prevent any default behavior that might cause page reload
    if (event) {
      event.preventDefault();
      event.stopPropagation();
      console.log('🛑 Event prevented');
    }

    console.log('📸 Uploaded photos count:', this.uploadedVehiclePhotos.length);
    if (this.uploadedVehiclePhotos.length === 0) {
      console.log('❌ No photos uploaded');
      alert('Veuillez d\'abord uploader une photo de véhicule');
      return;
    }

    this.isGenerating3D = true;
    const firstPhoto = this.uploadedVehiclePhotos[0];

    console.log('🚀 Starting 3D model generation...');
    console.log('Photo file:', firstPhoto.name, firstPhoto.size, 'bytes');
    console.log('🔧 CSM Service available:', !!this.csmService);
    console.log('📞 About to call csmService.uploadAndCreateTask()');

    // Start the API call in background
    this.csmService.uploadAndCreateTask(firstPhoto).subscribe({
      next: (response: CsmCreateSessionResponse) => {
        console.log('✅ CSM.ai session created:', response);
        console.log('🔍 Response structure:', JSON.stringify(response, null, 2));
        console.log('🔍 Response._id:', response._id);
        console.log('🔍 Response type:', typeof response._id);

        const sessionId = response._id;
        console.log('🔍 Extracted sessionId:', sessionId);

        // Store session ID in form data for later submission
        this.sinistreData.linkVehicule = `3d-model?sessionId=${sessionId}`;
        console.log('💾 Stored session link in form data:', this.sinistreData.linkVehicule);

        // Open new tab immediately with the 3D model viewer
        const url = `http://localhost:4200/3d-model?sessionId=${sessionId}`;
        console.log('🔗 Opening 3D Model Viewer URL:', url);

        // Force new tab opening with multiple strategies
        console.log('🔗 About to open new tab with URL:', url);
        console.log('🔗 Browser environment check:', this.isBrowser);

        if (this.isBrowser) {
          // Try direct window.open first
          try {
            const newTab = window.open(url, '_blank', 'noopener,noreferrer');
            if (newTab) {
              console.log('✅ New tab opened successfully');
              newTab.focus();
            } else {
              console.log('❌ Popup blocked! Trying alternative method...');
              this.openInNewTab(url);
            }
          } catch (error) {
            console.error('❌ Error opening new tab:', error);
            this.openInNewTab(url);
          }
        } else {
          console.log('❌ Not in browser environment');
        }

        this.isGenerating3D = false;
      },
      error: (error: any) => {
        console.error('❌ Error creating CSM.ai session:', error);

        // Show the real error to user
        alert(`Erreur CSM.ai: ${error}\n\nVérifiez votre clé API et votre connexion internet.`);

        this.isGenerating3D = false;
      }
    });
  }

  // Remove uploaded photo
  removeVehiclePhoto(index: number): void {
    // Revoke the URL to prevent memory leaks
    if (this.photoPreviewUrls[index]) {
      URL.revokeObjectURL(this.photoPreviewUrls[index]);
    }

    this.uploadedVehiclePhotos.splice(index, 1);
    this.photoPreviewUrls.splice(index, 1);
  }

  // Get photo preview URL by index
  getPhotoPreviewUrl(index: number): string {
    return this.photoPreviewUrls[index] || '';
  }

  onSubmit(): void {
    console.log('🔍 Form submission started:', this.sinistreData);
    console.log('🔍 Evenement value:', this.sinistreData.evenement);
    console.log('🔍 DegatType value:', this.sinistreData.degatType);

    // Validate required fields
    if (!this.sinistreData.evenement || !this.sinistreData.degatType) {
      console.error('❌ Validation failed - missing required fields');
      console.error('❌ Evenement:', this.sinistreData.evenement);
      console.error('❌ DegatType:', this.sinistreData.degatType);
      alert('Veuillez remplir tous les champs obligatoires (Événement et Type de dégât).');
      return;
    }

    // Additional validation for tiers-related fields (database constraint)
    if (this.sinistreData.tiersExiste) {
      const missingFields = [];

      if (!this.sinistreData.tierType && !this.sinistreData.tierType1) {
        missingFields.push('Type du Tiers');
      }
      if (!this.sinistreData.compagnieTier) {
        missingFields.push('Compagnie Tiers');
      }
      if (!this.sinistreData.casBareme) {
        missingFields.push('Cas de Barème');
      }

      if (missingFields.length > 0) {
        console.error('❌ Validation failed - missing required tiers fields:', missingFields);
        alert(`Lorsque des tiers existent, les champs suivants sont obligatoires:\n- ${missingFields.join('\n- ')}`);
        return;
      }
    }

    console.log('✅ Validation passed - proceeding with submission');

    // Convert form data to API format
    const formData: OuvertureSinistreData = this.ouvertureSinistreService.convertFormDataToApiFormat(this.sinistreData);

    // Add damage points if any
    if (this.selectedDamagePoints && this.selectedDamagePoints.length > 0) {
      formData.selectedDamagePoints = this.selectedDamagePoints;
    }

    console.log('📤 Submitting form data to backend:', formData);
    console.log('📸 Vehicle photos:', this.uploadedVehiclePhotos.length);
    console.log('📎 Documents:', this.uploadedDocuments.length);

    // Test backend connection first
    console.log('🧪 Testing backend connection...');
    this.http.post<any>('http://localhost:8080/api/ouverture-sinistre/test', { test: 'data', formData: formData }).subscribe({
      next: (response) => {
        console.log('✅ Backend connection test successful:', response);

        if (response.success) {
          // Now try the actual multipart submission with files
          console.log('🔄 Backend connection OK, trying multipart submission...');
          this.ouvertureSinistreService.submitOuvertureSinistre(
            formData,
            this.uploadedVehiclePhotos,
            this.uploadedDocuments
          ).subscribe({
            next: (multipartResponse) => {
              console.log('✅ Multipart submission successful:', multipartResponse);

              // Store data in session for next component
              this.ouvertureSinistreService.storeFormDataInSession(formData, multipartResponse.numeroSinistre);

              // Log success message (no popup)
              console.log(`✅ Sinistre créé avec succès! Numéro de sinistre: ${multipartResponse.numeroSinistre}`);

              // Redirect to recherche-contrat component
              console.log('🔄 Redirecting to recherche-contrat...');
              this.router.navigate(['/recherche-contrat']);
            },
            error: (multipartError) => {
              console.error('❌ Multipart submission failed:', multipartError);
              console.error('❌ Error details:', {
                status: multipartError.status,
                statusText: multipartError.statusText,
                error: multipartError.error,
                message: multipartError.message
              });
              alert('Erreur lors de la soumission du formulaire: ' + (multipartError.error?.message || multipartError.message || 'Erreur inconnue'));
            }
          });
        } else {
          console.error('❌ Backend test failed:', response.message);
          alert('Erreur de connexion backend: ' + response.message);
        }
      },
      error: (error) => {
        console.error('❌ Error submitting form:', error);
        alert('Erreur lors de la soumission du formulaire. Veuillez réessayer.');
      }
    });
  }

  // 3D Damage Methods
  open3DDamageViewer(): void {
    if (this.uploadedVehiclePhotos.length === 0) {
      alert('Veuillez d\'abord uploader une photo de véhicule pour générer le modèle 3D.');
      return;
    }

    // Use the first uploaded photo to generate 3D model
    const firstPhoto = this.uploadedVehiclePhotos[0];
    console.log('🚗 Opening 3D damage viewer with photo:', firstPhoto.name);

    // Start the 3D model generation
    this.csmService.uploadAndCreateTask(firstPhoto).subscribe({
      next: (response: CsmCreateSessionResponse) => {
        console.log('✅ CSM.ai session created for damage viewer:', response);
        const sessionId = response._id;

        // Store session ID in form data for later submission
        this.sinistreData.linkVehicule = `3d-model?sessionId=${sessionId}`;
        console.log('💾 Stored session link in form data:', this.sinistreData.linkVehicule);

        // Create URL with form parameters for damage selection (use existing 3d-model route)
        const damageViewerUrl = `http://localhost:4200/3d-model?sessionId=${sessionId}&formId=${this.formId}&callbackUrl=${encodeURIComponent(this.callbackUrl)}`;
        console.log('🔗 Opening 3D Damage Viewer URL:', damageViewerUrl);

        // Open in new tab (only in browser)
        if (this.isBrowser) {
          const newTab = window.open(damageViewerUrl, '_blank');
          if (!newTab) {
            alert('Popup bloqué! Veuillez autoriser les popups pour ce site.');
          } else {
            console.log('✅ 3D Damage Viewer opened in new tab');
          }
        }
      },
      error: (error) => {
        console.error('❌ Error creating 3D session for damage viewer:', error);
        alert('Erreur lors de la création du modèle 3D. Veuillez réessayer.');
      }
    });
  }

  removeDamagePoint(index: number): void {
    this.selectedDamagePoints.splice(index, 1);
    console.log('🗑️ Removed damage point at index:', index);
    console.log('📍 Remaining damage points:', this.selectedDamagePoints);
  }

  clearAllDamagePoints(): void {
    this.selectedDamagePoints = [];
    console.log('🧹 Cleared all damage points');
  }

  /**
   * Force opening URL in new tab with multiple strategies
   */
  private openInNewTab(url: string): void {
    console.log('🚀 Attempting to open in new tab:', url);

    // Only proceed if in browser environment
    if (!this.isBrowser) {
      console.log('❌ Not in browser environment, cannot open new tab');
      return;
    }

    // Strategy 1: Standard window.open with specific features
    try {
      const newWindow = window.open(url, '_blank', 'noopener,noreferrer,width=1200,height=800');
      if (newWindow && !newWindow.closed) {
        console.log('✅ Strategy 1 success: Standard window.open');
        newWindow.focus();
        return;
      }
    } catch (error) {
      console.warn('⚠️ Strategy 1 failed:', error);
    }

    // Strategy 2: Create invisible link and click it
    try {
      const link = document.createElement('a');
      link.href = url;
      link.target = '_blank';
      link.rel = 'noopener noreferrer';
      link.style.display = 'none';

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      console.log('✅ Strategy 2 success: Invisible link click');
      return;
    } catch (error) {
      console.warn('⚠️ Strategy 2 failed:', error);
    }

    // Strategy 3: Use Object.assign to create link
    try {
      const link = Object.assign(document.createElement('a'), {
        href: url,
        target: '_blank',
        rel: 'noopener noreferrer'
      });

      link.click();
      console.log('✅ Strategy 3 success: Object.assign link');
      return;
    } catch (error) {
      console.warn('⚠️ Strategy 3 failed:', error);
    }

    // Strategy 4: Show user instruction with copy to clipboard
    console.warn('❌ All strategies failed. Showing user instruction.');

    // Copy URL to clipboard
    try {
      navigator.clipboard.writeText(url).then(() => {
        alert(`🔗 URL copiée dans le presse-papiers !\n\nCollez cette URL dans un nouvel onglet:\n${url}\n\nOu autorisez les popups pour ce site.`);
        console.log('📋 URL copied to clipboard');
      });
    } catch (error) {
      console.warn('⚠️ Could not copy to clipboard:', error);
      alert(`Veuillez autoriser les popups pour ce site.\n\nOu copiez cette URL dans un nouvel onglet:\n${url}`);
    }
  }

  /**
   * Load auto-saved form data
   */
  loadAutoSavedData(): void {
    const autoSavedData = this.ouvertureSinistreService.loadAutoSavedFormData();
    if (autoSavedData) {
      console.log('🔄 Restoring auto-saved form data...');

      // Restore form data
      this.sinistreData = { ...this.sinistreData, ...autoSavedData };

      // Show notification to user
      console.log('✅ Form data restored from auto-save');
    }
  }

  /**
   * Set up auto-save functionality
   */
  setupAutoSave(): void {
    console.log('🔧 Setting up auto-save functionality...');

    // Auto-save every 30 seconds
    setInterval(() => {
      this.triggerAutoSave();
    }, 30000);
  }

  /**
   * Trigger auto-save
   */
  triggerAutoSave(): void {
    if (this.sinistreData.evenement || this.sinistreData.degatType) {
      // Only auto-save if user has started filling the form
      this.ouvertureSinistreService.autoSaveFormData(this.sinistreData);
    }
  }

  /**
   * Save 3D session when user generates 3D model
   */
  save3DSessionData(sessionId: string): void {
    console.log('💾 Saving 3D session data to database...');

    this.ouvertureSinistreService.save3DSession(sessionId, this.uploadedVehiclePhotos).subscribe({
      next: (response) => {
        console.log('✅ 3D session saved successfully:', response);
        // Store the database ID for later use
        if (this.isBrowser && window.localStorage) {
          localStorage.setItem('sinistre_3d_session_id', response.id);
        }
      },
      error: (error) => {
        console.error('❌ Error saving 3D session:', error);
      }
    });
  }

  /**
   * Clear all stored data on app restart
   */
  clearAllStoredData(): void {
    console.log('🧹 Clearing all stored data on app restart...');

    // Only proceed if in browser environment
    if (!this.isBrowser) {
      console.log('⚠️ Not in browser environment, skipping storage clear');
      return;
    }

    // Clear localStorage items
    if (window.localStorage) {
      localStorage.removeItem('sinistre_form_autosave');
      localStorage.removeItem('sinistre_3d_session_id');

      // Clear any form-specific damage data
      const keys = Object.keys(localStorage);
      keys.forEach(key => {
        if (key.startsWith('3d_damage_')) {
          localStorage.removeItem(key);
        }
      });

      console.log('✅ localStorage cleared');
    }

    // Clear session storage
    if (window.sessionStorage) {
      sessionStorage.clear();
      console.log('✅ sessionStorage cleared');
    }

    console.log('✅ All stored data cleared');
  }
}
