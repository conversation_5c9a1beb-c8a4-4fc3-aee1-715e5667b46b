package com.fed.platform.sinister.service;

import java.util.List;

import org.springframework.stereotype.Service;

import com.fed.platform.sinister.domain.CasDeBareme;
import com.fed.platform.sinister.repository.CasDeBaremeRepository;

@Service
public class CasDeBaremeService {

    private final CasDeBaremeRepository repository;

    public CasDeBaremeService(CasDeBaremeRepository repository) {
        this.repository = repository;
    }

    public List<CasDeBareme> findAll() {
        return repository.findAll();
    }

    public CasDeBareme create(CasDeBareme casDeBareme) {
        return repository.save(casDeBareme);
    }

    public CasDeBareme update(Long id, CasDeBareme casDeBareme) {
        casDeBareme.setId(id);
        return repository.save(casDeBareme);
    }

    public void delete(Long id) {
        repository.deleteById(id);
    }
}