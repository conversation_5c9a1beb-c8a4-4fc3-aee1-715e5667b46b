.container {
  padding: 24px;
  height: 100%;
  min-height: 500px;
  display: flex;
  flex-direction: column;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  flex-shrink: 0;
}

.header h2 {
  font-family: 'Gilmer Bold', sans-serif;
  color: #1C3F93;
  margin: 0;
}

.create-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  background: #1C3F93;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 24px;
  cursor: pointer;
  font-family: 'Gilmer Bold', sans-serif;
  transition: all 0.3s ease;
}

.create-btn:hover {
  background: #152D69;
  transform: translateY(-1px);
}

.table-container {
  flex: 1;
  min-height: 0;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.table-responsive {
  flex: 1;
  overflow-x: auto;
  overflow-y: auto;
  min-height: 0;
  max-height: 400px; /* Hauteur fixe pour forcer le défilement */
}

.table-responsive::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.table-responsive::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.table-responsive::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 3px;
}

.table-responsive::-webkit-scrollbar-thumb:hover {
  background: #555;
}

table {
  width: 100%;
  border-collapse: collapse;
  background: white;
}

th, td {
  padding: 16px;
  text-align: left;
  border-bottom: 1px solid #E5E7EB;
}

th {
  position: sticky;
  top: 0;
  background: #F9FAFB;
  font-family: 'Gilmer Bold', sans-serif;
  color: #6B7280;
  z-index: 1;
}

td {
  font-family: 'Gilmer Regular', sans-serif;
  color: #374151;
}

tr:hover td {
  background: #F9FAFB;
}

.clickable-row {
  cursor: pointer;
  transition: all 0.2s ease;
}

.clickable-row:hover {
  background: #F0F9FF !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.actions {
  display: flex;
  gap: 8px;
}

.icon-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.icon-btn:hover {
  background: #F3F4F6;
  transform: translateY(-1px);
}

.icon-btn.edit:hover {
  background: #EBF5FF;
  color: #2563EB;
}

.icon-btn.delete:hover {
  background: #FEE2E2;
  color: #DC2626;
}

/* Modal styles */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.2s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.modal-content {
  background: white;
  border-radius: 8px;
  width: 100%;
  max-width: 500px;
  padding: 24px;
  animation: slideIn 0.3s ease;
  transform-origin: center;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.modal-header h3 {
  font-family: 'Gilmer Bold', sans-serif;
  color: #1C3F93;
  margin: 0;
}

.close-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: #F3F4F6;
  transform: scale(1.1);
}

.form-group {
  margin-bottom: 24px;
}

.form-group label {
  display: block;
  font-family: 'Gilmer Bold', sans-serif;
  font-size: 14px;
  color: #6B7280;
  margin-bottom: 8px;
}

.input-wrapper {
  position: relative;
}

.input-wrapper input {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #E5E7EB;
  border-radius: 24px;
  font-family: 'Gilmer Regular', sans-serif;
  font-size: 14px;
  color: #374151;
  transition: all 0.2s ease;
}

.input-wrapper input:focus {
  outline: none;
  border-color: #1C3F93;
  box-shadow: 0 0 0 3px rgba(28, 63, 147, 0.1);
}

.required {
  color: #FF4B55;
}

.error-message {
  color: #FF4B55;
  font-size: 12px;
  margin-top: 4px;
  animation: shake 0.4s ease;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-4px); }
  75% { transform: translateX(4px); }
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  margin-top: 24px;
}

.btn-secondary {
  background: white;
  border: 1px solid #E5E7EB;
  color: #6B7280;
  padding: 12px 24px;
  border-radius: 24px;
  cursor: pointer;
  font-family: 'Gilmer Bold', sans-serif;
  transition: all 0.2s ease;
}

.btn-primary {
  background: #1C3F93;
  border: none;
  color: white;
  padding: 12px 24px;
  border-radius: 24px;
  cursor: pointer;
  font-family: 'Gilmer Bold', sans-serif;
  transition: all 0.2s ease;
}

.btn-primary:disabled {
  background: #E5E7EB;
  cursor: not-allowed;
}

.btn-secondary:hover {
  background: #F9FAFB;
  transform: translateY(-1px);
}

.btn-primary:hover:not(:disabled) {
  background: #152D69;
  transform: translateY(-1px);
}

.form-row {
  display: flex;
  gap: 36px;
  margin-bottom: 24px;
}

.form-group {
  flex: 1;
  position: relative;
}

.form-group label {
  display: block;
  font-family: 'Gilmer Bold', sans-serif;
  font-size: 14px;
  line-height: 120%;
  margin-bottom: 8px;
  color: #6B7280;
}

.input-wrapper {
  position: relative;
}

.input-wrapper input,
.input-wrapper select {
  font-family: 'Gilmer Bold', sans-serif;
  width: 100%;
  height: 56px;
  padding: 16px;
  border: 1px solid #E5E7EB;
  border-radius: 0;
  border-top-right-radius: 24px;
  border-bottom-left-radius: 24px;
  font-size: 14px;
  color: #374151;
  background: white;
  appearance: none;
}

/* Toggle Switch Styles */
.toggle-wrapper {
  display: flex;
  align-items: center;
  gap: 12px;
}

.toggle {
  position: relative;
  display: inline-block;
  width: 60px;
  height: 34px;
}

.toggle input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
  border-radius: 34px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 26px;
  width: 26px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: #1C3F93;
}

input:checked + .slider:before {
  transform: translateX(26px);
}

.toggle-label {
  font-family: 'Gilmer Bold', sans-serif;
  font-size: 14px;
  color: #374151;
}

/* Details Modal Styles */
.details-modal {
  max-width: 700px;
}

.details-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 24px;
}

.details-grid .form-group:first-child {
  grid-column: 1 / -1;
}

.arrow-icon {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
}

.required {
  color: #FF0000;
}

.error-message {
  font-family: 'Gilmer Bold', sans-serif;
  color: #dc3545;
  font-size: 12px;
  margin-top: 4px;
}

/* Custom Material Date Picker */
.custom-form-field {
  width: 398px !important;
}

::ng-deep .mat-mdc-form-field {
  width: 398px !important;
}

::ng-deep .mat-mdc-text-field-wrapper {
  width: 398px !important;
  background-color: white !important;
  padding: 0 !important;
  height: 56px !important;
}

::ng-deep .mat-mdc-form-field-flex {
  padding: 0 16px !important;
  height: 56px !important;
  border: 1px solid #E5E7EB !important;
  border-radius: 0 !important;
  border-top-right-radius: 24px !important;
  border-bottom-left-radius: 24px !important;
  width: 398px !important;
}

::ng-deep .mat-mdc-form-field-infix {
  padding: 16px 0 !important;
  width: 100% !important;
}

::ng-deep .mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline__leading,
::ng-deep .mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline__notch,
::ng-deep .mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline__trailing {
  border-color: transparent !important;
}

::ng-deep .mat-mdc-form-field-subscript-wrapper {
  display: none;
}

::ng-deep #input-hey {
  width: 398px !important;
}

::ng-deep #input-hey .mat-mdc-form-field-flex {
  width: 398px !important;
  height: 56px !important;
}

::ng-deep #input-hey .mat-mdc-form-field-infix {
  height: 56px !important;
  display: flex;
  align-items: center;
}

::ng-deep #input-hey input.mat-mdc-input-element {
  height: 56px !important;
  box-sizing: border-box;
}