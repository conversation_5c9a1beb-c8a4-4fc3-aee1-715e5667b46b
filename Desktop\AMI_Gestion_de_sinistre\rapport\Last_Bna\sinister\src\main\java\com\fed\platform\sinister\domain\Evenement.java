package com.fed.platform.sinister.domain;

import com.fasterxml.jackson.annotation.JsonProperty;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

@Entity
@Table(name = "evenement")
public class Evenement {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String code;
    private String libelle;

    @Column(name = "type_de_degat")
    private String type_de_degat;

    @Column(name = "tier")
    @JsonProperty("tier")
    private Boolean tier;

    @Column(name = "responsabilite")
    @JsonProperty("responsabilite")
    private Integer responsabilite;

    @Column(name = "garantie")
    @JsonProperty("garantie")
    private String garantie;

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getLibelle() {
        return libelle;
    }

    public void setLibelle(String libelle) {
        this.libelle = libelle;
    }

    public String getType_de_degat() {
        return type_de_degat;
    }

    public void setType_de_degat(String type_de_degat) {
        this.type_de_degat = type_de_degat;
    }

    public Boolean getTier() {
        return tier;
    }

    public void setTier(Boolean tier) {
        this.tier = tier;
    }

    public Integer getResponsabilite() {
        return responsabilite;
    }

    public void setResponsabilite(Integer responsabilite) {
        this.responsabilite = responsabilite;
    }

    public String getGarantie() {
        return garantie;
    }

    public void setGarantie(String garantie) {
        this.garantie = garantie;
    }
}