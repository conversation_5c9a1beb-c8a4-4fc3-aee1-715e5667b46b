package com.fed.platform.sinister.domain;

import java.io.Serializable;
import java.time.Instant;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

@Entity
@Table(name = "conducteur")
public class Conducteur implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "vhicule_en_stationement")
    private Boolean vhiculeEnStationement;

    @Column(name = "conducteur_meme_assure")
    private Boolean conducteurMemeAssure;

    @Column(name = "nom")
    private String nom;

    @Column(name = "prenom")
    private String prenom;

    @Enumerated(EnumType.STRING)
    @Column(name = "categorie_du_permis")
    private CategorieDuPermis categorieDuPermis;

    @Column(name = "numero_du_permis")
    private Integer numeroDuPermis;

    @Column(name = "date_de_naissance")
    private Instant dateDeNaissance;

    @Column(name = "date_obtention_permis")
    private Instant dateObtentionPermis;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Boolean isVhiculeEnStationement() {
        return vhiculeEnStationement;
    }

    public void setVhiculeEnStationement(Boolean vhiculeEnStationement) {
        this.vhiculeEnStationement = vhiculeEnStationement;
    }

    public Boolean isConducteurMemeAssure() {
        return conducteurMemeAssure;
    }

    public void setConducteurMemeAssure(Boolean conducteurMemeAssure) {
        this.conducteurMemeAssure = conducteurMemeAssure;
    }

    public String getNom() {
        return nom;
    }

    public void setNom(String nom) {
        this.nom = nom;
    }

    public String getPrenom() {
        return prenom;
    }

    public void setPrenom(String prenom) {
        this.prenom = prenom;
    }

    public CategorieDuPermis getCategorieDuPermis() {
        return categorieDuPermis;
    }

    public void setCategorieDuPermis(CategorieDuPermis categorieDuPermis) {
        this.categorieDuPermis = categorieDuPermis;
    }

    public Integer getNumeroDuPermis() {
        return numeroDuPermis;
    }

    public void setNumeroDuPermis(Integer numeroDuPermis) {
        this.numeroDuPermis = numeroDuPermis;
    }

    public Instant getDateDeNaissance() {
        return dateDeNaissance;
    }

    public void setDateDeNaissance(Instant dateDeNaissance) {
        this.dateDeNaissance = dateDeNaissance;
    }

    public Instant getDateObtentionPermis() {
        return dateObtentionPermis;
    }

    public void setDateObtentionPermis(Instant dateObtentionPermis) {
        this.dateObtentionPermis = dateObtentionPermis;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof Conducteur)) {
            return false;
        }
        return id != null && id.equals(((Conducteur) o).id);
    }

    @Override
    public int hashCode() {
        return 31;
    }

    @Override
    public String toString() {
        return "Conducteur{" +
                "id=" + getId() +
                ", vhiculeEnStationement='" + isVhiculeEnStationement() + "'" +
                ", conducteurMemeAssure='" + isConducteurMemeAssure() + "'" +
                ", nom='" + getNom() + "'" +
                ", prenom='" + getPrenom() + "'" +
                ", categorieDuPermis='" + getCategorieDuPermis() + "'" +
                ", numeroDuPermis=" + getNumeroDuPermis() +
                ", dateDeNaissance='" + getDateDeNaissance() + "'" +
                ", dateObtentionPermis='" + getDateObtentionPermis() + "'" +
                "}";
    }
}
