<body>
  <div class="form-container">
    <div class="header">
      <h1>Ouverture sinistre</h1>
    </div>

    <div class="progress-steps">
      <div class="step active">
        <div class="step-number">1</div>
        <div class="step-title"><strong>Ouverture sinistre</strong></div>
        <div class="step-subtitle">
          <strong><span class="green">Données sinistre</span></strong>
          <strong><span class="blue">Recherche contrat</span></strong>
        </div>
      </div>
      <div class="step">
        <div class="step-number">2</div>
        <strong><div class="step-title">Lieu de survenance du sinistre</div></strong>
      </div>
      <div class="step">
        <div class="step-number">3</div>
        <strong><div class="step-title">Données conducteur</div></strong>
      </div>
      <div class="step">
        <div class="step-number">4</div>
        <strong><div class="step-title">Données tiers</div></strong>
      </div>
      <div class="step">
        <div class="step-number">5</div>
        <strong><div class="step-title">Documents</div></strong>
      </div>
      <div class="step">
        <div class="step-number">6</div>
        <strong><div class="step-title">Récapitulatif</div></strong>
      </div>
    </div>

    <div class="form-content">
      <h2>Saisi des données sinistres</h2>
      <div class="form-row">
       <!-- src/app/agent/declaration-sinistre/form-sinistre/form-sinistre.component.html -->
<div class="form-group">
  <label>Evenement <span class="required">*</span></label>
  <select [(ngModel)]="sinistreData.evenement"
          name="evenement"
          class="custom-select"
          (change)="onEvenementChange()"
          [disabled]="isLoadingEvents"
          required>
    <option value="" disabled selected>
      {{ isLoadingEvents ? 'Chargement des événements...' : 'Sélectionner un événement' }}
    </option>
    <option *ngFor="let event of uniqueEvenements" [value]="event.code">
      {{ event.libelle }}
    </option>
  </select>
  <div *ngIf="errorMessageEvents" class="error-message">
    {{ errorMessageEvents }}
  </div>
</div>

<div class="form-group">
  <label>Types de dégâts <span class="required">*</span></label>
  <select [(ngModel)]="sinistreData.degatType"
          name="degatType"
          class="custom-select"
          (change)="onTypeDegatChange()"
          [disabled]="!sinistreData.evenement || isLoadingTypes"
          required>
    <option value="" disabled selected>
      {{ isLoadingTypes ? 'Chargement des types de dégâts...' :
         (!sinistreData.evenement ? 'Sélectionnez d\'abord un événement' : 'Sélectionner un type de dégât') }}
    </option>
    <option *ngFor="let type of filteredTypeDegats" [value]="type.code">
      {{ type.code }}
    </option>
  </select>
  <div *ngIf="errorMessageTypes" class="error-message">
    {{ errorMessageTypes }}
  </div>
</div>
      </div>
      


       <div class="form-content">
      <h2>Dégats relatif a l'assuré</h2>
      <div class="form-row">
        <div class="form-group">
         <label>Description de dégats <span class="required">*</span></label>
          <input type="text"
                 [(ngModel)]="sinistreData.descriptionDegat"
                 placeholder="Description"
                 name="descriptionDegat"
                 class="custom-input">
        </div>
      </div>
      

      <div class="form-container">
        <div class="form-row2" [ngClass]="{'form-row2-expanded': sinistreData.tiersExiste}">
          <div class="form-group">
            <h2>Saisi des données tiers</h2>
           <label>Existe-t-il des tiers ?</label>
            <div class="toggle-group">
              <div class="toggle-container" (click)="sinistreData.tiersExiste = !sinistreData.tiersExiste; onTiersExisteChange()" [ngClass]="{'toggle-oui': sinistreData.tiersExiste, 'toggle-non': !sinistreData.tiersExiste}">
                <span class="toggle-option">Non</span>
                <div class="toggle-button" [ngStyle]="{'background': sinistreData.tiersExiste ? '#013888' : '#F8F9FC'}">
                  <div class="toggle-circle" [ngStyle]="{'transform': sinistreData.tiersExiste ? 'translateX(20px)' : 'translateX(0)'}"></div>
                </div>
                <span class="toggle-option">Oui</span>
              </div>
            </div>
          </div>
          <div class="form-group2" *ngIf="showNombreTiers">
            <label>Nombre du tiers <span class="required">*</span></label>
            <input type="number" placeholder="Nombre du tiers" [(ngModel)]="sinistreData.nombreTiers" (change)="onNombreTiersChange()" min="1" max="4">
          </div>
        </div>

        <div class="tiers-container">
          <ng-container *ngIf="sinistreData.tiersExiste; else noTiersBox">
            <div *ngFor="let i of tiersForms; let index = index">
              <div class="row-tier">
                <div class="border-jemla"
                     (click)="removeTier(index)"
                     title="Supprimer ce tiers"
                     style="cursor: pointer;">
                  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M17.5 4.98356C14.725 4.70856 11.9333 4.56689 9.15 4.56689C7.5 4.56689 5.85 4.65023 4.2 4.81689L2.5 4.98356" stroke="#1C3F93"/>
                    <path opacity="0.34" d="M7.08333 4.1415L7.26666 3.04984C7.4 2.25817 7.49999 1.6665 8.90833 1.6665H11.0917C12.5 1.6665 12.6083 2.2915 12.7333 3.05817L12.9167 4.1415" stroke="#1C3F93"/>
                    <path d="M15.7083 7.6167L15.1667 16.0084C15.075 17.3167 15 18.3334 12.675 18.3334H7.325C5 18.3334 4.92501 17.3167 4.83334 16.0084L4.29167 7.6167" stroke="#1C3F93"/>
                    <path opacity="0.34" d="M8.60834 13.75H11.3833" stroke="#1C3F93"/>
                    <path opacity="0.34" d="M7.91667 10.4165H12.0833" stroke="#1C3F93"/>
                  </svg>

                  <h2 class="tiers-title">Tiers {{index + 1}}</h2>

                  <div class="row-fields">
                    <div class="field-group">
                      <label>Type du Tiers <span class="required">*</span></label>
                      <select [(ngModel)]="sinistreData.tierType1" name="tierType1" class="custom-select">
                        <option value="" disabled selected>Sélectionner le type de tiers</option>
                        <option *ngFor="let option of tierTypeOptions" [value]="option.value">
                          {{ option.label }}
                        </option>
                      </select>
                    </div>

                    <div class="field-group">
                      <label>Compagnie du Tiers <span class="required">*</span></label>
                      <input type="text"
                             [(ngModel)]="sinistreData.compagnieTier"
                             placeholder="Compagnie du Tiers"
                             name="compagnieTier"
                             class="custom-input">
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </ng-container>
          <ng-template #noTiersBox>
            <div class="tiers-placeholder-box"></div>
          </ng-template>
        </div>
      </div>

      <!-- Container: Cas de barème, Dégats estimatif et Responsabilité -->
      <div class="bareme-resp-container" [ngClass]="{'bareme-resp-expanded': sinistreData.tiersExiste}">
        <h2 class="bareme-resp-title">Cas de barème et responsabilité</h2>
        <div class="bareme-resp-fields">
          <!-- Cas de barème - Only show when tiers exist -->
          <div class="form-group3" *ngIf="sinistreData.tiersExiste">
            <label>Cas de barème <span class="required">*</span></label>
            <select [(ngModel)]="sinistreData.casBareme"
                    name="casBareme"
                    class="custom-select"
                    [disabled]="isLoadingBaremes"
                    required>
              <option value="" disabled selected>
                {{ isLoadingBaremes ? 'Chargement des cas de barème...' : 'Choisir un cas de barème' }}
              </option>
              <option *ngFor="let cas of casDeBaremes" [value]="cas.id">
                {{ cas.libelle }}
              </option>
            </select>
            <div *ngIf="errorMessageBaremes" class="error-message">
              {{ errorMessageBaremes }}
            </div>
          </div>

          <!-- Dégats estimatif -->
          <div class="form-group3">
            <label>Dégats estimatif <span class="required">*</span></label>
            <input type="text"
                   [(ngModel)]="sinistreData.degatEstimatif"
                   name="degatEstimatif"
                   class="custom-select"
                   placeholder="Dégats estimatif">
          </div>

          <!-- Responsabilité -->
          <div class="form-group3">
            <label>Responsabilité <span class="required">*</span></label>
            <select [(ngModel)]="sinistreData.responsabilite"
                    name="responsabilite"
                    class="custom-select"
                    [disabled]="!sinistreData.evenement || !sinistreData.degatType"
                    required>
              <option value="" disabled selected>
                {{ (!sinistreData.evenement || !sinistreData.degatType) ? 'Sélectionnez d\'abord événement et type de dégât' : 'Choisir une responsabilité' }}
              </option>
              <option *ngFor="let resp of availableResponsabilites" [value]="resp">
                {{ resp }}
              </option>
            </select>
          </div>
        </div>
      </div>

      <!-- Attachment Section -->
<div class="attachment-section">
  
  <div class="attachment-box">
    <h2 class="attachment-title">Visualization 3D</h2>
    <div class="attachment-doc-box">
      <div class="attachment-row" style="border:none;box-shadow:none;padding:0;">
        <div>
          <span class="attachment-label"><strong>Ajouter photos de véhicule</strong></span>
          <div class="attachment-note">(JPEG,PNG)</div>
        </div>
        <div class="upload-actions">
          <input type="file"
                 #vehiclePhotoInput
                 (change)="onVehiclePhotoUpload($event)"
                 accept="image/jpeg,image/png"
                 multiple
                 style="display: none;">
          <button class="attachment-upload-btn"
                  type="button"
                  (click)="vehiclePhotoInput.click()"
                  [disabled]="isGenerating3D">
            <span class="icon-paperclip">📎</span>
          </button>
          <button class="btn-3d-generate"
                  type="button"
                  (click)="generate3DModel($event)"
                  [disabled]="uploadedVehiclePhotos.length === 0 || isGenerating3D">
            <span class="icon-3d">🎯</span>
            {{ isGenerating3D ? 'Génération...' : 'Générer 3D' }}
          </button>
        </div>
      </div>

      <!-- Progress indicator -->
      <div class="generation-progress" *ngIf="isGenerating3D">
        <div class="progress-spinner"></div>
        <span>{{ generationProgress }}</span>
      </div>
    </div>
<!-- Row: Document du constat -->



    <div class="photo-upload-area">
      <div class="photo-upload-label">
        Disposez-vous des photos ?<br>
        <span class="photo-upload-note">(JPEG,PNG)</span>
      </div>
    </div>

    <!-- Row: Photo previews -->
    <div class="photo-preview-row" *ngIf="uploadedVehiclePhotos.length > 0">
      <div class="photo-preview-container">
        <h4>Photos de véhicule uploadées:</h4>
        <div class="photo-grid">
          <div class="photo-item" *ngFor="let photo of uploadedVehiclePhotos; let i = index">
            <img [src]="getPhotoPreviewUrl(i)" [alt]="photo.name" class="photo-thumbnail">
            <div class="photo-info">
              <span class="photo-name">{{ photo.name }}</span>
              <button class="photo-remove-btn" (click)="removeVehiclePhoto(i)" type="button">
                <span class="icon-trash">🗑️</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>


<div class="attachment-section">

  <div class="attachment-box">
    <h2 class="attachment-title">Attachement</h2>

    <!-- Available Documents from Backoffice -->
    @if (sinistreData.evenement && availableDocuments.length > 0) {
      <div class="available-documents">
        <h3>Documents disponibles pour cet événement:</h3>
        @for (doc of availableDocuments; track doc.id) {
          <div class="available-doc-item">
            <span class="doc-name">{{ doc.document }}</span>
            <span class="doc-path">{{ doc.pathFichier }}</span>
          </div>
        }
      </div>
    }

    <!-- Document Upload Section -->
    <div class="attachment-doc-box">
      <div class="attachment-row" style="border:none;box-shadow:none;padding:0;">
        <div>
          <span class="attachment-label"><strong>Ajouter un constat</strong></span>
          <div class="attachment-note">(PDF - Max 24MB)</div>
        </div>
        <input type="file"
               id="documentUpload"
               accept=".pdf"
               multiple
               (change)="onDocumentUpload($event)"
               style="display: none;">
        <button class="attachment-upload-btn"
                type="button"
                (click)="triggerFileUpload()">
          <span class="icon-paperclip"></span>
        </button>
      </div>
    </div>

    <!-- Uploaded Documents Display -->
    @if (uploadedDocuments.length > 0) {
      <div class="uploaded-documents">
        <h3>Documents du constat <span class="required">*</span></h3>
        @for (doc of uploadedDocuments; track $index) {
          <div class="attachment-doc-box">
            <div>
              <span class="attachment-doc-label"><strong>{{ doc.name }}</strong></span>
              <br>
              <span class="attachment-doc-filename">{{ getDocumentSizeInMB(doc) }}</span>
            </div>
            <button class="attachment-doc-delete"
                    type="button"
                    title="Supprimer"
                    (click)="removeUploadedDocument($index)">
              <span class="icon-trash"></span>
            </button>
          </div>
        }
      </div>
    }


   
    <!-- Row: Photo previews -->
    <div class="photo-preview-row">
     
    </div>
  </div>
</div>
<div class="footer-action-bar-container">
  <button class="footer-btn footer-btn-retour" aria-label="Retour">
    <span class="footer-btn-icon-left">&larr;</span> Retour
  </button>
  <button class="footer-btn footer-btn-continuer" aria-label="Continuer" (click)="onSubmit()">
    Continuer <span class="footer-btn-icon-right">&rarr;</span>
  </button>
</div>
