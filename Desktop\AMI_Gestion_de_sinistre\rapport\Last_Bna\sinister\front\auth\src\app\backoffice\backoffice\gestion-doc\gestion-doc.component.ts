import { CommonModule } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { environment } from '../../../../environments/environment';
import { Evenement } from '../evenements/evenement.model';
import { EvenementService } from '../evenements/evenement.service';

@Component({
  selector: 'app-document-management',
  templateUrl: './gestion-doc.component.html',
  styleUrls: ['./gestion-doc.component.css'],
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule]
})
export class DocumentManagementComponent implements OnInit {

  events: Evenement[] = [];
  uniqueEvents: Evenement[] = [];
  documents: any[] = [];
  eventDocuments: Map<string, any[]> = new Map();
  isLoading = false;
  errorMessage: string | null = null;
  successMessage: string | null = null;
  showEventModal = false;
  showDocumentModal = false;
  selectedEvent: Evenement | null = null;
  editingEvent: Evenement | null = null;
  newEventName = '';
  newDocumentName = '';
  documentForm: FormGroup;

  constructor(
    private evenementService: EvenementService,
    private fb: FormBuilder,
    private http: HttpClient
  ) {
    this.documentForm = this.fb.group({
      libelleEvenement: ['', [Validators.required]],
      document: ['', [Validators.required]]
    });
  }

  ngOnInit(): void {
    this.loadEvents();
    this.loadDocuments();
  }



  loadEvents(): void {
    this.isLoading = true;
    this.evenementService.getAll().subscribe({
      next: (events) => {
        this.events = events;
        this.isLoading = false;
        this.organizeEventDocuments();
      },
      error: (_error) => {
        this.errorMessage = 'Erreur lors du chargement des événements.';
        this.isLoading = false;
      }
    });
  }

  loadDocuments(): void {
    this.http.get(`${environment.apiUrl}/documents-sinistre`).subscribe({
      next: (documents: any) => {
        this.documents = documents;
        this.organizeEventDocuments();
      },
      error: (error) => {
        console.error('Erreur lors du chargement des documents:', error);
      }
    });
  }

  organizeEventDocuments(): void {
    this.eventDocuments.clear();

    // Group documents by event libelle
    this.documents.forEach(doc => {
      const eventLibelle = doc.libelleEvenement;
      if (!this.eventDocuments.has(eventLibelle)) {
        this.eventDocuments.set(eventLibelle, []);
      }
      this.eventDocuments.get(eventLibelle)?.push(doc);
    });

    // Create unique events list - only show events that have documents
    const uniqueLibelles = new Set<string>();
    this.uniqueEvents = [];

    // First, add events that have documents
    this.eventDocuments.forEach((docs, libelle) => {
      const event = this.events.find(e => e.libelle === libelle);
      if (event && !uniqueLibelles.has(libelle)) {
        this.uniqueEvents.push(event);
        uniqueLibelles.add(libelle);
      }
    });

    // Then, add events that don't have documents yet (to allow adding documents)
    this.events.forEach(event => {
      if (!uniqueLibelles.has(event.libelle)) {
        this.uniqueEvents.push(event);
        uniqueLibelles.add(event.libelle);
      }
    });
  }

  getDocumentsForEvent(eventLibelle: string): any[] {
    return this.eventDocuments.get(eventLibelle) || [];
  }

  openEventModal(event?: Evenement): void {
    this.editingEvent = event || null;
    this.showEventModal = true;
    this.errorMessage = null;
    this.successMessage = null;

    // Reset form
    this.documentForm.reset();

    if (event) {
      // Edit mode - populate form with existing data
      this.documentForm.patchValue({
        libelleEvenement: event.libelle,
        document: ''
      });
    }
  }


  closeModal(): void {
    this.showEventModal = false;
    this.showDocumentModal = false;
    this.selectedEvent = null;
    this.editingEvent = null;
    this.newEventName = '';
    this.newDocumentName = '';
    this.errorMessage = null;
    this.successMessage = null;
    this.documentForm.reset();
  }

  onSubmit(): void {
    if (this.documentForm.invalid) {
      this.documentForm.markAllAsTouched();
      return;
    }

    this.isLoading = true;
    this.errorMessage = null;

    const formValue = this.documentForm.value;
    const documentData = {
      libelleEvenement: formValue.libelleEvenement,
      document: formValue.document,
      pathFichier: `/documents/${formValue.document}`
    };

    console.log('Création document sinistre:', documentData);

    // Call backend API
    this.http.post(`${environment.apiUrl}/documents-sinistre`, documentData, {
      headers: {
        'Content-Type': 'application/json'
      }
    })
      .subscribe({
        next: (response: any) => {
          console.log('Document créé avec succès:', response);
          this.successMessage = `Document "${formValue.document}" créé avec succès pour l'événement "${formValue.libelleEvenement}"`;
          this.isLoading = false;
          this.loadDocuments(); // Refresh the documents list
          this.closeModal();
        },
        error: (error) => {
          console.error('Erreur lors de la création du document:', error);
          console.error('Error details:', error.error);
          console.error('Status:', error.status);
          console.error('Message:', error.message);

          let errorMsg = 'Erreur lors de la création du document.';
          if (error.error && error.error.message) {
            errorMsg += ' Détails: ' + error.error.message;
          } else if (error.status === 500) {
            errorMsg += ' Erreur serveur interne.';
          }

          this.errorMessage = errorMsg;
          this.isLoading = false;
        }
      });
  }

  onSubmitEvent(): void {
    this.onSubmit();
  }

  onSubmitDocument(): void {
    if (this.newDocumentName.trim()) {
      this.successMessage = 'Document ajouté: ' + this.newDocumentName;
      this.closeModal();
    }
  }

  deleteEvent(_eventId: number): void {
    if (confirm('Êtes-vous sûr de vouloir supprimer cet événement ?')) {
      this.errorMessage = 'Suppression non implémentée';
    }
  }

  deleteDocument(docId: number): void {
    if (confirm('Êtes-vous sûr de vouloir supprimer ce document ?')) {
      this.isLoading = true;
      this.http.delete(`${environment.apiUrl}/documents-sinistre/${docId}`).subscribe({
        next: () => {
          this.successMessage = 'Document supprimé avec succès';
          this.loadDocuments(); // Refresh the list
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Erreur lors de la suppression:', error);
          this.errorMessage = 'Erreur lors de la suppression du document';
          this.isLoading = false;
        }
      });
    }
  }

  forceShowModal(): void {
    console.log('🔄 Force show modal called');
    this.showEventModal = true;
    console.log('✅ showEventModal set to:', this.showEventModal);
    setTimeout(() => {
      console.log('⏰ After timeout, showEventModal:', this.showEventModal);
    }, 100);
  }
}