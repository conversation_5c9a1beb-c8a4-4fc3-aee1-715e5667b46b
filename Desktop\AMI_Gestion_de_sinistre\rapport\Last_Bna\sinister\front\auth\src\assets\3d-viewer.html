<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Visualisation 3D - Véhicule</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            flex-direction: column;
            font-family: Arial, sans-serif;
            overflow: hidden;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 30px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            margin: 0;
            color: #2c3e50;
            font-size: 24px;
        }

        .actions {
            display: flex;
            gap: 15px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #013888;
            color: white;
        }

        .btn-primary:hover {
            background: #012a66;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: transparent;
            color: #6c757d;
            border: 2px solid #6c757d;
        }

        .btn-secondary:hover {
            background: #6c757d;
            color: white;
        }

        .content {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            padding: 20px;
        }

        .car-container {
            position: relative;
            width: 300px;
            height: 150px;
            animation: float 3s ease-in-out infinite;
        }

        .car-body {
            width: 200px;
            height: 60px;
            background: linear-gradient(45deg, #0066cc, #004499);
            border-radius: 10px;
            position: absolute;
            top: 50px;
            left: 50px;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
        }

        .car-roof {
            width: 120px;
            height: 40px;
            background: linear-gradient(45deg, #004499, #002266);
            border-radius: 8px;
            position: absolute;
            top: 20px;
            left: 90px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .wheel {
            width: 40px;
            height: 40px;
            background: radial-gradient(circle, #333, #111);
            border-radius: 50%;
            position: absolute;
            border: 3px solid #555;
            animation: rotate 2s linear infinite;
        }

        .wheel-1 { top: 80px; left: 30px; }
        .wheel-2 { top: 80px; right: 30px; }
        .wheel-3 { top: 80px; left: 70px; }
        .wheel-4 { top: 80px; right: 70px; }

        .info-panel {
            position: absolute;
            bottom: 30px;
            right: 30px;
            background: rgba(255, 255, 255, 0.95);
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            min-width: 250px;
        }

        .info-panel h4 {
            margin: 0 0 15px 0;
            color: #2c3e50;
            font-size: 16px;
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .info-row span:first-child {
            color: #6c757d;
        }

        .info-row span:last-child {
            color: #2c3e50;
            font-weight: 600;
        }

        .success {
            color: #28a745 !important;
        }

        .demo {
            color: #ff6b35 !important;
        }

        .success-message {
            position: absolute;
            top: 30px;
            left: 30px;
            background: rgba(40, 167, 69, 0.9);
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .success-message h3 {
            margin: 0 0 5px 0;
            font-size: 16px;
        }

        .success-message p {
            margin: 0;
            font-size: 14px;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotateY(0deg); }
            50% { transform: translateY(-10px) rotateY(5deg); }
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <h1>🚗 Visualisation 3D du Véhicule</h1>
        <div class="actions">
            <button class="btn btn-primary" onclick="downloadModel()">
                ⬇️ Télécharger Modèle
            </button>
            <button class="btn btn-secondary" onclick="window.close()">
                ❌ Fermer
            </button>
        </div>
    </div>

    <!-- Content -->
    <div class="content">
        <!-- Car Animation -->
        <div class="car-container">
            <div class="car-body"></div>
            <div class="car-roof"></div>
            <div class="wheel wheel-1"></div>
            <div class="wheel wheel-2"></div>
            <div class="wheel wheel-3"></div>
            <div class="wheel wheel-4"></div>
        </div>

        <!-- Success Message -->
        <div class="success-message">
            <h3>🎉 Modèle 3D Généré avec Succès !</h3>
            <p>Votre véhicule a été converti en modèle 3D</p>
        </div>

        <!-- Info Panel -->
        <div class="info-panel">
            <h4>Informations du Modèle</h4>
            <div class="info-row">
                <span>Task ID:</span>
                <span id="taskId">demo-task</span>
            </div>
            <div class="info-row">
                <span>Format:</span>
                <span>GLB</span>
            </div>
            <div class="info-row">
                <span>Status:</span>
                <span class="success">✅ Généré</span>
            </div>
            <div class="info-row">
                <span>Mode:</span>
                <span class="demo">🎭 Démonstration</span>
            </div>
            <div class="info-row">
                <span>Service:</span>
                <span style="color: #007bff !important;">🎯 CSM.ai</span>
            </div>
        </div>
    </div>

    <script>
        // Get task ID from URL
        const urlParams = new URLSearchParams(window.location.search);
        const taskId = urlParams.get('taskId') || 'demo-task';
        document.getElementById('taskId').textContent = taskId;

        console.log('=== 3D VIEWER LOADED ===');
        console.log('Task ID:', taskId);
        console.log('URL:', window.location.href);

        function downloadModel() {
            console.log('Downloading 3D model...');
            alert('Téléchargement du modèle 3D démarré !\n\nTask ID: ' + taskId);
        }

        // Log success
        console.log('✅ 3D Viewer loaded successfully');
    </script>
</body>
</html>
