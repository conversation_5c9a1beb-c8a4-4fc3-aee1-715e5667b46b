package com.fed.platform.sinister.repository;

import java.time.Instant;
import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.fed.platform.sinister.domain.Contrat;

/**
 * Spring Data JPA repository for the Contrat entity.
 */
@Repository
public interface ContratRepository extends JpaRepository<Contrat, Long> {

    /**
     * Find contracts by immatriculation (case insensitive, partial match)
     */
    List<Contrat> findByImmatriculationContainingIgnoreCase(String immatriculation);

    /**
     * Find contracts by exact immatriculation
     */
    List<Contrat> findByImmatriculation(String immatriculation);

    /**
     * Find contracts by contract number
     */
    List<Contrat> findByNumContrat(String numContrat);

    /**
     * Find active contracts (current date between dateEffet and dateFin)
     */
    @Query("SELECT c FROM Contrat c WHERE :currentDate BETWEEN c.dateEffet AND c.dateFin")
    List<Contrat> findActiveContracts(@Param("currentDate") Instant currentDate);

    /**
     * Find contracts that cover a specific date
     */
    @Query("SELECT c FROM Contrat c WHERE :targetDate BETWEEN c.dateEffet AND c.dateFin")
    List<Contrat> findContractsCoveringDate(@Param("targetDate") Instant targetDate);

    /**
     * Find contracts by immatriculation that cover a specific date
     */
    @Query("SELECT c FROM Contrat c WHERE c.immatriculation = :immatriculation AND :targetDate BETWEEN c.dateEffet AND c.dateFin")
    List<Contrat> findByImmatriculationAndCoveringDate(@Param("immatriculation") String immatriculation,
            @Param("targetDate") Instant targetDate);

    /**
     * Find contracts by product type
     */
    List<Contrat> findByProduit(String produit);

    /**
     * Find contracts by immatriculation and product
     */
    List<Contrat> findByImmatriculationAndProduit(String immatriculation, String produit);
}