import { Component } from '@angular/core';

@Component({
  selector: 'app-test-3d',
  standalone: true,
  template: `
    <div style="padding: 50px; text-align: center; font-family: Arial;">
      <h1 style="color: green;">✅ SUCCESS!</h1>
      <h2>3D Component Loaded Successfully</h2>
      <p><strong>Session ID:</strong> {{sessionId}}</p>
      <p><strong>Current URL:</strong> {{currentUrl}}</p>
      <p><strong>Status:</strong> Component working perfectly!</p>
      
      <div style="margin: 30px 0; padding: 20px; background: #f0f8ff; border-radius: 10px;">
        <h3>🎉 No more login redirect!</h3>
        <p>This proves the routing is working correctly.</p>
      </div>
      
      <button onclick="window.close()" style="padding: 15px 30px; background: #3498db; color: white; border: none; border-radius: 5px; cursor: pointer; font-size: 16px;">
        Close Window
      </button>
    </div>
  `
})
export class Test3DComponent {
  sessionId = '';
  currentUrl = '';

  constructor() {
    const params = new URLSearchParams(window.location.search);
    this.sessionId = params.get('sessionId') || 'no-session';
    this.currentUrl = window.location.href;
    
    console.log('✅ Test 3D Component loaded successfully!');
    console.log('Session ID:', this.sessionId);
    console.log('URL:', this.currentUrl);
  }
}
