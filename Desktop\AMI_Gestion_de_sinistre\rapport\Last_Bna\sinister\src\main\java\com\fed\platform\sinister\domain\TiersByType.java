package com.fed.platform.sinister.domain;

import java.io.Serializable;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

@Entity
@Table(name = "tiers_by_type")
public class TiersByType implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Enumerated(EnumType.STRING)
    @Column(name = "type_tiers")
    private TypeTiers typeTiers;

    @Enumerated(EnumType.STRING)
    @Column(name = "compagnie_tiers")
    private CompagnieTiers compagnieTiers;

    @Enumerated(EnumType.STRING)
    @Column(name = "blesse_ou_decede")
    private StatutTiers blesseOuDecede;

    @Enumerated(EnumType.STRING)
    @Column(name = "organisme")
    private OrganismeTiers organisme;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public TypeTiers getTypeTiers() {
        return typeTiers;
    }

    public void setTypeTiers(TypeTiers typeTiers) {
        this.typeTiers = typeTiers;
    }

    public CompagnieTiers getCompagnieTiers() {
        return compagnieTiers;
    }

    public void setCompagnieTiers(CompagnieTiers compagnieTiers) {
        this.compagnieTiers = compagnieTiers;
    }

    public StatutTiers getBlesseOuDecede() {
        return blesseOuDecede;
    }

    public void setBlesseOuDecede(StatutTiers blesseOuDecede) {
        this.blesseOuDecede = blesseOuDecede;
    }

    public OrganismeTiers getOrganisme() {
        return organisme;
    }

    public void setOrganisme(OrganismeTiers organisme) {
        this.organisme = organisme;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof TiersByType)) {
            return false;
        }
        return id != null && id.equals(((TiersByType) o).id);
    }

    @Override
    public int hashCode() {
        return 31;
    }

    @Override
    public String toString() {
        return "TiersByType{" +
                "id=" + getId() +
                ", typeTiers='" + getTypeTiers() + "'" +
                ", compagnieTiers='" + getCompagnieTiers() + "'" +
                ", blesseOuDecede='" + getBlesseOuDecede() + "'" +
                ", organisme='" + getOrganisme() + "'" +
                "}";
    }
}
