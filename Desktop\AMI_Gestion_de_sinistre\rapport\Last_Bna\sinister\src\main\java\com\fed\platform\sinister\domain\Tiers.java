package com.fed.platform.sinister.domain;

import java.io.Serializable;
import java.time.Instant;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Size;

@Entity
@Table(name = "tiers")
public class Tiers implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Size(max = 30)
    @Column(name = "nom", length = 30)
    private String nom;

    @Size(max = 30)
    @Column(name = "prenom", length = 30)
    private String prenom;

    @Size(max = 20)
    @Column(name = "immatriculation_tiers", length = 20)
    private String immatriculationTiers;

    @Column(name = "num_permis")
    private String numPermis;

    @Column(name = "date_naissance")
    private Instant dateNaissance;

    @Column(name = "compagnie_assurance")
    private String compagnieAssurance;

    @Min(value = 0)
    @Max(value = 4)
    @Column(name = "responsabilite")
    private Integer responsabilite;

    @Enumerated(EnumType.STRING)
    @Column(name = "sexe_tiers")
    private Sexe sexeTiers;

    @Enumerated(EnumType.STRING)
    @Column(name = "nature")
    private NatureTiers nature;

    @Column(name = "description")
    private String description;

    @Column(name = "num_contrat")
    private Integer numContrat;

    @Column(name = "numdesinistre")
    private Integer numdesinistre;

    @Column(name = "date_obtention")
    private Instant dateObtention;

    @Column(name = "profession")
    private String profession;

    @Column(name = "nombre_dejour_derepos")
    private Integer nombreDejourDerepos;

    @Column(name = "taux_dincapacite")
    private Integer tauxDincapacite;

    @Column(name = "accompagnant_tiers")
    private String accompagnantTiers;

    @Column(name = "degre_de_prejudice_morale")
    private String degreDePrejudiceMorale;

    @Column(name = "sinistre_traite")
    private Boolean sinistreTraite;

    @Column(name = "les_ayant_droit")
    private String lesAyantDroit;

    @Column(name = "nombre_des_ayant_droit")
    private Integer nombreDesAyantDroit;

    @Column(name = "date_denaissance_des_ayant_droit")
    private Instant dateDenaissanceDesAyantDroit;

    @Column(name = "age_des_ayant_droit")
    private Integer ageDesAyantDroit;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getNom() {
        return nom;
    }

    public void setNom(String nom) {
        this.nom = nom;
    }

    public String getPrenom() {
        return prenom;
    }

    public void setPrenom(String prenom) {
        this.prenom = prenom;
    }

    public String getImmatriculationTiers() {
        return immatriculationTiers;
    }

    public void setImmatriculationTiers(String immatriculationTiers) {
        this.immatriculationTiers = immatriculationTiers;
    }

    public String getNumPermis() {
        return numPermis;
    }

    public void setNumPermis(String numPermis) {
        this.numPermis = numPermis;
    }

    public Instant getDateNaissance() {
        return dateNaissance;
    }

    public void setDateNaissance(Instant dateNaissance) {
        this.dateNaissance = dateNaissance;
    }

    public String getCompagnieAssurance() {
        return compagnieAssurance;
    }

    public void setCompagnieAssurance(String compagnieAssurance) {
        this.compagnieAssurance = compagnieAssurance;
    }

    public Integer getResponsabilite() {
        return responsabilite;
    }

    public void setResponsabilite(Integer responsabilite) {
        this.responsabilite = responsabilite;
    }

    public Sexe getSexeTiers() {
        return sexeTiers;
    }

    public void setSexeTiers(Sexe sexeTiers) {
        this.sexeTiers = sexeTiers;
    }

    public NatureTiers getNature() {
        return nature;
    }

    public void setNature(NatureTiers nature) {
        this.nature = nature;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getNumContrat() {
        return numContrat;
    }

    public void setNumContrat(Integer numContrat) {
        this.numContrat = numContrat;
    }

    public Integer getNumdesinistre() {
        return numdesinistre;
    }

    public void setNumdesinistre(Integer numdesinistre) {
        this.numdesinistre = numdesinistre;
    }

    public Instant getDateObtention() {
        return dateObtention;
    }

    public void setDateObtention(Instant dateObtention) {
        this.dateObtention = dateObtention;
    }

    public String getProfession() {
        return profession;
    }

    public void setProfession(String profession) {
        this.profession = profession;
    }

    public Integer getNombreDejourDerepos() {
        return nombreDejourDerepos;
    }

    public void setNombreDejourDerepos(Integer nombreDejourDerepos) {
        this.nombreDejourDerepos = nombreDejourDerepos;
    }

    public Integer getTauxDincapacite() {
        return tauxDincapacite;
    }

    public void setTauxDincapacite(Integer tauxDincapacite) {
        this.tauxDincapacite = tauxDincapacite;
    }

    public String getAccompagnantTiers() {
        return accompagnantTiers;
    }

    public void setAccompagnantTiers(String accompagnantTiers) {
        this.accompagnantTiers = accompagnantTiers;
    }

    public String getDegreDePrejudiceMorale() {
        return degreDePrejudiceMorale;
    }

    public void setDegreDePrejudiceMorale(String degreDePrejudiceMorale) {
        this.degreDePrejudiceMorale = degreDePrejudiceMorale;
    }

    public Boolean isSinistreTraite() {
        return sinistreTraite;
    }

    public void setSinistreTraite(Boolean sinistreTraite) {
        this.sinistreTraite = sinistreTraite;
    }

    public String getLesAyantDroit() {
        return lesAyantDroit;
    }

    public void setLesAyantDroit(String lesAyantDroit) {
        this.lesAyantDroit = lesAyantDroit;
    }

    public Integer getNombreDesAyantDroit() {
        return nombreDesAyantDroit;
    }

    public void setNombreDesAyantDroit(Integer nombreDesAyantDroit) {
        this.nombreDesAyantDroit = nombreDesAyantDroit;
    }

    public Instant getDateDenaissanceDesAyantDroit() {
        return dateDenaissanceDesAyantDroit;
    }

    public void setDateDenaissanceDesAyantDroit(Instant dateDenaissanceDesAyantDroit) {
        this.dateDenaissanceDesAyantDroit = dateDenaissanceDesAyantDroit;
    }

    public Integer getAgeDesAyantDroit() {
        return ageDesAyantDroit;
    }

    public void setAgeDesAyantDroit(Integer ageDesAyantDroit) {
        this.ageDesAyantDroit = ageDesAyantDroit;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof Tiers)) {
            return false;
        }
        return id != null && id.equals(((Tiers) o).id);
    }

    @Override
    public int hashCode() {
        return 31;
    }

    @Override
    public String toString() {
        return "Tiers{" +
                "id=" + getId() +
                ", nom='" + getNom() + "'" +
                ", prenom='" + getPrenom() + "'" +
                ", immatriculationTiers='" + getImmatriculationTiers() + "'" +
                ", numPermis='" + getNumPermis() + "'" +
                ", dateNaissance='" + getDateNaissance() + "'" +
                ", compagnieAssurance='" + getCompagnieAssurance() + "'" +
                ", responsabilite=" + getResponsabilite() +
                ", sexeTiers='" + getSexeTiers() + "'" +
                ", nature='" + getNature() + "'" +
                ", description='" + getDescription() + "'" +
                ", numContrat=" + getNumContrat() +
                ", numdesinistre=" + getNumdesinistre() +
                ", dateObtention='" + getDateObtention() + "'" +
                ", profession='" + getProfession() + "'" +
                ", nombreDejourDerepos=" + getNombreDejourDerepos() +
                ", tauxDincapacite=" + getTauxDincapacite() +
                ", accompagnantTiers='" + getAccompagnantTiers() + "'" +
                ", degreDePrejudiceMorale='" + getDegreDePrejudiceMorale() + "'" +
                ", sinistreTraite='" + isSinistreTraite() + "'" +
                ", lesAyantDroit='" + getLesAyantDroit() + "'" +
                ", nombreDesAyantDroit=" + getNombreDesAyantDroit() +
                ", dateDenaissanceDesAyantDroit='" + getDateDenaissanceDesAyantDroit() + "'" +
                ", ageDesAyantDroit=" + getAgeDesAyantDroit() +
                "}";
    }
}
