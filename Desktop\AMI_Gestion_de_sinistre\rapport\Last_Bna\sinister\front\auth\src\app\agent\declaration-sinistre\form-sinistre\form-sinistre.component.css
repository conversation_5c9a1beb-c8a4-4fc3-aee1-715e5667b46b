
body {
  background-color: #fbfafa;
  margin: 0;
  padding: 0;
  width: 100%;
  min-height: 300vh;
  font-family: '<PERSON><PERSON> Bold', sans-serif;
}

:host {
    display: block;
    font-family: '<PERSON>mer Bold', sans-serif;
    color: #0A1633;
  }
  
  /* Form container */

.form-container {
  display: flex;
  max-width: 2000px;
  margin: 1px ;
  padding: 30px;
}

@media (max-width: 900px) {
  .form-container {
    padding: 10px;
    overflow-x: auto;
  }
  .form-row, .form-row2, .tiers-container, .bareme-resp-container, .attachment-section, .footer-action-bar-container {
    width: 100vw;
    min-width: 0;
    max-width: 100vw;
    box-sizing: border-box;
    left: 0 !important;
    right: 0 !important;
    margin-left: 0;
    margin-right: 0;
  }
  .row-fields {
    flex-direction: column;
    gap: 10px;
  }
  .footer-action-bar-container {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
    padding: 8px 0;
  }
  .footer-btn {
    width: 100%;
    justify-content: center;
  }
  .attachment-section, .attachment-box, .attachment-doc-box {
    width: 100vw;
    min-width: 0;
    max-width: 100vw;
    box-sizing: border-box;
    padding-left: 0;
    padding-right: 0;
  }
}

@media (max-width: 600px) {
  .form-container {
    padding: 2px;
    overflow-x: auto;
  }
  .form-row, .form-row2, .tiers-container, .bareme-resp-container, .attachment-section, .footer-action-bar-container {
    width: 100vw;
    min-width: 0;
    max-width: 100vw;
    box-sizing: border-box;
    padding: 2px !important;
    left: 0 !important;
    right: 0 !important;
    margin-left: 0;
    margin-right: 0;
  }
  .row-fields {
    flex-direction: column;
    gap: 4px;
  }
  .tiers-title {
    font-size: 16px;
  }
  .footer-action-bar-container {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
    padding: 4px 0;
  }
  .footer-btn {
    width: 100%;
    justify-content: center;
    font-size: 16px;
    padding: 10px 0;
  }
  .attachment-section, .attachment-box, .attachment-doc-box {
    width: 100vw;
    min-width: 0;
    max-width: 100vw;
    box-sizing: border-box;
    padding-left: 0;
    padding-right: 0;
  }
}


  
  /* Header */
  .header h1 {
    font-family: 'Gilmer Bold', sans-serif;
    font-size: 25px;
   
    
    color: #0A1633;
    position: relative;
    left: 300px;
    bottom: 20px;
  }
  
  /* Progress steps */
  .progress-steps {
    width: 250px;
    margin-right: 50px;
    position: relative;
    right: 180px;
  }
  
  .step {
    position: relative;
    margin-bottom: 30px;
    padding-left: 40px;
  }
  
  .step-number {
    position: absolute;
    left: 0;
    top: 0;
    width: 24px;
    height: 24px;
    background-color: #B9C3DE;
    border-radius: 50%;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: 'Gilmer Bold', sans-serif;
    font-size: 16px;
  }
  
  .step.active .step-number {
    background-color: #1C3F93;
  }
  
  .step-title {
    font-family: 'Gilmer Bold', sans-serif;
    font-size: 16px;
    color: #B9C3DE;
    margin-bottom: 5px;
  }
  
  .step.active .step-title {
    color: #1C3F93;
  }
  
  .step-subtitle {
    font-family: 'Gilmer Bold', sans-serif;
    font-size: 13px;
    margin-top: 5px;
  }
  
  .step-subtitle .green {
    color: #009B79;
    display: block;
  }
  
  .step-subtitle .blue {
    color: #1C3F93;
    display: block;
  }
  

  .form-content {
    position: absolute;
    width: 880px;
    height: 185px;
    left: calc(50% - 440px + 2px); /* Center it with a 2px offset */
    top: 190px;
    background: white;
    border-radius: 16px;
    padding: 30px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  }
  
  
  .form-content h2 {
    font-family: 'Gilmer Bold', sans-serif;
    font-size: 20px;
   
    
    color: #1C3F93;
    margin-bottom: 30px;
  }

  
  .form-content2 h2 {
    font-family: 'Gilmer Bold', sans-serif;
    font-size: 20px;
  
    color: #1C3F93;
    margin-bottom: 30px;
  }
  
  /* Form rows */
  .form-row {
    display: flex;
    gap: 30px;
    margin-bottom: 20px;
  }
  .form-row2 {
    display: flex;
    gap: 30px;
    margin-bottom: 20px;
    position: absolute;
    width: 880px;
    height: 190px;
    left: calc(50% - 440px + 2px); /* Center it with a 2px offset */
    top: 190px;
    background: white;
    border-radius: 16px;
    padding: 30px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    transition: height 0.3s ease-in-out;
  }

  .form-row2-expanded {
    height: 900px !important;
  }
  
  /* Form groups */
  .form-group {
    flex: 1;
    margin-bottom: 20px;
  }
  
  .form-group.full-width {
    width: 100%;
  }
  
  .form-group label {
    display: block;
    font-family: 'Gilmer Bold', sans-serif;
    font-size: 14px;
    color: #6B6B6B;
    margin-bottom: 8px;
  }
  
  /* Input styles */
  input[type="text"],
  input[type="date"],
  input[type="time"],
  select,
  textarea {
    width: 100%;
    padding: 16px;
    border: 1px solid #DCDCDC;
    border-radius: 0 24px;
    font-family: 'Gilmer Bold', sans-serif;
    font-size: 14px;
    color: #B5B5B5;
    background: white;
  }
  
  textarea {
    height: 124px;
    resize: vertical;
  }
  
  .custom-select {
    position: relative;
  }
  
  .custom-select::after {
    content: "▼";
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    color: #868686;
    pointer-events: none;
  }
  
  /* Toggle switches */
  .toggle-switch {
    display: flex;
    align-items: center;
    gap: 10px;
  }
  
  .toggle-switch span {
    font-family: 'Gilmer Bold', sans-serif;
    font-size: 16px;
    color: #6B6B6B;
  }
  
  .switch {
    position: relative;
    display: inline-block;
    width: 40px;
    height: 20px;
  }
  
  .switch input {
    opacity: 0;
    width: 0;
    height: 0;
  }
  
  .slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #013888;
    transition: .4s;
    border-radius: 10px;
  }
  
  .slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 1px;
    bottom: 1px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
  }
  
  input:checked + .slider:before {
    transform: translateX(20px);
  }
  
  /* Document upload */
  .document-upload {
    margin-bottom: 30px;
  }
  
  .upload-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #F8F9FC;
    border: 1px solid white;
    border-radius: 16px;
    padding: 16px;
    margin-top: 8px;
  }
  
  .upload-info {
    display: flex;
    align-items: center;
    gap: 15px;
  }
  
  .document-icon {
    font-size: 24px;
    color: #1C3F93;
  }
  
  .upload-box p {
    font-family: 'Gilmer Bold', sans-serif;
    font-size: 14px;
    color: #8F8F8F;
    margin: 0;
  }
  
  .upload-button {
    background: #1C3F93;
    color: white;
    border: none;
    border-radius: 0 16px;
    padding: 16px;
    cursor: pointer;
    font-family: 'Gilmer Bold', sans-serif;
  }
  
  /* Photo upload */
  .photo-upload {
    margin-bottom: 30px;
  }
  
  .upload-area {
    background: #F8F9FC;
    border: 1px dashed #1C3F93;
    border-radius: 16px;
    padding: 30px;
    text-align: center;
    margin-top: 8px;
  }
  
  .upload-area p {
    font-family: 'Gilmer Bold', sans-serif;
    font-size: 14px;
    color: #8F8F8F;
    margin-bottom: 20px;
  }
  
  .photo-preview {
    display: flex;
    gap: 20px;
    justify-content: center;
  }
  
  .photo-placeholder {
    width: 100px;
    height: 100px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 16px;
    position: relative;
  }
  
  /* Form actions */

  
  .cancel-btn {
    background: white;
    color: #1C3F93;
    border: 1px solid #1C3F93;
    border-radius: 0 24px;
    padding: 16px 32px;
    cursor: pointer;
    font-family: 'Gilmer Bold', sans-serif;
  }
  
  .submit-btn {
    background: #1C3F93;
    color: white;
    border: none;
    border-radius: 0 24px;
    padding: 16px 32px;
    cursor: pointer;
    font-family: 'Gilmer Bold', sans-serif;
  }
  
  /* Responsive adjustments */
  @media (max-width: 768px) {
    .form-container {
      flex-direction: column;
    }
    
    .progress-steps {
      width: 100%;
      margin-right: 0;
      margin-bottom: 30px;
    }
    
    .form-row {
      flex-direction: column;
      gap: 15px;
    }
  }

  .toggle-group {
    position: relative;
    width: 336px;
    height: 63px;
    margin-bottom: 10px;
    bottom: 30px;
  }
  
  .toggle-group label {
    position: absolute;
    width: 336px;
    height: 19px;
    left: 0px;
    top: 0px;
    font-family: 'Gilmer Bold', sans-serif;
    font-style: normal;
   
    
    font-size: 16px;
    line-height: 120%;
    color: #6B6B6B;
  }
  
  .toggle-container {
    position: absolute;
    width: 176px;
    height: 20px;
    top: 43px;
    left: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  
  .toggle-option {
    font-family: 'Gilmer Bold', sans-serif;
    font-style: normal;
  
    font-size: 16px;
    line-height: 120%;
    color: #6B6B6B;
  }
  
  .toggle-button {
    width: 40px;
    height: 20px;
    background: #013888;
    border-radius: 10px;
    display: flex;
    align-items: center;
    padding: 1px 1px 1px 1px;
    cursor: pointer;
    position: relative;
  }
  
  .toggle-circle {
    width: 18px;
    height: 18px;
    background: #FFFFFF;
    border-radius: 50%;
    transition: transform 0.3s ease;
  }
  
  /* If toggle is ON (Oui selected) */
  .toggle-circle.toggle-on {
    transform: translateX(20px);
  }
  .light-blue-box {
    background-color: #d9f1ff; /* bleu ciel */
    border: 1px solid #DCDCDC;
    border-radius: 16px;
    padding: 16px;
  }

  .form-group2{
    position: relative;
    flex: 1;
    margin-bottom: 20px;
    top: 50px;

  }

  .form-group2 label {
    display: block;
    font-family: 'Gilmer Bold', sans-serif;
    font-size: 14px;
    color: #6B6B6B;
    margin-bottom: 8px;
  }

  .tiers-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 40px;
     position: relative;
     top: 150px;
     right: 40px;
  }
  

  
  .border-jemla {
    position: relative;
    border: solid 1px #F8F9FC;
    background-color: #F8F9FC;
    width: 832px;
    height: auto;
    border-radius: 16px;
    padding: 24px;
    box-sizing: border-box;
  }
  
  .border-jemla h2 {
    font-family: 'Gilmer Bold', sans-serif;
    font-size: 20px;
    color: #1C3F93;
    margin-bottom: 24px;
  }
  
  .row-fields {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
  }
  
  .field-group {
    flex: 1;
    min-width: 250px;
  }
  
  .field-group label {
    display: block;
    font-family: 'Gilmer Bold', sans-serif;
    font-size: 14px;
    color: #6B6B6B;
    margin-bottom: 8px;
  }
  
  .field-group input {
    width: 100%;
  }
  
  .field-group select {
    width: 100%;
  }
  
  .border-jemla svg {
    position: absolute;
    top: 16px;
    right: 16px;
  }
  
  @media (max-width: 768px) {
    .row-fields {
      flex-direction: column;
    }
  }
  
  .form-row3 {
    display: flex;
    gap: 30px;
    margin-bottom: 20px;
    background: white;
    border-radius: 16px;
    border: 1px solid white;
    position: relative;
    top: 180px;
    right: 30px;
    width: 880px;
    height: 176px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  }


  .form-group3{
    flex: 1;
  }
  .form-group3 label{
    display: block;
    font-family: 'Gilmer Bold', sans-serif;
    font-size: 14px;
    color: #6B6B6B;
    margin-bottom: 8px;
  }

  .tiers-title {
  font-size: 28px;
  font-family: 'Gilmer Bold', sans-serif;
  color: #1C3F93;
  margin-bottom: 18px;
  margin-top: 0;
}

/* Cas de baréme et responsabilité container styles */
  .bareme-resp-container {
    margin-top: 120px;
    background: white;
    border-radius: 16px;
    border: 1px solid white;
    box-shadow: 0 4px 12px rgba(0,0,0,0.05);
    width: 880px;
    height: 275px;
    position: relative;
    padding: 0;
    right: 30px;
    transition: margin-top 0.3s ease-in-out;
  }

  .bareme-resp-expanded {
    margin-top: 200px !important;
  }
  .bareme-resp-title {
    width: 100%;
    font-size: 20px;
    font-family: 'Gilmer Bold', sans-serif;
    color: #1C3F93;
    margin: 24px 0 0 30px;
    margin-bottom: 18px;
  }
  .bareme-resp-fields {
    display: flex;
    gap: 30px;
    padding: 0 30px;
    width: 100%;
    align-items: flex-start;
  }
  .bareme-degat-label {
    margin-top: 12px;
    display: block;
  }
/* Attachment Section */
.attachment-section {
  margin: 32px auto 0 auto;
  width: 880px;
  background: transparent;
  border-radius: 24px;
  position: relative;
  padding: 0;
  box-shadow: none;
  right: 30px;
}
.attachment-title {
  font-size: 22px;
  font-family: 'Gilmer Bold', sans-serif;
  color: #1C3F93;
  margin-bottom: 18px;
  margin-left: 4px;
  letter-spacing: 0.5px;
}
.attachment-box {
  background: #fff;
  border-radius: 24px;
  box-shadow: 0 4px 16px rgba(28,63,147,0.08);
  padding: 28px 32px 18px 32px;
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 18px;
}
.attachment-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  margin-bottom: 0;
}
.attachment-label {
  font-family: 'Gilmer Bold', sans-serif;
  font-size: 16px;
  color: #323232;
  margin-bottom: 4px;
}
.attachment-note {
  font-family: 'Gilmer Bold', sans-serif;
  font-size: 14px;
  color: #8F8F8F;
  margin-top: 2px;
}
.attachment-upload-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  background: #1C3F93;
  border-radius: 0 16px 16px 0;
  width: 48px;
  height: 48px;
  border: none;
  cursor: pointer;
  margin-left: 16px;
  box-shadow: none;
  padding: 0;
  transition: background 0.2s;
}
.attachment-upload-btn:hover {
  background: #174baf;
}
.icon-paperclip {
  width: 18px;
  height: 18px;
  display: inline-block;
  background: url("data:image/svg+xml;utf8,<svg fill='none' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'><path d='M11.06 7.06L6.53 11.59a2.5 2.5 0 11-3.54-3.54l6.36-6.36a3.5 3.5 0 115 4.95l-7.07 7.07a4.5 4.5 0 01-6.36-6.36l7.78-7.78' stroke='%23E6F5F2' stroke-width='1.5'/></svg>") center/contain no-repeat;
}
.attachment-doc-box {
  margin-top: 8px;
  background: #fff;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 20px;
  box-shadow: 0 2px 6px rgba(0,0,0,0.04);
  border: 1px solid #F8F9FC;
  width: 100%;
  min-height: 48px;
}
.attachment-doc-label {
  font-family: 'Gilmer Bold', sans-serif;
  font-size: 16px;
  color: #323232;
  margin-right: 12px;
}
.required {
  color: #E53935;
  font-weight: bold;
  margin-left: 2px;
}

/* Error message styles */
.error-message {
  color: #E53935;
  font-family: 'Gilmer Bold', sans-serif;
  font-size: 12px;
  margin-top: 4px;
  display: block;
}

/* 3D Model Upload Styles */
.upload-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}

.btn-3d-generate {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 8px 16px;
  font-family: 'Gilmer Medium', sans-serif;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.btn-3d-generate:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.btn-3d-generate:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.icon-3d {
  font-size: 14px;
}

/* Generation Progress */
.generation-progress {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-top: 10px;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.progress-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #013888;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Photo Preview Styles */
.photo-preview-container {
  margin-top: 15px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.photo-preview-container h4 {
  margin: 0 0 15px 0;
  color: #2c3e50;
  font-family: 'Gilmer Bold', sans-serif;
  font-size: 14px;
}

.photo-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 15px;
}

.photo-item {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
}

.photo-item:hover {
  transform: translateY(-2px);
}

.photo-thumbnail {
  width: 100%;
  height: 80px;
  object-fit: cover;
  border-bottom: 1px solid #e9ecef;
}

.photo-info {
  padding: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.photo-name {
  font-size: 11px;
  color: #6c757d;
  font-family: 'Gilmer Medium', sans-serif;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 70px;
}

.photo-remove-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 2px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.photo-remove-btn:hover {
  background: #f8d7da;
}

.icon-trash {
  font-size: 12px;
}
.attachment-doc-filename {
  display: block;
  margin-top: 4px;
  font-family: 'Roboto', sans-serif;
  font-size: 14px;
  color: #8F8F8F;
  margin-left: 16px;
}
.attachment-doc-delete {
  background: #fff;
  border: 1px solid #E53935;
  color: #E53935;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  margin-left: 16px;
  transition: background 0.2s;
}
.attachment-doc-delete:hover {
  background: #FFE5E5;
}
.icon-trash {
  width: 18px;
  height: 18px;
  display: inline-block;
  background: url("data:image/svg+xml;utf8,<svg fill='none' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'><path d='M3 6h18M9 6V4a1 1 0 011-1h4a1 1 0 011 1v2m2 0v14a2 2 0 01-2 2H7a2 2 0 01-2-2V6h14z' stroke='%23E53935' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/></svg>") center/contain no-repeat;
}
.photo-upload-area {
  margin-top: 16px;
  border: 1.5px dashed #1C3F93;
  border-radius: 16px;
  background: #F8F9FC;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 68px;
  width: 100%;
  text-align: center;
  padding: 18px 0;
}
.photo-upload-label {
  font-family: 'Gilmer Bold', sans-serif;
  font-size: 18px;
  color: #323232;
  margin-bottom: 2px;
}
.photo-upload-note {
  font-family: 'Roboto', sans-serif;
  font-size: 14px;
  color: #8F8F8F;
 
}
.photo-preview-row {
  display: flex;
  gap: 24px;
  margin-top: 12px;
  margin-left: 8px;
}
.photo-preview-img {
  width: 100px;
  height: 100px;
  border-radius: 16px;
  object-fit: cover;
  background: #eee;
  border: 2px solid #fff;
  box-shadow: 0 2px 6px rgba(0,0,0,0.08);
}

.footer-action-bar-container {
  width:880px;
  max-width: 880px;
  min-height: 92px;
  background: #fff;
  border-radius: 24px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 24px;
  margin-top: 24px;
  box-shadow: 0 2px 8px #F4F4F4;
  padding: 0 32px 0 0;
  position: relative;
  right: 30px;
}
.footer-btn {
  font-family: 'Gilmer Bold', sans-serif;
  font-size: 18px;
  border: none;
  outline: none;
  background: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: color 0.2s, background 0.2s;
  height: 48px;
  padding: 0 16px;
  margin: 0;
}
.footer-btn-retour {
  color: #1C3F93;
  background: none;
  border-radius: 0 24px 24px 0;
  
}
.footer-btn-retour:hover {
  text-decoration: underline;
  background: #F4F4F4;
}
.footer-btn-continuer {
  color: #fff;
  background: #00A887;
  width: 135px;
  height: 48px;
  border-top-right-radius: 24px;
  border-bottom-left-radius: 24px;
  border-top-left-radius: 0;
  border-bottom-right-radius: 0;
 
  
  box-shadow: none;
  padding: 0 16px;
  gap: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.footer-btn-continuer:hover {
  background: #00916f;
}
.footer-btn-icon-left {
  font-size: 22px;
  margin-right: 6px;
  line-height: 1;
}
.footer-btn-icon-right {
  font-size: 22px;
  margin-left: 6px;
  line-height: 1;
}
@media (max-width: 900px) {
  .footer-action-bar-container {
    width: 98vw;
    min-height: auto;
    border-radius: 18px;
    padding: 0 8px;
    justify-content: center;
  }
  .footer-btn {
    width: 90vw;
    justify-content: center;
  }
}

/* Document Management Styles */
.available-documents {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.available-documents h3 {
  font-family: 'Gilmer Bold', sans-serif;
  font-size: 16px;
  color: #1C3F93;
  margin-bottom: 10px;
}

.available-doc-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  margin: 5px 0;
  background-color: white;
  border-radius: 6px;
  border: 1px solid #dee2e6;
}

.doc-name {
  font-weight: 600;
  color: #333;
}

.doc-path {
  font-size: 12px;
  color: #6c757d;
  font-style: italic;
}

.uploaded-documents {
  margin-top: 20px;
}

.uploaded-documents h3 {
  font-family: 'Gilmer Bold', sans-serif;
  font-size: 16px;
  color: #1C3F93;
  margin-bottom: 15px;
}

.attachment-note {
  font-size: 12px;
  color: #6c757d;
  margin-top: 4px;
}

.required {
  color: #dc3545;
  font-weight: bold;
}

/* 3D Damage Selection Styles */
.damage-3d-section {
  margin-top: 20px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.damage-3d-section h3 {
  font-family: 'Gilmer Bold', sans-serif;
  font-size: 18px;
  color: #1C3F93;
  margin-bottom: 15px;
}

.damage-3d-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background-color: white;
  border-radius: 6px;
  border: 1px solid #dee2e6;
  margin-bottom: 15px;
}

.damage-3d-info {
  flex: 1;
}

.damage-3d-label {
  font-weight: 600;
  color: #333;
  font-size: 16px;
}

.damage-3d-note {
  font-size: 12px;
  color: #6c757d;
  margin-top: 4px;
}

.damage-3d-btn {
  background: linear-gradient(135deg, #1C3F93, #099e8a);
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
}

.damage-3d-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(28, 63, 147, 0.3);
}

.damage-3d-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
  opacity: 0.6;
}

.icon-3d {
  font-size: 16px;
}

.selected-damages {
  background-color: white;
  border-radius: 6px;
  border: 1px solid #dee2e6;
  padding: 15px;
}

.selected-damages h4 {
  font-family: 'Gilmer Bold', sans-serif;
  font-size: 16px;
  color: #1C3F93;
  margin-bottom: 10px;
}

.damage-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  margin: 8px 0;
  background-color: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

.damage-info {
  font-weight: 600;
  color: #333;
}

.damage-coords {
  font-size: 12px;
  color: #6c757d;
  font-family: monospace;
}

.damage-remove {
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  cursor: pointer;
  font-size: 14px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
}

.damage-remove:hover {
  background: #c82333;
}
