import { Routes, provideRouter } from '@angular/router';
import { ConducteurComponent } from '../agent/declaration-sinistre/conducteur/conducteur.component';
import { DeclarationSinistreComponent } from '../agent/declaration-sinistre/declaration-sinistre.component';
import { DocumentsComponent } from '../agent/declaration-sinistre/documents/documents.component';
import { RecapitulatifComponent } from '../agent/declaration-sinistre/recapitulatif/recapitulatif.component';
import { RechercheContratComponent } from '../agent/declaration-sinistre/recherche-contrat/recherche-contrat.component';
import { SurvenanceSinistreComponent } from '../agent/declaration-sinistre/survenance-sinistre/survenance-sinistre.component';
import { TiersComponent } from '../agent/declaration-sinistre/tiers/tiers.component';
import { BackofficeComponent } from '../backoffice/backoffice/backoffice.component';
import { CasDeBaremeComponent } from '../backoffice/backoffice/cas-de-bareme/cas-de-bareme.component';
import { EvenementsComponent } from '../backoffice/backoffice/evenements/evenements.component';
import { DocumentManagementComponent } from '../backoffice/backoffice/gestion-doc/gestion-doc.component';
import { NatureTiersComponent } from '../backoffice/backoffice/nature-tiers/nature-tiers.component';
import { TypeDeDegatComponent } from '../backoffice/backoffice/type-de-degat/type-de-degat.component';
import { LoaderComponent } from '../loader/loader.component';
import { LoginComponent } from './login/login.component';

export const authRoutes: Routes = [
  { path: '', component: LoaderComponent },
  { path: 'login', component: LoginComponent },
  { path: 'sinistre', component: DeclarationSinistreComponent },
  { path: 'recherche-contrat', component: RechercheContratComponent },
  { path: 'survenance-sinistre', component: SurvenanceSinistreComponent },
  { path: 'conducteur', component: ConducteurComponent },
  { path: 'tiers', component: TiersComponent },
  { path: 'documents', component: DocumentsComponent },
  { path: 'recap', component: RecapitulatifComponent },
  { 
    path: 'backoffice', 
    component: BackofficeComponent,
    children: [
      { path: 'evenement', component: EvenementsComponent },
      { path: 'type-de-degat', component: TypeDeDegatComponent },
      { path: 'cas-de-bareme', component: CasDeBaremeComponent },
      { path: 'gestion-doc', component: DocumentManagementComponent },
      { path: 'nature-tiers', component: NatureTiersComponent }
    ]
  },
  {
    path: '3d-model',
    loadComponent: () => import('../3d-viewer.component').then(m => m.Simple3DViewerComponent)
  },
  { path: '**', redirectTo: 'login' }
];

export const authRouting = provideRouter(authRoutes);