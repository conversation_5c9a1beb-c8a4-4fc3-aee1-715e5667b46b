-- ========================================
-- MISE À JOUR DE LA TABLE EVENEMENT
-- ========================================
-- Exécuter ce script pour ajouter les nouvelles colonnes

-- 1. Ajouter les nouvelles colonnes
ALTER TABLE evenement
ADD COLUMN type_de_degat VARCHAR(50),
ADD COLUMN tier BOOLEAN DEFAULT FALSE,
ADD COLUMN responsabilite INT;

-- 2. Vérification de la structure mise à jour
DESCRIBE evenement;

-- 3. Données de test (optionnel)
INSERT INTO evenement (code, libelle, type_de_degat, tier, responsabilite) VALUES
('COL_VEH', 'Collision avec véhicule', 'MATERIEL', TRUE, 2),
('VOL_VEH', 'Vol de véhicule', 'MATERIEL', FALSE, 0),
('ACC_CORP', 'Accident corporel', 'CORPOREL', TRUE, 3),
('BRI_GLACE', 'Bris de glace', 'MATERIEL', FALSE, 1),
('INCENDIE', 'Incendie véhicule', 'MAT_CORP', FALSE, 4);

-- 4. Vérification des données
SELECT * FROM evenement;

-- ========================================
-- COMMENTAIRES
-- ========================================
-- type_de_degat: Type de dégât (MATERIEL, CORPOREL, MAT_CORP)
-- tier: Présence de tiers (TRUE/FALSE)
-- responsabilite: Niveau de responsabilité (0-4)
-- ========================================
