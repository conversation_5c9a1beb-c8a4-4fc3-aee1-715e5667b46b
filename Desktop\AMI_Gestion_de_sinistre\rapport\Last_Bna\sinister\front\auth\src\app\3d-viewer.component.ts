import { CommonModule } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { AfterViewInit, Component, ElementRef, inject, ViewChild } from '@angular/core';

import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js';
@Component({
  selector: 'app-3d-viewer',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="viewer-container">
      <!-- Enhanced Header with Project Styling -->
      <nav class="navbar">
        <div class="nav-content">
          <div class="nav-left">
            <div class="logo">
              <span class="logo-icon">🚗</span>
              <span class="logo-text">3D Vehicle Viewer</span>
            </div>
            <div class="nav-title">
              <h1>{{ isDamageMode ? 'Sélection des Dommages 3D' : 'Visualiseur 3D Avancé' }}</h1>
              <p>{{ isDamageMode ? 'Cliquez sur le modèle pour marquer les zones endommagées' : 'Session: ' + sessionId }}</p>
            </div>
          </div>
          <div class="nav-right">
            <button class="close-btn" onclick="window.close()">
              <span class="close-icon">✕</span>
              Fermer
            </button>
          </div>
        </div>
      </nav>

      <!-- Enhanced Loading Section -->
      <div id="loadingDiv" class="loading-container">
        <div class="loading-content">
          <div class="loading-spinner">
            <div class="spinner-ring"></div>
            <div class="spinner-ring"></div>
            <div class="spinner-ring"></div>
          </div>
          <div class="loading-text">
            <h2>{{message}}</h2>
            <p class="progress-text">Progression: {{progress}}%</p>
          </div>
          <div class="progress-container">
            <div class="progress-bar">
              <div class="progress-fill" [style.width.%]="progress"></div>
              <div class="progress-glow" [style.width.%]="progress"></div>
            </div>
          </div>
          <div class="loading-info">
            <div class="info-item">
              <span class="info-icon">🔧</span>
              <span>Génération du modèle 3D en cours...</span>
            </div>
            <div class="info-item">
              <span class="info-icon">⚡</span>
              <span>Optimisation des textures et matériaux</span>
            </div>
          </div>
        </div>

        <div id="errorDiv" class="error-container" style="display: none;">
          <div class="error-content">
            <span class="error-icon">⚠️</span>
            <p class="error-message">{{error}}</p>
            <button (click)="retryPolling()" class="retry-btn">
              <span class="retry-icon">🔄</span>
              Réessayer
            </button>
          </div>
        </div>
      </div>

      <!-- Enhanced 3D Viewer -->
      <div id="viewerDiv" class="viewer-main" style="display: none;">
        <!-- 3D Canvas Area -->
        <div class="canvas-section">
          <div class="canvas-container">
            <div class="canvas-header">
              <h3 class="canvas-title">
                <span class="title-icon">🎯</span>
                Visualiseur 3D Véhicule
              </h3>
              <div class="canvas-controls">
                <button class="control-btn" (click)="resetCamera()">
                  <span class="btn-icon">🔄</span>
                  Reset Vue
                </button>
                <button class="control-btn" (click)="toggleWireframe()">
                  <span class="btn-icon">🔲</span>
                  Wireframe
                </button>
              </div>
            </div>
            <div style="display: flex; flex-direction: column; width: 100%; height: 100%;">
              <div #threeContainer class="three-container">
                <!-- 3D Scene will be rendered here -->
              </div>
              <button class="control-btn" (click)="toggleWireframe()">
                <span class="btn-icon">🔲</span>
                Wireframe
              </button>
              <div class="canvas-footer">
                <div class="controls-info">
                  <div class="control-item">
                    <span class="control-icon">🖱️</span>
                    <span>Rotation: Clic gauche + glisser</span>
                  </div>
                  <div class="control-item">
                    <span class="control-icon">🔍</span>
                    <span>Zoom: Molette de la souris</span>
                  </div>
                  <div class="control-item">
                    <span class="control-icon">📍</span>
                    <span>Marquer dommage: Clic sur le modèle</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Enhanced Damage Panel -->
        <div class="damage-section">
          <div class="damage-container">
            <div class="damage-header">
              <h3 class="damage-title">
                <span class="title-icon">📋</span>
                Points de Dommage
                <span class="damage-count" id="damageCount">0</span>
              </h3>
            </div>

            <div class="damage-content">
              <div id="damagesList" class="damages-list"></div>

              <div id="actionsDiv" class="damage-actions" style="display: none;">
                <button (click)="clearAll()" class="action-btn clear-btn">
                  <span class="btn-icon">🗑️</span>
                  {{ isDamageMode ? 'Effacer Tout' : 'Effacer Tout' }}
                </button>
                <button (click)="saveClaims()" class="action-btn save-btn">
                  <span class="btn-icon">💾</span>
                  {{ isDamageMode ? 'Sauvegarder et Retourner' : 'Sauvegarder' }}
                </button>
              </div>
            </div>

            <!-- Enhanced Session Info -->
            <div class="session-info">
              <div class="info-header">
                <span class="info-icon">ℹ️</span>
                <span class="info-title">Informations de Session</span>
              </div>
              <div class="info-content">
                <div class="info-row">
                  <span class="info-label">Session:</span>
                  <span class="info-value">{{sessionId}}</span>
                </div>
                <div class="info-row">
                  <span class="info-label">Statut:</span>
                  <span class="info-value status-{{status.toLowerCase()}}">{{status}}</span>
                </div>
                <div class="info-row">
                  <span class="info-label">Modèle:</span>
                  <span class="info-value">{{modelType}}</span>
                </div>
                <div id="csmStatusP" class="info-row" style="display: none;">
                  <span class="info-label">CSM.ai:</span>
                  <span id="csmStatusSpan" class="info-value"></span>
                </div>
                <div id="glbUrlP" class="info-row" style="display: none;">
                  <span class="info-label">Modèle 3D:</span>
                  <a id="glbUrlLink" target="_blank" class="download-link">
                    <span class="link-icon">📥</span>
                    Télécharger GLB
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <style>
      /* Enhanced 3D Viewer Styling */
      .viewer-container {
        width: 100vw;
        height: 100vh;
        background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%);
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        overflow: hidden;
        position: relative;
      }

      .viewer-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background:
          radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
          radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.15) 0%, transparent 50%),
          radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.1) 0%, transparent 50%);
        pointer-events: none;
      }

      /* Enhanced Navbar */
      .navbar {
        background: linear-gradient(90deg, #1e3c72 0%, #2a5298 100%);
        backdrop-filter: blur(10px);
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        position: relative;
        z-index: 1000;
      }

      .nav-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px 30px;
        max-width: 1400px;
        margin: 0 auto;
      }

      .nav-left {
        display: flex;
        align-items: center;
        gap: 30px;
      }

      .logo {
        display: flex;
        align-items: center;
        gap: 12px;
      }

      .logo-icon {
        font-size: 28px;
        filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.5));
      }

      .logo-text {
        font-size: 18px;
        font-weight: 600;
        color: #ffffff;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
      }

      .nav-title h1 {
        margin: 0;
        font-size: 24px;
        font-weight: 700;
        color: #ffffff;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
      }

      .nav-title p {
        margin: 5px 0 0 0;
        font-size: 14px;
        color: rgba(255, 255, 255, 0.8);
      }

      .close-btn {
        background: linear-gradient(45deg, #e74c3c, #c0392b);
        color: white;
        border: none;
        padding: 12px 20px;
        border-radius: 8px;
        cursor: pointer;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 8px;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
      }

      .close-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(231, 76, 60, 0.4);
      }

      .close-icon {
        font-size: 16px;
      }

      /* Enhanced Loading Styles */
      .loading-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: calc(100vh - 100px);
        position: relative;
        z-index: 100;
      }

      .loading-content {
        text-align: center;
        background: rgba(255, 255, 255, 0.05);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 20px;
        padding: 40px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
      }

      .loading-spinner {
        position: relative;
        width: 80px;
        height: 80px;
        margin: 0 auto 30px;
      }

      .spinner-ring {
        position: absolute;
        width: 100%;
        height: 100%;
        border: 3px solid transparent;
        border-radius: 50%;
        animation: spin 2s linear infinite;
      }

      .spinner-ring:nth-child(1) {
        border-top-color: #3498db;
        animation-delay: 0s;
      }

      .spinner-ring:nth-child(2) {
        border-right-color: #e74c3c;
        animation-delay: 0.5s;
        width: 70%;
        height: 70%;
        top: 15%;
        left: 15%;
      }

      .spinner-ring:nth-child(3) {
        border-bottom-color: #f39c12;
        animation-delay: 1s;
        width: 40%;
        height: 40%;
        top: 30%;
        left: 30%;
      }

      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }

      .loading-text h2 {
        color: #ffffff;
        font-size: 24px;
        font-weight: 600;
        margin: 0 0 10px 0;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
      }

      .progress-text {
        color: rgba(255, 255, 255, 0.8);
        font-size: 16px;
        margin: 0 0 20px 0;
      }

      .progress-container {
        width: 300px;
        margin: 0 auto 30px;
      }

      .progress-bar {
        width: 100%;
        height: 8px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 4px;
        overflow: hidden;
        position: relative;
      }

      .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #3498db, #2ecc71);
        border-radius: 4px;
        transition: width 0.3s ease;
        position: relative;
      }

      .progress-glow {
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
        border-radius: 4px;
        animation: glow 2s ease-in-out infinite;
      }

      @keyframes glow {
        0%, 100% { transform: translateX(-100%); }
        50% { transform: translateX(300px); }
      }

      .loading-info {
        display: flex;
        flex-direction: column;
        gap: 15px;
      }

      .info-item {
        display: flex;
        align-items: center;
        gap: 10px;
        color: rgba(255, 255, 255, 0.9);
        font-size: 14px;
      }

      .info-icon {
        font-size: 18px;
      }

      /* Enhanced 3D Viewer Main Layout */
      .viewer-main {
        display: flex;
        flex-direction: column; /* Modified to column layout */
        height: 100vh; /* Modified to full height */
        width: 100vw; /* Modified to full width */
        gap: 0px; /* Reduced gap */
        padding: 0px; /* Reduced padding */
        position: relative;
        z-index: 100;
      }

      /* Canvas Section */
      .canvas-section {
        flex: 1; /* Take remaining space */
        min-width: 0;
      }

      .viewer-main {
        display: flex;
        flex-direction: column; /* Modified to column layout */
        height: 100vh; /* Modified to full height */
        width: 100vw; /* Modified to full width */
        gap: 0px; /* Reduced gap */
        padding: 0px; /* Reduced padding */
        position: relative;
        z-index: 100;
        overflow: auto; /* Add scroll if content overflows */
      }

      .canvas-container {
        background: rgba(255, 255, 255, 0.05);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 20px;
        padding: 20px;
        height: 100%;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        display: flex;
        flex-direction: column;
      }

      .canvas-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 15px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      }

      .canvas-title {
        margin: 0;
        color: #ffffff;
        font-size: 20px;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 10px;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
      }

      .title-icon {
        font-size: 24px;
      }

      .canvas-controls {
        display: flex;
        gap: 10px;
      }

      .control-btn {
        background: linear-gradient(45deg, #3498db, #2980b9);
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 8px;
        cursor: pointer;
        font-size: 14px;
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 6px;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
      }

      .control-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
      }

      .btn-icon {
        font-size: 16px;
      }

      /* Three.js Container */
      .three-container {
        width: 100%;
        height: 100%;
        border: 2px solid rgba(52, 152, 219, 0.3);
        border-radius: 12px;
        background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 100%);
        position: relative;
        overflow: hidden;
        box-shadow: inset 0 4px 20px rgba(0, 0, 0, 0.5);
        display: block;
      }

      .canvas-footer {
        margin-top: 15px;
        padding-top: 15px;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
      }

      .controls-info {
        display: flex;
        justify-content: space-around;
        flex-wrap: wrap;
        gap: 15px;
      }

      .control-item {
        display: flex;
        align-items: center;
        gap: 8px;
        color: rgba(255, 255, 255, 0.8);
        font-size: 12px;
        background: rgba(255, 255, 255, 0.05);
        padding: 8px 12px;
        border-radius: 8px;
        border: 1px solid rgba(255, 255, 255, 0.1);
      }

      .control-icon {
        font-size: 16px;
      }
    </style>
  `
})
export class Simple3DViewerComponent implements AfterViewInit {
  @ViewChild('threeContainer', { static: false }) threeContainer!: ElementRef;

  sessionId = '';
  formId = '';
  callbackUrl = '';
  isDamageMode = false;
  isLoading = true;
  progress = 0;
  message = 'Loading 3D model...';
  error = '';
  status = 'Initializing';
  modelType = 'Demo';
  damages: any[] = [];
  glbUrl = '';
  csmStatus = '';

  // Platform check for browser-only operations
  private isBrowser: boolean = false;

  // Three.js properties
  private scene!: THREE.Scene;
  private camera!: THREE.PerspectiveCamera;
  private renderer!: THREE.WebGLRenderer;
  private controls!: OrbitControls;
  private loader = new GLTFLoader();
  private raycaster = new THREE.Raycaster();
  private mouse = new THREE.Vector2();
  private vehicleModel: THREE.Group | null = null;
  private damageMarkers: THREE.Mesh[] = [];

  private http = inject(HttpClient);

  constructor() {
    // Check if we're in browser environment
    this.isBrowser = typeof window !== 'undefined';

    if (this.isBrowser) {
      // Get parameters from URL (only in browser)
      const params = new URLSearchParams(window.location.search);
      this.sessionId = params.get('sessionId') || 'demo';
      this.formId = params.get('formId') || '';
      this.callbackUrl = params.get('callbackUrl') || '';

      // Check if this is damage selection mode
      this.isDamageMode = !!(this.formId && this.callbackUrl);

      console.log('🚗 3D Viewer started for session:', this.sessionId);
      console.log('🆔 Form ID:', this.formId);
      console.log('🔗 Callback URL:', this.callbackUrl);
      console.log('📍 Damage Mode:', this.isDamageMode);

      this.startProcess();
    } else {
      // SSR fallback
      console.log('⚠️ SSR environment detected, using fallback values');
      this.sessionId = 'demo';
      this.formId = '';
      this.callbackUrl = '';
      this.isDamageMode = false;
    }
  }

  ngAfterViewInit() {
    // Only initialize Three.js in browser environment
    if (this.isBrowser) {
      this.initThreeJS();

      // Start the process if not already started
      if (!this.sessionId || this.sessionId === 'demo') {
        this.startProcess();
      }
    } else {
      console.log('⚠️ Skipping Three.js initialization in SSR environment');
    }
  }

  initThreeJS() {
    const container = this.threeContainer.nativeElement;

    // Wait for container to be properly sized
    setTimeout(() => {
      let width = container.clientWidth;
      let height = container.clientHeight;

      console.log('🔧 Three.js container info:');
      console.log('Container element:', container);
      console.log('Container width:', width);
      console.log('Container height:', height);

      if (width === 0 || height === 0) {
        console.log('⚠️ Container has zero size, using parent dimensions...');

        // Try to get parent container size
        const parentElement = container.parentElement;
        if (parentElement) {
          width = parentElement.clientWidth - 40; // Account for padding
          height = parentElement.clientHeight - 100; // Account for header/footer
          console.log('� Using parent dimensions:', width, 'x', height);
        }

        // Fallback to reasonable defaults
        if (width <= 0) width = 800;
        if (height <= 0) height = 600;

        // Set container size explicitly
        container.style.width = width + 'px';
        container.style.height = height + 'px';
        container.style.display = 'block';

        console.log('🔧 Set container size to:', width, 'x', height);
      }

      // Ensure we have valid dimensions
      const renderWidth = Math.max(width, 400);
      const renderHeight = Math.max(height, 300);
      console.log('🔧 Final renderer dimensions:', renderWidth, 'x', renderHeight);

      this.setupThreeJSScene(renderWidth, renderHeight);
    }, 100); // Small delay to ensure DOM is ready
  }

  setupThreeJSScene(width: number, height: number) {
    const container = this.threeContainer.nativeElement;

    // Enhanced Scene with Realistic Space Background
    this.scene = new THREE.Scene();

    // Create realistic space background
    this.createSpaceBackground();

    // Add fog for depth
    this.scene.fog = new THREE.Fog(0x0a0a0a, 50, 200);
      const renderWidth = Math.max(width, 400);
      const renderHeight = Math.max(height, 300);
    // Camera
    this.camera = new THREE.PerspectiveCamera(75, renderWidth / renderHeight, 0.1, 1000);
    this.camera.position.set(5, 5, 5);

    // Renderer
    this.renderer = new THREE.WebGLRenderer({ antialias: true });
    this.renderer.setSize(renderWidth, renderHeight);
    this.renderer.shadowMap.enabled = true;
    this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    container.appendChild(this.renderer.domElement);

    // Controls
    this.controls = new OrbitControls(this.camera, this.renderer.domElement);
    this.controls.enableDamping = true;
    this.controls.dampingFactor = 0.05;

    // Enhanced Professional Lighting Setup
    this.setupAdvancedLighting();

    // Ground
    const groundGeometry = new THREE.PlaneGeometry(20, 20);
    const groundMaterial = new THREE.MeshLambertMaterial({ color: 0xcccccc });
    const ground = new THREE.Mesh(groundGeometry, groundMaterial);
    ground.rotation.x = -Math.PI / 2;
    ground.receiveShadow = true;
    this.scene.add(ground);

    // Mouse events for damage marking
    this.renderer.domElement.addEventListener('click', (event) => this.onMouseClick(event));

    // Scene is ready for 3D model loading
    console.log('✅ Three.js scene initialized and ready for 3D model');

    // Start animation loop
    this.animate();

    console.log('✅ Three.js initialized with test cube');
  }

  showViewer() {
    const loadingDiv = document.getElementById('loadingDiv');
    const viewerDiv = document.getElementById('viewerDiv');
    if (loadingDiv) loadingDiv.style.display = 'none';
    if (viewerDiv) viewerDiv.style.display = 'flex';
  }

  showError() {
    const errorDiv = document.getElementById('errorDiv');
    if (errorDiv) errorDiv.style.display = 'block';
  }

  updateDamagesList() {
    const damagesList = document.getElementById('damagesList');
    const damageCount = document.getElementById('damageCount');
    const actionsDiv = document.getElementById('actionsDiv');

    if (damageCount) damageCount.textContent = this.damages.length.toString();

    if (damagesList) {
      damagesList.innerHTML = '';
      this.damages.forEach((damage, index) => {
        const damageDiv = document.createElement('div');
        damageDiv.style.cssText = 'background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px; padding: 15px; margin-bottom: 15px;';
        damageDiv.innerHTML = `
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
            <span style="background: #3498db; color: white; width: 30px; height: 30px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: bold;">${index + 1}</span>
            <button onclick="window.component.removeDamage(${index})" style="background: #e74c3c; color: white; border: none; border-radius: 5px; padding: 5px 10px; cursor: pointer;">Remove</button>
          </div>
          <p style="margin: 5px 0; font-size: 14px; color: #666;"><strong>Partie:</strong> ${damage.part || 'Non spécifiée'}</p>
          <p style="margin: 5px 0; font-size: 14px; color: #666;"><strong>Type:</strong> ${damage.type}</p>
          <p style="margin: 5px 0; font-size: 14px; color: #666;"><strong>Gravité:</strong> ${damage.severity || 'Modéré'}</p>
          <p style="margin: 5px 0; font-size: 12px; color: #999;">Position: (${damage.x}, ${damage.y})</p>
        `;
        damagesList.appendChild(damageDiv);
      });
    }

    if (actionsDiv) {
      actionsDiv.style.display = this.damages.length > 0 ? 'block' : 'none';
    }

    // Make component accessible globally for button clicks
    (window as any).component = this;
  }

  updateCsmStatus() {
    const csmStatusP = document.getElementById('csmStatusP');
    const csmStatusSpan = document.getElementById('csmStatusSpan');
    if (this.csmStatus && csmStatusP && csmStatusSpan) {
      csmStatusSpan.textContent = this.csmStatus;
      csmStatusP.style.display = 'block';
    }
  }

  updateGlbUrl() {
    const glbUrlP = document.getElementById('glbUrlP');
    const glbUrlLink = document.getElementById('glbUrlLink') as HTMLAnchorElement;
    if (this.glbUrl && glbUrlP && glbUrlLink) {
      glbUrlLink.href = this.glbUrl;
      glbUrlP.style.display = 'block';
    }
  }

  animate() {
    requestAnimationFrame(() => this.animate());
    this.controls.update();
    this.renderer.render(this.scene, this.camera);
  }

  onMouseClick(event: MouseEvent) {
    const rect = this.renderer.domElement.getBoundingClientRect();
    this.mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
    this.mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;

    this.raycaster.setFromCamera(this.mouse, this.camera);

    if (this.vehicleModel) {
      const intersects = this.raycaster.intersectObjects(this.vehicleModel.children, true);

      if (intersects.length > 0) {
        const intersect = intersects[0];
        this.addDamageMarker(intersect.point, intersect.object);
      }
    }
  }

  addDamageMarker(position: THREE.Vector3, object: THREE.Object3D) {
    // Create damage marker
    const markerGeometry = new THREE.SphereGeometry(0.05, 16, 16);
    const markerMaterial = new THREE.MeshBasicMaterial({ color: 0xff0000 });
    const marker = new THREE.Mesh(markerGeometry, markerMaterial);

    marker.position.copy(position);
    this.scene.add(marker);
    this.damageMarkers.push(marker);

    // Add to damages list
    const damage = {
      x: Math.round(position.x * 100) / 100,
      y: Math.round(position.y * 100) / 100,
      z: Math.round(position.z * 100) / 100,
      type: 'Impact Damage',
      part: object.name || 'Vehicle Part',
      severity: 'Moderate',
      timestamp: new Date()
    };

    this.damages.push(damage);
    this.updateDamagesList();

    console.log('📍 3D Damage marker added:', damage);
  }

  loadDemo3DModel() {
    console.log('🔄 Loading demo 3D model...');

    // Create a simple demo car model using Three.js primitives
    const carGroup = new THREE.Group();

    // Car body (main box) - RED COLOR
    const bodyGeometry = new THREE.BoxGeometry(6, 1.5, 3); // Increased size
    const bodyMaterial = new THREE.MeshPhongMaterial({ color: 0xff0000 }); // Red color
    const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
    body.position.y = 0.75;
    body.castShadow = true;
    body.receiveShadow = true;
    carGroup.add(body);

    // Car roof - RED COLOR
    const roofGeometry = new THREE.BoxGeometry(3.75, 1.2, 2.7); // Increased size
    const roofMaterial = new THREE.MeshPhongMaterial({ color: 0xcc0000 }); // Darker red
    const roof = new THREE.Mesh(roofGeometry, roofMaterial);
    roof.position.set(0, 2.1, 0);
    roof.castShadow = true;
    carGroup.add(roof);

    // Wheels - Larger size
    const wheelGeometry = new THREE.CylinderGeometry(0.6, 0.6, 0.45, 16); // Increased size
    const wheelMaterial = new THREE.MeshPhongMaterial({ color: 0x2c3e50 });

    const positions = [
      [-2.25, 0, 1.8],   // Front left - adjusted for larger car
      [2.25, 0, 1.8],    // Front right
      [-2.25, 0, -1.8],  // Rear left
      [2.25, 0, -1.8]    // Rear right
    ];

    positions.forEach(pos => {
      const wheel = new THREE.Mesh(wheelGeometry, wheelMaterial);
      wheel.position.set(pos[0], pos[1], pos[2]);
      wheel.rotation.z = Math.PI / 2;
      wheel.castShadow = true;
      carGroup.add(wheel);
    });

    // Remove existing model
    if (this.vehicleModel) {
      this.scene.remove(this.vehicleModel);
    }

    this.vehicleModel = carGroup;
    this.scene.add(this.vehicleModel);

    // Position camera for good view of larger model
    this.camera.position.set(12, 9, 12); // Moved camera further back for larger model
    this.controls.target.set(0, 0, 0);
    this.controls.update();

    console.log('✅ Demo 3D car model loaded successfully');
  }

  load3DModel() {
    if (this.glbUrl) {
      console.log('🔄 Loading 3D model from CSM.ai:', this.glbUrl);

      this.loader.load(
        this.glbUrl,
        (gltf) => {
          console.log('✅ 3D model loaded successfully');
          console.log('Model scene:', gltf.scene);
          console.log('Model children:', gltf.scene.children);

          // Remove existing model
          if (this.vehicleModel) {
            this.scene.remove(this.vehicleModel);
          }

          this.vehicleModel = gltf.scene;

          // Configure model with custom materials and textures
          this.vehicleModel.traverse((child) => {
            console.log('Processing child:', child.name, child.type);
            if (child instanceof THREE.Mesh) {
              child.castShadow = true;
              child.receiveShadow = true;

              // Apply custom vehicle material with realistic car colors and textures
              child.material = this.createVehicleMaterial(child.name);
              console.log('✅ Applied custom material to:', child.name);
            }
          });

          // Scale and position model
          const box = new THREE.Box3().setFromObject(this.vehicleModel);
          const size = box.getSize(new THREE.Vector3());
          const center = box.getCenter(new THREE.Vector3());
          const maxSize = Math.max(size.x, size.y, size.z);

          console.log('🔍 DETAILED MODEL INFO:');
          console.log('Original size:', size);
          console.log('Original center:', center);
          console.log('Max size:', maxSize);
          console.log('Bounding box min:', box.min);
          console.log('Bounding box max:', box.max);

          if (maxSize > 0.001) { // Check if model has reasonable size
            // Scale to fill most of the viewport (much bigger)
            const scale = 40 / maxSize; // Increased scale for bigger display
            this.vehicleModel.scale.setScalar(scale);
            console.log('Applied large scale:', scale);

            // Center the model at origin
            this.vehicleModel.position.set(0, 0, 0);
            console.log('Model positioned at origin');
          } else {
            console.log('⚠️ Model too small or zero size, applying fixed large scale');
            this.vehicleModel.scale.setScalar(400); // Increased fixed scale
            this.vehicleModel.position.set(0, 0, 0);
          }

          this.scene.add(this.vehicleModel);

          // Position camera to get a good view of the larger model
          this.camera.position.set(12, 9, 12); // Moved camera further back for larger model
          this.controls.target.set(0, 0, 0); // Look at origin where model is
          this.controls.update();

          console.log('📷 Camera positioned for optimal view');
          console.log('✅ CSM.ai 3D model added to scene');
          console.log('Scene children count:', this.scene.children.length);
        },
        (progress) => {
          const percent = (progress.loaded / progress.total * 100);
          console.log('📊 Loading progress:', percent + '%');
        },
        (error) => {
          console.error('❌ Error loading 3D model:', error);
          this.error = `Failed to load 3D model: ${error instanceof Error ? error.message : 'Unknown error'}`;
          this.showError();
        }
      );
    } else {
      console.log('⚠️ No GLB URL available, waiting for CSM.ai session to complete...');
      this.error = 'No 3D model URL available yet. Session may still be processing.';
      this.showError();
    }
  }



  startProcess() {
    console.log('🚀 Starting 3D model process for session:', this.sessionId);

    // Check if this is a demo session or real CSM.ai session
    // Real CSM.ai sessions are typically 24-character hex strings
    if (this.sessionId === 'demo' || this.sessionId.startsWith('SESSION_demo_') || this.sessionId.startsWith('SESSION_fallback_')) {
      console.log('📋 Detected demo/fallback session, loading demo model');
      this.startDemoSession();
    } else if (this.sessionId.length === 24 && /^[a-f0-9]+$/i.test(this.sessionId)) {
      console.log('🔄 Detected real CSM.ai session (24-char hex), polling API');
      this.startRealSession();
    } else {
      console.log('⚠️ Unknown session format, trying real API first then fallback to demo');
      // Try real API first, if it fails, fallback to demo
      this.startRealSession();
    }
  }

  startDemoSession() {
    this.message = 'Loading demo 3D model...';
    this.modelType = 'Demo Model';
    this.status = 'Loading Demo';
    this.progress = 50;

    // Simulate loading for demo
    setTimeout(() => {
      this.progress = 100;
      this.message = 'Demo model loaded successfully!';
      this.status = 'Complete';

      // Load a demo 3D model
      this.loadDemo3DModel();
      this.showViewer();
    }, 2000);
  }

  startRealSession() {
    this.message = 'Processing CSM.ai session...';
    this.modelType = 'CSM.ai Generated';
    this.status = 'Processing';

    console.log('🔄 Starting real CSM.ai session polling for:', this.sessionId);

    // Real CSM.ai polling with fallback to demo on error
    this.pollCsmApi();
  }

  pollCsmApi() {
    const apiUrl = `https://api.csm.ai/v3/sessions/${this.sessionId}`;

    const poll = () => {
      console.log('🔄 Polling CSM.ai for session:', this.sessionId);
      console.log('🔗 API URL:', apiUrl);

      this.http.get<any>(apiUrl, {
        headers: {
          'x-api-key': '600fbeB2D525309bA145A85afab2F5e5',
          'Content-Type': 'application/json'
        }
      }).subscribe({
        next: (data: any) => {
          console.log('📊 CSM.ai response:', data);
          this.csmStatus = data.status;
          this.updateCsmStatus();

          // Check mesh status and progress
          if (data.output?.meshes?.[0]) {
            const mesh = data.output.meshes[0];
            console.log(`📊 Mesh status: ${mesh.status}`);

            // Check for progress in jobs
            if (mesh.jobs && mesh.jobs.length > 0) {
              const job = mesh.jobs[0];
              console.log(`📊 Job status: ${job.status}`);

              if (job.last_percent) {
                const jobProgress = Math.round(job.last_percent);
                this.progress = Math.min(jobProgress, 95);
                this.message = `Generating 3D model... ${jobProgress}%`;
                console.log(`📊 Job progress: ${jobProgress}%`);
              } else {
                // No progress yet, show job status
                this.message = `Job status: ${job.status}`;
                if (job.status === 'queued') {
                  this.progress = 5;
                } else if (job.status === 'processing') {
                  this.progress = Math.max(this.progress, 20);
                }
              }
            }
          }

          // Check session status
          switch (data.status) {
            case 'incomplete':
              // Session is incomplete, check if mesh is complete
              if (data.output?.meshes?.[0]?.status === 'complete') {
                // Mesh is complete even though session is incomplete
                this.progress = 100;
                this.message = 'Model generation complete!';
                this.status = 'Complete';

                // Get GLB URL from completed mesh
                const mesh = data.output.meshes[0];
                if (mesh.data?.glb_url) {
                  this.glbUrl = mesh.data.glb_url;
                  this.modelType = 'CSM.ai 3D Model';
                  console.log('✅ GLB URL found from completed mesh:', this.glbUrl);

                  setTimeout(() => {
                    this.showViewer();
                    this.updateGlbUrl();
                    this.load3DModel();
                  }, 1000);
                  return; // Stop polling
                } else {
                  console.log('⚠️ Mesh complete but no GLB URL found');
                  this.error = 'Mesh complete but no 3D model URL found';
                  this.showError();
                  return;
                }
              } else {
                // Still processing
                this.message = `Model generation: ${data.status}...`;
                this.progress = Math.max(this.progress, 10);
                console.log(`⏳ Session status: ${data.status}, mesh status: ${data.output?.meshes?.[0]?.status || 'unknown'}, polling again in 8 seconds...`);
                setTimeout(poll, 8000);
                break;
              }

            case 'complete':
              this.progress = 100;
              this.message = 'Model generation complete!';
              this.status = 'Complete';

              // Get GLB URL from completed session
              if (data.output?.meshes?.[0]?.data?.glb_url) {
                this.glbUrl = data.output.meshes[0].data.glb_url;
                this.modelType = 'CSM.ai 3D Model';
                console.log('✅ GLB URL found from completed session:', this.glbUrl);

                setTimeout(() => {
                  this.showViewer();
                  this.updateGlbUrl();
                  this.load3DModel();
                }, 1000);
              } else {
                console.log('⚠️ Session complete but no GLB URL found');
                console.log('Session data:', data);
                this.error = 'Session complete but no 3D model URL found';
                this.showError();
              }
              return; // Stop polling

            case 'failed':
            case 'error':
              this.error = `CSM.ai generation failed: ${data.status}`;
              this.status = 'Failed';
              this.showError();
              console.log('❌ Session failed:', data);
              return; // Stop polling

            default:
              console.log(`🔄 Unknown status: ${data.status}, continuing to poll...`);
              this.message = `Status: ${data.status}`;
              this.progress = Math.min(this.progress + 5, 80);
              setTimeout(poll, 10000); // Poll every 10 seconds for unknown status
          }
        },
        error: (error: any) => {
          console.error('❌ CSM.ai API error:', error);
          console.log('🔄 API failed, falling back to demo model...');

          // Fallback to demo model when API fails
          this.message = 'API unavailable, loading demo model...';
          this.status = 'Loading Demo';
          this.modelType = 'Demo Model (API Fallback)';
          this.error = '';

          // Start demo session after a short delay
          setTimeout(() => {
            this.startDemoSession();
          }, 1000);
        }
      });
    };

    // Start polling
    poll();
  }

  retryPolling() {
    this.error = '';
    const errorDiv = document.getElementById('errorDiv');
    if (errorDiv) errorDiv.style.display = 'none';

    this.message = 'Retrying CSM.ai connection...';
    this.pollCsmApi();
  }



  removeDamage(index: number) {
    this.damages.splice(index, 1);

    // Remove corresponding 3D marker
    if (this.damageMarkers[index]) {
      this.scene.remove(this.damageMarkers[index]);
      this.damageMarkers.splice(index, 1);
    }

    this.updateDamagesList();
  }

  clearAll() {
    this.damages = [];

    // Remove all 3D markers
    this.damageMarkers.forEach(marker => {
      this.scene.remove(marker);
    });
    this.damageMarkers = [];

    this.updateDamagesList();
  }

  saveClaims() {
    if (this.isDamageMode) {
      this.saveAndReturn();
    } else {
      const data = {
        sessionId: this.sessionId,
        modelType: this.modelType,
        damages: this.damages,
        timestamp: new Date()
      };

      console.log('💾 Saving claims:', data);
      alert(`✅ ${this.damages.length} damage claim(s) saved!\n\nSession: ${this.sessionId}`);
    }
  }

  saveAndReturn() {
    const damageData = {
      type: '3D_DAMAGE_DATA',
      formId: this.formId,
      damages: this.damages,
      timestamp: new Date()
    };

    console.log('💾 Saving damage data for form:', damageData);

    // Only proceed if in browser environment
    if (!this.isBrowser) {
      console.log('⚠️ Not in browser environment, cannot save claims');
      return;
    }

    // Method 1: PostMessage to parent window (opener)
    if (window.opener && !window.opener.closed) {
      try {
        window.opener.postMessage(damageData, window.location.origin);
        console.log('📨 Sent damage data via postMessage to opener');
      } catch (error) {
        console.error('❌ Error sending postMessage:', error);
      }
    }

    // Method 2: LocalStorage backup (in case postMessage fails)
    try {
      localStorage.setItem(`3d_damage_${this.formId}`, JSON.stringify(damageData));
      console.log('📦 Saved damage data to localStorage as backup');
    } catch (error) {
      console.error('❌ Error saving to localStorage:', error);
    }

    // Show confirmation
    alert(`✅ ${this.damages.length} dommage(s) sauvegardé(s)!\n\nRetour au formulaire principal...`);

    // Close the window
    try {
      window.close();
    } catch (error) {
      console.error('❌ Error closing window:', error);
      // If window.close() fails, try to navigate back
      if (this.callbackUrl) {
        window.location.href = this.callbackUrl;
      }
    }
  }

  /**
   * Create realistic vehicle materials with colors and textures using Three.js
   */
  createVehicleMaterial(partName: string): THREE.Material {
    const lowerName = partName.toLowerCase();

    // Car body parts - metallic paint - RED COLOR
    if (lowerName.includes('body') || lowerName.includes('door') || lowerName.includes('hood') ||
        lowerName.includes('roof') || lowerName.includes('trunk') || lowerName.includes('panel')) {
      return new THREE.MeshPhysicalMaterial({
        color: 0xff0000,           // Bright red car color
        metalness: 0.9,            // High metallic finish
        roughness: 0.1,            // Smooth car paint
        clearcoat: 1.0,            // Clear coat layer
        clearcoatRoughness: 0.05,  // Smooth clear coat
        envMapIntensity: 1.5       // Strong reflections
      });
    }

    // Bumpers and plastic parts - matte plastic
    if (lowerName.includes('bumper') || lowerName.includes('trim') || lowerName.includes('plastic')) {
      return new THREE.MeshLambertMaterial({
        color: 0x2d2d2d,           // Dark gray plastic
        transparent: false
      });
    }

    // Windows and glass - transparent
    if (lowerName.includes('window') || lowerName.includes('glass') || lowerName.includes('windshield')) {
      return new THREE.MeshPhysicalMaterial({
        color: 0x87ceeb,           // Light blue tint
        metalness: 0.0,
        roughness: 0.0,
        transmission: 0.9,         // High transparency
        transparent: true,
        opacity: 0.3
      });
    }

    // Wheels and tires - rubber/metal
    if (lowerName.includes('wheel') || lowerName.includes('tire') || lowerName.includes('rim')) {
      if (lowerName.includes('rim') || lowerName.includes('alloy')) {
        // Metal rim
        return new THREE.MeshPhysicalMaterial({
          color: 0xc0c0c0,         // Silver metal
          metalness: 1.0,
          roughness: 0.2
        });
      } else {
        // Rubber tire
        return new THREE.MeshLambertMaterial({
          color: 0x1a1a1a          // Black rubber
        });
      }
    }

    // Lights - emissive
    if (lowerName.includes('light') || lowerName.includes('lamp') || lowerName.includes('headlight')) {
      return new THREE.MeshPhysicalMaterial({
        color: 0xffffff,           // White light
        emissive: 0xffffaa,        // Warm white emission
        emissiveIntensity: 0.3,
        metalness: 0.0,
        roughness: 0.1
      });
    }

    // Chrome and metal parts
    if (lowerName.includes('chrome') || lowerName.includes('handle') || lowerName.includes('mirror')) {
      return new THREE.MeshPhysicalMaterial({
        color: 0xf0f0f0,           // Bright chrome
        metalness: 1.0,
        roughness: 0.05,
        envMapIntensity: 2.0
      });
    }

    // Default car body material for unknown parts - RED COLOR
    return new THREE.MeshPhysicalMaterial({
      color: 0xff0000,             // Default red car color
      metalness: 0.8,
      roughness: 0.2,
      clearcoat: 0.8,
      clearcoatRoughness: 0.1
    });
  }

  /**
   * Create realistic space background with stars and nebula
   */
  createSpaceBackground() {
    // Create starfield
    const starsGeometry = new THREE.BufferGeometry();
    const starsMaterial = new THREE.PointsMaterial({
      color: 0xffffff,
      size: 2,
      sizeAttenuation: false
    });

    const starsVertices = [];
    for (let i = 0; i < 10000; i++) {
      const x = (Math.random() - 0.5) * 2000;
      const y = (Math.random() - 0.5) * 2000;
      const z = (Math.random() - 0.5) * 2000;
      starsVertices.push(x, y, z);
    }

    starsGeometry.setAttribute('position', new THREE.Float32BufferAttribute(starsVertices, 3));
    const starField = new THREE.Points(starsGeometry, starsMaterial);
    this.scene.add(starField);

    // Create nebula background sphere
    const nebulaGeometry = new THREE.SphereGeometry(500, 32, 32);
    const nebulaMaterial = new THREE.MeshBasicMaterial({
      color: 0x1a1a2e,
      transparent: true,
      opacity: 0.3,
      side: THREE.BackSide
    });
    const nebula = new THREE.Mesh(nebulaGeometry, nebulaMaterial);
    this.scene.add(nebula);

    console.log('✅ Space background created with stars and nebula');
  }

  /**
   * Setup advanced lighting for realistic vehicle rendering
   */
  setupAdvancedLighting() {
    // Enhanced ambient lighting
    const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
    this.scene.add(ambientLight);

    // Main key light (sun-like)
    const keyLight = new THREE.DirectionalLight(0xffffff, 1.2);
    keyLight.position.set(10, 10, 5);
    keyLight.castShadow = true;
    keyLight.shadow.mapSize.width = 4096;
    keyLight.shadow.mapSize.height = 4096;
    keyLight.shadow.camera.near = 0.5;
    keyLight.shadow.camera.far = 500;
    keyLight.shadow.camera.left = -50;
    keyLight.shadow.camera.right = 50;
    keyLight.shadow.camera.top = 50;
    keyLight.shadow.camera.bottom = -50;
    this.scene.add(keyLight);

    // Fill light (softer, opposite side)
    const fillLight = new THREE.DirectionalLight(0x87ceeb, 0.6);
    fillLight.position.set(-10, 5, -5);
    this.scene.add(fillLight);

    // Rim light (for edge definition)
    const rimLight = new THREE.DirectionalLight(0xffffff, 0.8);
    rimLight.position.set(0, 10, -10);
    this.scene.add(rimLight);

    // Bottom bounce light
    const bounceLight = new THREE.DirectionalLight(0x404040, 0.3);
    bounceLight.position.set(0, -5, 0);
    this.scene.add(bounceLight);

    // Add hemisphere light for realistic sky lighting
    const hemisphereLight = new THREE.HemisphereLight(0x87ceeb, 0x1a1a2e, 0.5);
    this.scene.add(hemisphereLight);

    console.log('✅ Advanced lighting setup complete');
  }

  /**
   * Reset camera to default position
   */
  resetCamera() {
    if (this.camera && this.controls) {
      this.camera.position.set(12, 9, 12); // Updated for larger model view
      this.controls.target.set(0, 0, 0);
      this.controls.update();
      console.log('📷 Camera reset to default position');
    }
  }

  /**
   * Toggle wireframe mode for the vehicle model
   */
  toggleWireframe() {
    if (this.vehicleModel) {
      this.vehicleModel.traverse((child) => {
        if (child instanceof THREE.Mesh && child.material) {
          if (Array.isArray(child.material)) {
            child.material.forEach(mat => {
              if ('wireframe' in mat) {
                (mat as any).wireframe = !(mat as any).wireframe;
              }
            });
          } else if ('wireframe' in child.material) {
            (child.material as any).wireframe = !(child.material as any).wireframe;
          }
        }
      });
      console.log('🔲 Wireframe mode toggled');
    }
  }
}
