import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { Evenement } from './evenement.model';

@Injectable({
  providedIn: 'root'
})
export class EvenementService {
  private apiUrl = 'http://localhost:8080/api/evenements';

  constructor(private http: HttpClient) {}

  getAll(): Observable<Evenement[]> {
    return this.http.get<Evenement[]>(this.apiUrl);
  }

  getById(id: number): Observable<Evenement> {
    console.log('🌐 Service: Getting event by ID:', id);
    return this.http.get<Evenement>(`${this.apiUrl}/${id}`).pipe(
      tap(response => console.log('🌐 Service: Received event details:', response))
    );
  }

  create(evenement: Evenement): Observable<Evenement> {
    console.log('🌐 Service: Sending event to backend:', evenement);
    return this.http.post<Evenement>(this.apiUrl, evenement).pipe(
      tap(response => console.log('🌐 Service: Received response:', response))
    );
  }

  update(id: number, evenement: Evenement): Observable<Evenement> {
    return this.http.put<Evenement>(`${this.apiUrl}/${id}`, evenement);
  }

  delete(id: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}`);
  }
}