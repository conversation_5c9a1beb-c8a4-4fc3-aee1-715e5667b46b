package com.fed.platform.sinister.service;

import java.io.IOException;
import java.time.Instant;
import java.util.List;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.fed.platform.sinister.domain.OuvertureSinistre;
import com.fed.platform.sinister.repository.OuvertureSinistreRepository;

@Service
@Transactional
public class OuvertureSinistreService {

    private static final Logger logger = LoggerFactory.getLogger(OuvertureSinistreService.class);

    @Autowired
    private OuvertureSinistreRepository ouvertureSinistreRepository;

    /**
     * Save a new ouverture sinistre
     */
    public OuvertureSinistre save(OuvertureSinistre ouvertureSinistre) {
        logger.info("🔍 Saving new ouverture sinistre");

        // Set creation time if not set
        if (ouvertureSinistre.getDateCreation() == null) {
            ouvertureSinistre.setDateCreation(Instant.now());
        }

        OuvertureSinistre saved = ouvertureSinistreRepository.save(ouvertureSinistre);
        logger.info("✅ Ouverture sinistre saved with ID: {} and numero: {}",
                saved.getId(), saved.getNumeroSinistre());

        return saved;
    }

    /**
     * Update an existing ouverture sinistre
     */
    public OuvertureSinistre update(Long id, OuvertureSinistre ouvertureSinistre) {
        logger.info("🔍 Updating ouverture sinistre with ID: {}", id);

        Optional<OuvertureSinistre> existingOpt = ouvertureSinistreRepository.findById(id);
        if (existingOpt.isPresent()) {
            OuvertureSinistre existing = existingOpt.get();

            // Update fields
            existing.setEvenement(ouvertureSinistre.getEvenement());
            existing.setTypeDeDegat(ouvertureSinistre.getTypeDeDegat());
            existing.setDescriptionDeDegat(ouvertureSinistre.getDescriptionDeDegat());
            existing.setTier(ouvertureSinistre.getTier());
            existing.setTypeDuTier(ouvertureSinistre.getTypeDuTier());
            existing.setCompagnieTier(ouvertureSinistre.getCompagnieTier());
            existing.setCasDeBareme(ouvertureSinistre.getCasDeBareme());
            existing.setDegatEstimatif(ouvertureSinistre.getDegatEstimatif());
            existing.setResponsabilite(ouvertureSinistre.getResponsabilite());
            existing.setLinkVehicule(ouvertureSinistre.getLinkVehicule());

            // Update files if provided
            if (ouvertureSinistre.getPhotosDeVehicule() != null) {
                existing.setPhotosDeVehicule(ouvertureSinistre.getPhotosDeVehicule());
            }
            if (ouvertureSinistre.getAttachement() != null) {
                existing.setAttachement(ouvertureSinistre.getAttachement());
            }

            OuvertureSinistre updated = ouvertureSinistreRepository.save(existing);
            logger.info("✅ Ouverture sinistre updated: {}", updated.getNumeroSinistre());

            return updated;
        } else {
            logger.error("❌ Ouverture sinistre not found with ID: {}", id);
            throw new RuntimeException("Ouverture sinistre not found with ID: " + id);
        }
    }

    /**
     * Find all ouverture sinistres
     */
    @Transactional(readOnly = true)
    public List<OuvertureSinistre> findAll() {
        logger.info("🔍 Finding all ouverture sinistres");
        List<OuvertureSinistre> result = ouvertureSinistreRepository.findAllOrderByDateCreationDesc();
        logger.info("📄 Found {} ouverture sinistres", result.size());
        return result;
    }

    /**
     * Find by ID
     */
    @Transactional(readOnly = true)
    public Optional<OuvertureSinistre> findById(Long id) {
        logger.info("🔍 Finding ouverture sinistre by ID: {}", id);
        Optional<OuvertureSinistre> result = ouvertureSinistreRepository.findById(id);
        if (result.isPresent()) {
            logger.info("✅ Found ouverture sinistre: {}", result.get().getNumeroSinistre());
        } else {
            logger.info("❌ Ouverture sinistre not found with ID: {}", id);
        }
        return result;
    }

    /**
     * Find by numero sinistre
     */
    @Transactional(readOnly = true)
    public Optional<OuvertureSinistre> findByNumeroSinistre(String numeroSinistre) {
        logger.info("🔍 Finding ouverture sinistre by numero: {}", numeroSinistre);
        Optional<OuvertureSinistre> result = ouvertureSinistreRepository.findByNumeroSinistre(numeroSinistre);
        if (result.isPresent()) {
            logger.info("✅ Found ouverture sinistre with numero: {}", numeroSinistre);
        } else {
            logger.info("❌ Ouverture sinistre not found with numero: {}", numeroSinistre);
        }
        return result;
    }

    /**
     * Delete by ID
     */
    public void deleteById(Long id) {
        logger.info("🔍 Deleting ouverture sinistre with ID: {}", id);
        if (ouvertureSinistreRepository.existsById(id)) {
            ouvertureSinistreRepository.deleteById(id);
            logger.info("✅ Ouverture sinistre deleted with ID: {}", id);
        } else {
            logger.error("❌ Cannot delete - ouverture sinistre not found with ID: {}", id);
            throw new RuntimeException("Ouverture sinistre not found with ID: " + id);
        }
    }

    /**
     * Search by criteria
     */
    @Transactional(readOnly = true)
    public List<OuvertureSinistre> findByCriteria(String evenement, String typeDeDegat,
            Boolean tier, Integer responsabilite) {
        logger.info("🔍 Searching ouverture sinistres by criteria");
        List<OuvertureSinistre> result = ouvertureSinistreRepository.findByCriteria(
                evenement, typeDeDegat, tier, responsabilite);
        logger.info("📄 Found {} ouverture sinistres matching criteria", result.size());
        return result;
    }

    /**
     * Handle file upload for photos
     */
    public byte[] processPhotoUpload(MultipartFile file) throws IOException {
        if (file == null || file.isEmpty()) {
            return null;
        }

        logger.info("📸 Processing photo upload: {} ({})", file.getOriginalFilename(), file.getSize());

        // Validate file type
        String contentType = file.getContentType();
        if (contentType == null || (!contentType.equals("image/jpeg") && !contentType.equals("image/png"))) {
            throw new IllegalArgumentException("Only JPEG and PNG images are allowed");
        }

        // Validate file size (max 10MB)
        if (file.getSize() > 10 * 1024 * 1024) {
            throw new IllegalArgumentException("File size must be less than 10MB");
        }

        return file.getBytes();
    }

    /**
     * Handle file upload for attachments
     */
    public byte[] processAttachmentUpload(MultipartFile file) throws IOException {
        if (file == null || file.isEmpty()) {
            return null;
        }

        logger.info("📎 Processing attachment upload: {} ({})", file.getOriginalFilename(), file.getSize());

        // Validate file type
        String contentType = file.getContentType();
        if (contentType == null || !contentType.equals("application/pdf")) {
            throw new IllegalArgumentException("Only PDF files are allowed for attachments");
        }

        // Validate file size (max 24MB)
        if (file.getSize() > 24 * 1024 * 1024) {
            throw new IllegalArgumentException("File size must be less than 24MB");
        }

        return file.getBytes();
    }

    /**
     * Check if numero sinistre exists
     */
    @Transactional(readOnly = true)
    public boolean existsByNumeroSinistre(String numeroSinistre) {
        return ouvertureSinistreRepository.existsByNumeroSinistre(numeroSinistre);
    }
}
