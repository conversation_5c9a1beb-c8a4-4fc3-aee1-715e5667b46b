package com.fed.platform.sinister.domain;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Instant;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Enumerated;
import jakarta.persistence.EnumType;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Size;

@Entity
@Table(name = "sinistre")
public class Sinistre implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "num_sinistre")
    private String numSinistre;

    @Column(name = "date_declaration")
    private Instant dateDeclaration;

    @Column(name = "date_survenance")
    private Instant dateSurvenance;

    @Column(name = "date_ouverture_sinistre")
    private Instant dateOuvertureSinistre;

    @Enumerated(EnumType.STRING)
    @Column(name = "etat")
    private EtatSinistre etat;

    @Column(name = "cout_estime")
    private BigDecimal coutEstime;

    @Size(max = 256)
    @Column(name = "description_degats", length = 256)
    private String descriptionDegats;

    @Enumerated(EnumType.STRING)
    @Column(name = "type_degat")
    private TypeDegats typeDegat;

    @Enumerated(EnumType.STRING)
    @Column(name = "type_document")
    private TypeDocument typeDocument;

    @Enumerated(EnumType.STRING)
    @Column(name = "nature_tiers")
    private NatureTiers natureTiers;

    @Enumerated(EnumType.STRING)
    @Column(name = "statut_tiers")
    private StatutTiers statutTiers;

    @Enumerated(EnumType.STRING)
    @Column(name = "type_tiers")
    private TypeTiers typeTiers;

    @Enumerated(EnumType.STRING)
    @Column(name = "type_notification")
    private TypeNotification typeNotification;

    @Enumerated(EnumType.STRING)
    @Column(name = "type_personne")
    private TypePersonne typePersonne;

    @Min(value = 0)
    @Max(value = 4)
    @Column(name = "responsabilite")
    private Integer responsabilite;

    @Column(name = "existe_tier")
    private Boolean existeTier;

    @Column(name = "conducteur_assure")
    private Boolean conducteurAssure;

    @Column(name = "couverture_contrat")
    private Boolean couvertureContrat;

    @Column(name = "constat_attache")
    private Boolean constatAttache;

    @Column(name = "tiers_compagnie_assurance")
    private String tiersCompagnieAssurance;

    @Column(name = "numero_contrat")
    private String numeroContrat;

    @Column(name = "cin_assure")
    private String cinAssure;

    @Column(name = "nom_prenom_assure")
    private String nomPrenomAssure;

    @Column(name = "date_naissance_assure")
    private Instant dateNaissanceAssure;

    @Column(name = "immatriculation_assure")
    private String immatriculationAssure;

    @Column(name = "upload_date")
    private Instant uploadDate;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getNumSinistre() {
        return numSinistre;
    }

    public void setNumSinistre(String numSinistre) {
        this.numSinistre = numSinistre;
    }

    public Instant getDateDeclaration() {
        return dateDeclaration;
    }

    public void setDateDeclaration(Instant dateDeclaration) {
        this.dateDeclaration = dateDeclaration;
    }

    public Instant getDateSurvenance() {
        return dateSurvenance;
    }

    public void setDateSurvenance(Instant dateSurvenance) {
        this.dateSurvenance = dateSurvenance;
    }

    public Instant getDateOuvertureSinistre() {
        return dateOuvertureSinistre;
    }

    public void setDateOuvertureSinistre(Instant dateOuvertureSinistre) {
        this.dateOuvertureSinistre = dateOuvertureSinistre;
    }

    public EtatSinistre getEtat() {
        return etat;
    }

    public void setEtat(EtatSinistre etat) {
        this.etat = etat;
    }

    public BigDecimal getCoutEstime() {
        return coutEstime;
    }

    public void setCoutEstime(BigDecimal coutEstime) {
        this.coutEstime = coutEstime;
    }

    public String getDescriptionDegats() {
        return descriptionDegats;
    }

    public void setDescriptionDegats(String descriptionDegats) {
        this.descriptionDegats = descriptionDegats;
    }

    public TypeDegats getTypeDegat() {
        return typeDegat;
    }

    public void setTypeDegat(TypeDegats typeDegat) {
        this.typeDegat = typeDegat;
    }

    public Integer getResponsabilite() {
        return responsabilite;
    }

    public void setResponsabilite(Integer responsabilite) {
        this.responsabilite = responsabilite;
    }

    public Boolean isExisteTier() {
        return existeTier;
    }

    public void setExisteTier(Boolean existeTier) {
        this.existeTier = existeTier;
    }

    public Boolean isConducteurAssure() {
        return conducteurAssure;
    }

    public void setConducteurAssure(Boolean conducteurAssure) {
        this.conducteurAssure = conducteurAssure;
    }

    public Boolean isCouvertureContrat() {
        return couvertureContrat;
    }

    public void setCouvertureContrat(Boolean couvertureContrat) {
        this.couvertureContrat = couvertureContrat;
    }

    public Boolean isConstatAttache() {
        return constatAttache;
    }

    public void setConstatAttache(Boolean constatAttache) {
        this.constatAttache = constatAttache;
    }

    public String getTiersCompagnieAssurance() {
        return tiersCompagnieAssurance;
    }

    public void setTiersCompagnieAssurance(String tiersCompagnieAssurance) {
        this.tiersCompagnieAssurance = tiersCompagnieAssurance;
    }

    public String getNumeroContrat() {
        return numeroContrat;
    }

    public void setNumeroContrat(String numeroContrat) {
        this.numeroContrat = numeroContrat;
    }

    public String getCinAssure() {
        return cinAssure;
    }

    public void setCinAssure(String cinAssure) {
        this.cinAssure = cinAssure;
    }

    public String getNomPrenomAssure() {
        return nomPrenomAssure;
    }

    public void setNomPrenomAssure(String nomPrenomAssure) {
        this.nomPrenomAssure = nomPrenomAssure;
    }

    public Instant getDateNaissanceAssure() {
        return dateNaissanceAssure;
    }

    public void setDateNaissanceAssure(Instant dateNaissanceAssure) {
        this.dateNaissanceAssure = dateNaissanceAssure;
    }

    public String getImmatriculationAssure() {
        return immatriculationAssure;
    }

    public void setImmatriculationAssure(String immatriculationAssure) {
        this.immatriculationAssure = immatriculationAssure;
    }

    public Instant getUploadDate() {
        return uploadDate;
    }

    public void setUploadDate(Instant uploadDate) {
        this.uploadDate = uploadDate;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof Sinistre)) {
            return false;
        }
        return id != null && id.equals(((Sinistre) o).id);
    }

    @Override
    public int hashCode() {
        return 31;
    }

    @Override
    public String toString() {
        return "Sinistre{" +
                "id=" + getId() +
                ", numSinistre='" + getNumSinistre() + "'" +
                ", dateDeclaration='" + getDateDeclaration() + "'" +
                ", dateSurvenance='" + getDateSurvenance() + "'" +
                ", dateOuvertureSinistre='" + getDateOuvertureSinistre() + "'" +
                ", etat='" + getEtat() + "'" +
                ", coutEstime=" + getCoutEstime() +
                ", descriptionDegats='" + getDescriptionDegats() + "'" +
                ", typeDegat='" + getTypeDegat() + "'" +
                ", responsabilite=" + getResponsabilite() +
                ", existeTier='" + isExisteTier() + "'" +
                ", conducteurAssure='" + isConducteurAssure() + "'" +
                ", couvertureContrat='" + isCouvertureContrat() + "'" +
                ", constatAttache='" + isConstatAttache() + "'" +
                ", tiersCompagnieAssurance='" + getTiersCompagnieAssurance() + "'" +
                ", numeroContrat='" + getNumeroContrat() + "'" +
                ", cinAssure='" + getCinAssure() + "'" +
                ", nomPrenomAssure='" + getNomPrenomAssure() + "'" +
                ", dateNaissanceAssure='" + getDateNaissanceAssure() + "'" +
                ", immatriculationAssure='" + getImmatriculationAssure() + "'" +
                ", uploadDate='" + getUploadDate() + "'" +
                "}";
    }
}
