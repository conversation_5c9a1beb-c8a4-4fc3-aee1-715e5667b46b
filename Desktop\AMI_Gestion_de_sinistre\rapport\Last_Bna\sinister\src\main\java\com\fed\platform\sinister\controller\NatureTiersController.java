package com.fed.platform.sinister.controller;

import java.util.List;

import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fed.platform.sinister.domain.NatureTiers;
import com.fed.platform.sinister.domain.NatureTiersEntity;
import com.fed.platform.sinister.service.NatureTiersService;

@RestController
@RequestMapping("/api/natures")
@CrossOrigin(origins = "*")
public class NatureTiersController {

    private final NatureTiersService service;

    public NatureTiersController(NatureTiersService service) {
        this.service = service;
    }

    // Get all ENUM values
    @GetMapping("/enum")
    public NatureTiers[] getEnumValues() {
        return NatureTiers.values();
    }

    // Get all from DB
    @GetMapping
    public List<NatureTiersEntity> getAllEntities() {
        return service.findAll();
    }

    // Save new enum value to DB
    @PostMapping
    public NatureTiersEntity save(@RequestParam NatureTiers value) {
        return service.save(value);
    }

    // Delete by ID
    @DeleteMapping("/{id}")
    public void delete(@PathVariable Long id) {
        service.deleteById(id);
    }
}
