entity EtatSinistre {
    id Long,
    name String
}

enum TypePersonne {
    PERSONNE_MORALE, PERSONNE_PHYSIQUE
}

enum Sexe {
HOMME, FEMME
}

enum NatureTiers {
    AD, AT, AY, BY, CF, CH, MO, PI, TR, VH
}
enum TypeTiers {
PERSONNE, VEHICULE, OBJET
}
enum CompagnieTiers {
BNA_ASSURANCE, ADVERSE, NON_COUVERT
}
enum StatutTiers {
BLESSE, DECEDE
}
enum OrganismeTiers {
STEG, TELECOM, ETC
}
enum TypeDegats {
    MATERIEL, CORPOREL, MAT_CORP, AUTRE
}

enum RoleUtilisateur {
    AGENT, ADMIN, GESTIONNAIRE
}

enum CategorieDuPermis {
    TUNISIEN, ETRANGER
}

enum TypeNotification {
    SMS , EMAIL , SYSTEME
}
enum TypeDocument {
 CONSTAT, PHOTO_VHICULE, CLAUSE_SIGNE, AVIS_MEDECIN, PERMIS_CONDUITE, CIN
}
entity Clause{
 id Long
 dateSignature Instant
 contenu String  
}

entity Notification {
	id Long
    type TypeNotification
    contenu String 
    dateCreation Instant
    destination RoleUtilisateur
}

entity ConfigurationDocument {
	id Long
    obligatoire Boolean
    seulDegats BigDecimal
    avecTiers Boolean
    avecRecours Boolean
}
entity Sinistre {
    id Long
    numSinistre String
    dateDeclaration Instant
    dateSurvenance Instant
    dateOuvertureSinistre Instant
    numSinistre String
    coutEstime BigDecimal
    descriptionDegats String maxlength(256)
    typeDegat TypeDegats
    responsabilite Integer min(0) max(4)
    existeTier Boolean
    conducteurAssure Boolean
    couvertureContrat Boolean
    constatAttache Boolean 
    tiersCompagnieAssurance String
    numeroContrat String
    cinAssure String
    nomPrenomAssure String
    dateNaissanceAssure Instant
    immatriculationAssure String
    uploadDate Instant
}
relationship ManyToOne {
    Sinistre{etat} to EtatSinistre
}

entity Contrat {
    id Long
    numContrat String maxlength(20)
    immatriculation String
    dateEffet Instant
    dateFin Instant
    produit String
}

entity Evenement {
    id Long
    code String maxlength(10)
    libelle String maxlength(255)
}

entity DocumentSinistre {
    id Long
    dateUpload Instant
    pathFichier String
}

entity TiersByType {
	typeTiers TypeTiers
 	compagnieTiers CompagnieTiers
    blesseOuDecede StatutTiers
    organisme OrganismeTiers
}

entity Tiers {
    id Long
    nom String maxlength(30)
    prenom String maxlength(30)
    immatriculationTiers String maxlength(20)
    numPermis String
    dateNaissance Instant
    compagnieAssurance String
    responsabilite Integer min(0) max(4)
    sexeTiers Sexe
    nature NatureTiers
    description String
    numContrat Integer
    numdesinistre Integer
    dateObtention Instant
    profession String
    nombreDejourDerepos Integer
    tauxDincapacite Integer
    accompagnantTiers String 
    degreDePrejudiceMorale String
    sinistreTraite Boolean
    lesAyantDroit String
    nombreDesAyantDroit Integer
    dateDenaissanceDesAyantDroit Instant
    ageDesAyantDroit Integer
}

entity Garantie {
    id Long
    code String maxlength(5) 
    libelle String maxlength(100)
    plafond Double
    franchise Double
    plafondCouvert String
}

entity Adresse {
    id Long
    codePostal String
    gouvernorat String
    cite String
    region String
}

entity Client {
    id Long
    nom String
    prenom String
    typePersonne TypePersonne
    matriculeFiscal String
    cin String
}

entity CasDeBareme {
    id Long
    code String
    libelle String
    resX Integer
    resY Integer
}

entity Degat {
    id Long
    code String
    libele String
    typeDegat TypeDegats
}

entity Conducteur {
    id Long
    vhiculeEnStationement Boolean
    conducteurMemeAssure Boolean
    nom String
    prenom String
    categorieDuPermis CategorieDuPermis
    numeroDuPermis Integer
    dateDeNaissance Instant
    dateObtentionPermis Instant 
}

entity Utilisateur {
    id Long
    username String
    password String
    email String 
    role RoleUtilisateur
}

entity CoutMoyen {
    id Long
    cout BigDecimal
}

entity ConfigEvenement {
    id Long
    delaiDeclaration Integer
    tiersRequis Boolean
    responsabilite Integer min(0) max(4)
   
}

entity TypeDocument {
    id Long
    code String
    libelle String
}

relationship OneToOne {
    Adresse{sinistre} to Sinistre
    Adresse{tiers} to Tiers
    Clause{document} to DocumentSinistre
    ConfigEvenement{degat} to Degat
    CasDeBareme {casDeBareme} to Sinistre

}

relationship ManyToOne {
    Sinistre{contrat} to Contrat
    Sinistre{evenement} to Evenement
    Sinistre{client} to Client
    Tiers{sinistre} to Sinistre
    Conducteur{sinistre} to Sinistre
    DocumentSinistre{sinistre} to Sinistre
    Notification{utilisateur} to Utilisateur
    Clause{sinistre} to Sinistre
}

relationship OneToMany {
    Contrat{garanties} to Garantie
    Evenement{documents} to ConfigurationDocument
    Evenement{configs} to ConfigEvenement
    ConfigEvenement {garantieCouvrant} to Garantie
    TypeDocument{configurations} to ConfigurationDocument
    Sinistre{tiersParType} to TiersByType{sinistre}
}
