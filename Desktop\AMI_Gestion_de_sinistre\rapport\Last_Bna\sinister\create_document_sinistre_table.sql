-- Create document_sinistre table if it doesn't exist
CREATE TABLE IF NOT EXISTS document_sinistre (
    id BIGSERIAL PRIMARY KEY,
    libelle_evenement VARCHAR(255),
    document VARCHAR(255),
    date_upload TIMESTAMP,
    path_fichier VARCHAR(255)
);

-- Insert some test data
INSERT INTO document_sinistre (libelle_evenement, document, date_upload, path_fichier) 
VALUES 
    ('Accident de voiture', 'Constat amiable', NOW(), '/documents/constat_amiable.pdf'),
    ('Vol', 'Déclaration de vol', NOW(), '/documents/declaration_vol.pdf')
ON CONFLICT DO NOTHING;

-- Check if table was created successfully
SELECT * FROM document_sinistre;
