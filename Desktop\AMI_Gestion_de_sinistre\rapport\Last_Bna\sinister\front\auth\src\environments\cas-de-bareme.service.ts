import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';

export interface CasDeBareme {
  id: number;
  libelle: string;
  resX: number;
  resY: number;
}

@Injectable({
  providedIn: 'root'
})
export class CasDeBaremeService {
  private baseUrl = 'http://localhost:8080/api/cas-de-bareme';

  constructor(private http: HttpClient) {}

  getAll(): Observable<CasDeBareme[]> {
    return this.http.get<CasDeBareme[]>(this.baseUrl);
  }
}
