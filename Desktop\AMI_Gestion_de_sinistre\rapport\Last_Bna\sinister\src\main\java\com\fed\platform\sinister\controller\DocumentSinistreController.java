package com.fed.platform.sinister.controller;

import java.util.List;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.fed.platform.sinister.domain.DocumentSinistre;
import com.fed.platform.sinister.repository.DocumentSinistreRepository;

@RestController
@RequestMapping("/api/documents-sinistre")
public class DocumentSinistreController {

    @Autowired
    private DocumentSinistreRepository documentSinistreRepository;

    @GetMapping
    public ResponseEntity<List<DocumentSinistre>> getAllDocuments() {
        try {
            System.out.println("🔍 Getting all documents sinistre");
            List<DocumentSinistre> documents = documentSinistreRepository.findAll();
            System.out.println("📄 Found " + documents.size() + " documents");
            return ResponseEntity.ok(documents);
        } catch (Exception e) {
            System.out.println("❌ Error getting documents: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(500).build();
        }
    }

    @GetMapping("/{id}")
    public ResponseEntity<DocumentSinistre> getDocumentById(@PathVariable Long id) {
        System.out.println("🔍 Getting document sinistre by ID: " + id);
        Optional<DocumentSinistre> document = documentSinistreRepository.findById(id);

        if (document.isPresent()) {
            System.out.println("✅ Document found: " + document.get().getLibelleEvenement());
            return ResponseEntity.ok(document.get());
        } else {
            System.out.println("❌ Document not found with ID: " + id);
            return ResponseEntity.notFound().build();
        }
    }

    @GetMapping("/by-evenement/{libelleEvenement}")
    public List<DocumentSinistre> getDocumentsByEvenement(@PathVariable String libelleEvenement) {
        System.out.println("🔍 Getting documents for evenement: " + libelleEvenement);
        List<DocumentSinistre> documents = documentSinistreRepository.findByLibelleEvenement(libelleEvenement);
        System.out.println("📄 Found " + documents.size() + " documents for evenement: " + libelleEvenement);
        return documents;
    }

    @PostMapping
    public ResponseEntity<DocumentSinistre> createDocument(@RequestBody DocumentSinistre documentSinistre) {
        try {
            System.out.println("🔍 Creating new document sinistre:");
            System.out.println("  - Libelle evenement: " + documentSinistre.getLibelleEvenement());
            System.out.println("  - Document: " + documentSinistre.getDocument());
            System.out.println("  - Path fichier: " + documentSinistre.getPathFichier());

            // Set the current date/time for dateUpload if not provided
            if (documentSinistre.getDateUpload() == null) {
                documentSinistre.setDateUpload(java.time.Instant.now());
                System.out.println("  - Date upload set to: " + documentSinistre.getDateUpload());
            }

            DocumentSinistre saved = documentSinistreRepository.save(documentSinistre);

            System.out.println("✅ Document created with ID: " + saved.getId());
            return ResponseEntity.ok(saved);

        } catch (Exception e) {
            System.out.println("❌ Error creating document: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(500).build();
        }
    }

    @PutMapping("/{id}")
    public ResponseEntity<DocumentSinistre> updateDocument(
            @PathVariable Long id,
            @RequestBody DocumentSinistre documentDetails) {
        System.out.println("🔍 Updating document sinistre with ID: " + id);

        Optional<DocumentSinistre> documentOptional = documentSinistreRepository.findById(id);

        if (documentOptional.isEmpty()) {
            System.out.println("❌ Document not found with ID: " + id);
            return ResponseEntity.notFound().build();
        }

        DocumentSinistre document = documentOptional.get();
        document.setLibelleEvenement(documentDetails.getLibelleEvenement());
        document.setDocument(documentDetails.getDocument());

        DocumentSinistre updated = documentSinistreRepository.save(document);
        System.out.println("✅ Document updated successfully");

        return ResponseEntity.ok(updated);
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteDocument(@PathVariable Long id) {
        System.out.println("🔍 Deleting document sinistre with ID: " + id);

        if (!documentSinistreRepository.existsById(id)) {
            System.out.println("❌ Document not found with ID: " + id);
            return ResponseEntity.notFound().build();
        }

        documentSinistreRepository.deleteById(id);
        System.out.println("✅ Document deleted successfully");
        return ResponseEntity.noContent().build();
    }
}
