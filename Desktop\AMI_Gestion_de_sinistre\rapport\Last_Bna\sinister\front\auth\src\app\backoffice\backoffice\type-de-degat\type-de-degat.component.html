<div class="container">
  <div class="header">
    <h2>Gestion des Types de Dégâts</h2>
    <button class="create-btn" (click)="openModal()" [disabled]="isLoading">
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
        <path d="M12 5V19M5 12H19" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
      Créer un type de dégât
    </button>
  </div>

  <div *ngIf="errorMessage" class="error-message">
    {{ errorMessage }}
  </div>

  <div *ngIf="isLoading" class="loading-spinner">
    <div class="spinner"></div>
  </div>

  <div class="table-container" *ngIf="!isLoading">
    <div class="table-responsive">
      <table>
        <thead>
          <tr>
            <th>Code</th>
            <th>Libellé</th>
            <th>Description</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let type of typeDegats">
            <td>{{type.code}}</td>
            <td>{{type.libelle}}</td>
            <td>{{type.description || '-'}}</td>
            <td class="actions">
              <button class="icon-btn edit" (click)="editTypeDegat(type)" [disabled]="isLoading">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                  <path d="M11 4H4C3.46957 4 2.96086 4.21071 2.58579 4.58579C2.21071 4.96086 2 5.46957 2 6V20C2 20.5304 2.21071 21.0391 2.58579 21.4142C2.96086 21.7893 3.46957 22 4 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V13" stroke="#1C3F93" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  <path d="M18.5 2.50001C18.8978 2.10219 19.4374 1.87869 20 1.87869C20.5626 1.87869 21.1022 2.10219 21.5 2.50001C21.8978 2.89784 22.1213 3.4374 22.1213 4.00001C22.1213 4.56262 21.8978 5.10219 21.5 5.50001L12 15L8 16L9 12L18.5 2.50001Z" stroke="#1C3F93" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </button>
              <button class="icon-btn delete" (click)="deleteTypeDegat(type)" [disabled]="isLoading">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                  <path d="M3 6H5H21" stroke="#FF4B55" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  <path d="M8 6V4C8 3.46957 8.21071 2.96086 8.58579 2.58579C8.96086 2.21071 9.46957 2 10 2H14C14.5304 2 15.0391 2.21071 15.4142 2.58579C15.7893 2.96086 16 3.46957 16 4V6M19 6V20C19 20.5304 18.7893 21.0391 18.4142 21.4142C18.0391 21.7893 17.5304 22 17 22H7C6.46957 22 5.96086 21.7893 5.58579 21.4142C5.21071 21.0391 5 20.5304 5 20V6H19Z" stroke="#FF4B55" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </button>
            </td>
          </tr>
          <!-- Lignes de test pour vérifier le défilement -->

        </tbody>
      </table>
    </div>
  </div>
</div>

<!-- Modal -->
<div class="modal" *ngIf="showModal">
  <div class="modal-content">
    <div class="modal-header">
      <h3>{{ editingTypeDegat ? 'Modifier' : 'Créer' }} un type de dégât</h3>
      <button class="close-btn" (click)="closeModal()" [disabled]="isLoading">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
          <path d="M18 6L6 18" stroke="#6B7280" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M6 6L18 18" stroke="#6B7280" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </button>
    </div>

    <form [formGroup]="typeDegatForm" (ngSubmit)="onSubmit()">
      <div *ngIf="errorMessage" class="error-message">
        {{ errorMessage }}
      </div>
      
      <div class="form-group">
        <label>Code <span class="required">*</span></label>
        <div class="input-wrapper">
          <select formControlName="code" [disabled]="isLoading">
            <option value="" disabled selected>Sélectionner un code</option>
            <option value="MATERIEL">MATERIEL</option>
            <option value="CORPOREL">CORPOREL</option>
            <option value="MAT_CORP">MAT_CORP</option>
            <option value="AUTRE">AUTRE</option>
          </select>
        </div>
        <div class="error-message" *ngIf="typeDegatForm.get('code')?.invalid && typeDegatForm.get('code')?.touched">
          Le code est requis
        </div>
      </div>

      <div class="form-group">
        <label>Libellé <span class="required">*</span></label>
        <div class="input-wrapper">
          <input type="text" formControlName="libelle" placeholder="Entrez le libellé" [disabled]="isLoading">
        </div>
        <div class="error-message" *ngIf="typeDegatForm.get('libelle')?.invalid && typeDegatForm.get('libelle')?.touched">
          Le libellé est requis
        </div>
      </div>

      <div class="form-group">
        <label>Description</label>
        <div class="input-wrapper">
          <textarea formControlName="description" placeholder="Entrez une description (optionnelle)" [disabled]="isLoading"></textarea>
        </div>
      </div>

      <div class="modal-footer">
        <button type="button" class="btn-secondary" (click)="closeModal()" [disabled]="isLoading">Annuler</button>
        <button type="submit" class="btn-primary" [disabled]="!typeDegatForm.valid || isLoading">
          <span *ngIf="isLoading" class="spinner"></span>
          {{ editingTypeDegat ? 'Modifier' : 'Créer' }}
        </button>
      </div>
    </form>
  </div>
</div>