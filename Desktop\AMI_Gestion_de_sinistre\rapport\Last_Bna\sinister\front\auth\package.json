{"name": "auth", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "serve:ssr:auth": "node dist/auth/server/server.mjs"}, "private": true, "dependencies": {"@angular/cdk": "^19.2.5", "@angular/common": "^19.2.7", "@angular/compiler": "^19.2.7", "@angular/core": "^19.2.7", "@angular/forms": "^19.2.7", "@angular/material": "^19.2.5", "@angular/platform-browser": "^19.2.7", "@angular/platform-browser-dynamic": "^19.2.7", "@angular/platform-server": "^19.2.7", "@angular/router": "^19.2.7", "@angular/ssr": "^19.2.9", "angular-websocket": "^2.0.1", "bootstrap": "^5.3.3", "express": "^4.18.2", "primeflex": "^4.0.0", "rxjs": "~7.8.0", "three": "^0.175.0", "three-obj-loader": "^1.1.3", "three-stdlib": "^2.35.15", "tslib": "^2.3.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.2.9", "@angular/cli": "^19.2.9", "@angular/compiler-cli": "^19.2.7", "@types/express": "^4.17.17", "@types/jasmine": "~5.1.0", "@types/node": "^18.18.0", "@types/three": "^0.175.0", "jasmine-core": "~5.6.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.7.2"}}