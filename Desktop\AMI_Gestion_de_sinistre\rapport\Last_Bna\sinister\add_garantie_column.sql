-- ========================================
-- ADD GARANTIE COLUMN TO EVENEMENT TABLE
-- ========================================

-- 1. Add garantie column to evenement table
ALTER TABLE evenement ADD COLUMN garantie TEXT;

-- 2. Verify the column was added
DESCRIBE evenement;

-- 3. Add sample data with garanties for testing
UPDATE evenement SET garantie = 'Responsabilité Civile,Dommages Matériels,Protection Juridique' 
WHERE code = 'COL_VEH' AND type_de_degat = 'MATERIEL';

UPDATE evenement SET garantie = 'Vol et Tentative de Vol,Assistance Dépannage' 
WHERE code = 'VOL_VEH' AND type_de_degat = 'MATERIEL';

UPDATE evenement SET garantie = 'Responsabilité Civile,Dommages Corporels,Protection Juridique' 
WHERE code = 'ACC_CORP' AND type_de_degat = 'CORPOREL';

UPDATE evenement SET garantie = '<PERSON><PERSON> de <PERSON>,Dommages Matériels' 
WHERE code = 'BRI_GLACE' AND type_de_degat = 'MATERIEL';

UPDATE evenement SET garantie = 'Incendie,Dommages Matériels,Assistance Dépannage' 
WHERE code = 'INCENDIE' AND type_de_degat = 'MAT_CORP';

-- 4. Verify the data was inserted
SELECT id, code, libelle, type_de_degat, tier, responsabilite, garantie 
FROM evenement 
WHERE garantie IS NOT NULL;

-- 5. Test query that will be used by the application
SELECT * FROM evenement 
WHERE code = 'COL_VEH' AND type_de_degat = 'MATERIEL';

-- ========================================
-- NOTES:
-- ========================================
-- The garantie column stores comma-separated guarantee names
-- Example: "Responsabilité Civile,Dommages Matériels,Protection Juridique"
-- The application will parse this string to get individual guarantees
-- ========================================
