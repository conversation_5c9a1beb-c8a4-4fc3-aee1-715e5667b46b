{"version": 3, "sources": ["../../../../../../node_modules/three/examples/jsm/loaders/MTLLoader.js"], "sourcesContent": ["import { Color, ColorManagement, DefaultLoadingManager, FileLoader, FrontSide, Loader, LoaderUtils, MeshPhongMaterial, RepeatWrapping, TextureLoader, Vector2, SRGBColorSpace } from 'three';\n\n/**\n * A loader for the MTL format.\n *\n * The Material Template Library format (MTL) or .MTL File Format is a companion file format\n * to OBJ that describes surface shading (material) properties of objects within one or more\n * OBJ files.\n *\n * ```js\n * const loader = new MTLLoader();\n * const materials = await loader.loadAsync( 'models/obj/male02/male02.mtl' );\n *\n * const objLoader = new OBJLoader();\n * objLoader.setMaterials( materials );\n * ```\n *\n * @augments Loader\n */\nclass MTLLoader extends Loader {\n  constructor(manager) {\n    super(manager);\n  }\n\n  /**\n   * Starts loading from the given URL and passes the loaded MTL asset\n   * to the `onLoad()` callback.\n   *\n   * @param {string} url - The path/URL of the file to be loaded. This can also be a data URI.\n   * @param {function(MaterialCreator)} onLoad - Executed when the loading process has been finished.\n   * @param {onProgressCallback} onProgress - Executed while the loading is in progress.\n   * @param {onErrorCallback} onError - Executed when errors occur.\n   */\n  load(url, onLoad, onProgress, onError) {\n    const scope = this;\n    const path = this.path === '' ? LoaderUtils.extractUrlBase(url) : this.path;\n    const loader = new FileLoader(this.manager);\n    loader.setPath(this.path);\n    loader.setRequestHeader(this.requestHeader);\n    loader.setWithCredentials(this.withCredentials);\n    loader.load(url, function (text) {\n      try {\n        onLoad(scope.parse(text, path));\n      } catch (e) {\n        if (onError) {\n          onError(e);\n        } else {\n          console.error(e);\n        }\n        scope.manager.itemError(url);\n      }\n    }, onProgress, onError);\n  }\n\n  /**\n   * Sets the material options.\n   *\n   * @param {MTLLoader~MaterialOptions} value - The material options.\n   * @return {MTLLoader} A reference to this loader.\n   */\n  setMaterialOptions(value) {\n    this.materialOptions = value;\n    return this;\n  }\n\n  /**\n   * Parses the given MTL data and returns the resulting material creator.\n   *\n   * @param {string} text - The raw MTL data as a string.\n   * @param {string} path - The URL base path.\n   * @return {MaterialCreator} The material creator.\n   */\n  parse(text, path) {\n    const lines = text.split('\\n');\n    let info = {};\n    const delimiter_pattern = /\\s+/;\n    const materialsInfo = {};\n    for (let i = 0; i < lines.length; i++) {\n      let line = lines[i];\n      line = line.trim();\n      if (line.length === 0 || line.charAt(0) === '#') {\n        // Blank line or comment ignore\n        continue;\n      }\n      const pos = line.indexOf(' ');\n      let key = pos >= 0 ? line.substring(0, pos) : line;\n      key = key.toLowerCase();\n      let value = pos >= 0 ? line.substring(pos + 1) : '';\n      value = value.trim();\n      if (key === 'newmtl') {\n        // New material\n\n        info = {\n          name: value\n        };\n        materialsInfo[value] = info;\n      } else {\n        if (key === 'ka' || key === 'kd' || key === 'ks' || key === 'ke') {\n          const ss = value.split(delimiter_pattern, 3);\n          info[key] = [parseFloat(ss[0]), parseFloat(ss[1]), parseFloat(ss[2])];\n        } else {\n          info[key] = value;\n        }\n      }\n    }\n    const materialCreator = new MaterialCreator(this.resourcePath || path, this.materialOptions);\n    materialCreator.setCrossOrigin(this.crossOrigin);\n    materialCreator.setManager(this.manager);\n    materialCreator.setMaterials(materialsInfo);\n    return materialCreator;\n  }\n}\n\n/**\n * Material options of `MTLLoader`.\n *\n * @typedef {Object} MTLLoader~MaterialOptions\n * @property {(FrontSide|BackSide|DoubleSide)} [side=FrontSide] - Which side to apply the material.\n * @property {(RepeatWrapping|ClampToEdgeWrapping|MirroredRepeatWrapping)} [wrap=RepeatWrapping] - What type of wrapping to apply for textures.\n * @property {boolean} [normalizeRGB=false] - Whether RGB colors should be normalized to `0-1` from `0-255`.\n * @property {boolean} [ignoreZeroRGBs=false] - Ignore values of RGBs (Ka,Kd,Ks) that are all 0's.\n */\n\nclass MaterialCreator {\n  constructor(baseUrl = '', options = {}) {\n    this.baseUrl = baseUrl;\n    this.options = options;\n    this.materialsInfo = {};\n    this.materials = {};\n    this.materialsArray = [];\n    this.nameLookup = {};\n    this.crossOrigin = 'anonymous';\n    this.side = this.options.side !== undefined ? this.options.side : FrontSide;\n    this.wrap = this.options.wrap !== undefined ? this.options.wrap : RepeatWrapping;\n  }\n  setCrossOrigin(value) {\n    this.crossOrigin = value;\n    return this;\n  }\n  setManager(value) {\n    this.manager = value;\n  }\n  setMaterials(materialsInfo) {\n    this.materialsInfo = this.convert(materialsInfo);\n    this.materials = {};\n    this.materialsArray = [];\n    this.nameLookup = {};\n  }\n  convert(materialsInfo) {\n    if (!this.options) return materialsInfo;\n    const converted = {};\n    for (const mn in materialsInfo) {\n      // Convert materials info into normalized form based on options\n\n      const mat = materialsInfo[mn];\n      const covmat = {};\n      converted[mn] = covmat;\n      for (const prop in mat) {\n        let save = true;\n        let value = mat[prop];\n        const lprop = prop.toLowerCase();\n        switch (lprop) {\n          case 'kd':\n          case 'ka':\n          case 'ks':\n            // Diffuse color (color under white light) using RGB values\n\n            if (this.options && this.options.normalizeRGB) {\n              value = [value[0] / 255, value[1] / 255, value[2] / 255];\n            }\n            if (this.options && this.options.ignoreZeroRGBs) {\n              if (value[0] === 0 && value[1] === 0 && value[2] === 0) {\n                // ignore\n\n                save = false;\n              }\n            }\n            break;\n          default:\n            break;\n        }\n        if (save) {\n          covmat[lprop] = value;\n        }\n      }\n    }\n    return converted;\n  }\n  preload() {\n    for (const mn in this.materialsInfo) {\n      this.create(mn);\n    }\n  }\n  getIndex(materialName) {\n    return this.nameLookup[materialName];\n  }\n  getAsArray() {\n    let index = 0;\n    for (const mn in this.materialsInfo) {\n      this.materialsArray[index] = this.create(mn);\n      this.nameLookup[mn] = index;\n      index++;\n    }\n    return this.materialsArray;\n  }\n  create(materialName) {\n    if (this.materials[materialName] === undefined) {\n      this.createMaterial_(materialName);\n    }\n    return this.materials[materialName];\n  }\n  createMaterial_(materialName) {\n    // Create material\n\n    const scope = this;\n    const mat = this.materialsInfo[materialName];\n    const params = {\n      name: materialName,\n      side: this.side\n    };\n    function resolveURL(baseUrl, url) {\n      if (typeof url !== 'string' || url === '') return '';\n\n      // Absolute URL\n      if (/^https?:\\/\\//i.test(url)) return url;\n      return baseUrl + url;\n    }\n    function setMapForType(mapType, value) {\n      if (params[mapType]) return; // Keep the first encountered texture\n\n      const texParams = scope.getTextureParams(value, params);\n      const map = scope.loadTexture(resolveURL(scope.baseUrl, texParams.url));\n      map.repeat.copy(texParams.scale);\n      map.offset.copy(texParams.offset);\n      map.wrapS = scope.wrap;\n      map.wrapT = scope.wrap;\n      if (mapType === 'map' || mapType === 'emissiveMap') {\n        map.colorSpace = SRGBColorSpace;\n      }\n      params[mapType] = map;\n    }\n    for (const prop in mat) {\n      const value = mat[prop];\n      let n;\n      if (value === '') continue;\n      switch (prop.toLowerCase()) {\n        // Ns is material specular exponent\n\n        case 'kd':\n          // Diffuse color (color under white light) using RGB values\n\n          params.color = ColorManagement.toWorkingColorSpace(new Color().fromArray(value), SRGBColorSpace);\n          break;\n        case 'ks':\n          // Specular color (color when light is reflected from shiny surface) using RGB values\n          params.specular = ColorManagement.toWorkingColorSpace(new Color().fromArray(value), SRGBColorSpace);\n          break;\n        case 'ke':\n          // Emissive using RGB values\n          params.emissive = ColorManagement.toWorkingColorSpace(new Color().fromArray(value), SRGBColorSpace);\n          break;\n        case 'map_kd':\n          // Diffuse texture map\n\n          setMapForType('map', value);\n          break;\n        case 'map_ks':\n          // Specular map\n\n          setMapForType('specularMap', value);\n          break;\n        case 'map_ke':\n          // Emissive map\n\n          setMapForType('emissiveMap', value);\n          break;\n        case 'norm':\n          setMapForType('normalMap', value);\n          break;\n        case 'map_bump':\n        case 'bump':\n          // Bump texture map\n\n          setMapForType('bumpMap', value);\n          break;\n        case 'disp':\n          // Displacement texture map\n\n          setMapForType('displacementMap', value);\n          break;\n        case 'map_d':\n          // Alpha map\n\n          setMapForType('alphaMap', value);\n          params.transparent = true;\n          break;\n        case 'ns':\n          // The specular exponent (defines the focus of the specular highlight)\n          // A high exponent results in a tight, concentrated highlight. Ns values normally range from 0 to 1000.\n\n          params.shininess = parseFloat(value);\n          break;\n        case 'd':\n          n = parseFloat(value);\n          if (n < 1) {\n            params.opacity = n;\n            params.transparent = true;\n          }\n          break;\n        case 'tr':\n          n = parseFloat(value);\n          if (this.options && this.options.invertTrProperty) n = 1 - n;\n          if (n > 0) {\n            params.opacity = 1 - n;\n            params.transparent = true;\n          }\n          break;\n        default:\n          break;\n      }\n    }\n    this.materials[materialName] = new MeshPhongMaterial(params);\n    return this.materials[materialName];\n  }\n  getTextureParams(value, matParams) {\n    const texParams = {\n      scale: new Vector2(1, 1),\n      offset: new Vector2(0, 0)\n    };\n    const items = value.split(/\\s+/);\n    let pos;\n    pos = items.indexOf('-bm');\n    if (pos >= 0) {\n      matParams.bumpScale = parseFloat(items[pos + 1]);\n      items.splice(pos, 2);\n    }\n    pos = items.indexOf('-mm');\n    if (pos >= 0) {\n      matParams.displacementBias = parseFloat(items[pos + 1]);\n      matParams.displacementScale = parseFloat(items[pos + 2]);\n      items.splice(pos, 3);\n    }\n    pos = items.indexOf('-s');\n    if (pos >= 0) {\n      texParams.scale.set(parseFloat(items[pos + 1]), parseFloat(items[pos + 2]));\n      items.splice(pos, 4); // we expect 3 parameters here!\n    }\n    pos = items.indexOf('-o');\n    if (pos >= 0) {\n      texParams.offset.set(parseFloat(items[pos + 1]), parseFloat(items[pos + 2]));\n      items.splice(pos, 4); // we expect 3 parameters here!\n    }\n    texParams.url = items.join(' ').trim();\n    return texParams;\n  }\n  loadTexture(url, mapping, onLoad, onProgress, onError) {\n    const manager = this.manager !== undefined ? this.manager : DefaultLoadingManager;\n    let loader = manager.getHandler(url);\n    if (loader === null) {\n      loader = new TextureLoader(manager);\n    }\n    if (loader.setCrossOrigin) loader.setCrossOrigin(this.crossOrigin);\n    const texture = loader.load(url, onLoad, onProgress, onError);\n    if (mapping !== undefined) texture.mapping = mapping;\n    return texture;\n  }\n}\nexport { MTLLoader };"], "mappings": ";;;;;;;;;;;;;;;;;;AAmBA,IAAM,YAAN,cAAwB,OAAO;AAAA,EAC7B,YAAY,SAAS;AACnB,UAAM,OAAO;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,KAAK,KAAK,QAAQ,YAAY,SAAS;AACrC,UAAM,QAAQ;AACd,UAAM,OAAO,KAAK,SAAS,KAAK,YAAY,eAAe,GAAG,IAAI,KAAK;AACvE,UAAM,SAAS,IAAI,WAAW,KAAK,OAAO;AAC1C,WAAO,QAAQ,KAAK,IAAI;AACxB,WAAO,iBAAiB,KAAK,aAAa;AAC1C,WAAO,mBAAmB,KAAK,eAAe;AAC9C,WAAO,KAAK,KAAK,SAAU,MAAM;AAC/B,UAAI;AACF,eAAO,MAAM,MAAM,MAAM,IAAI,CAAC;AAAA,MAChC,SAAS,GAAG;AACV,YAAI,SAAS;AACX,kBAAQ,CAAC;AAAA,QACX,OAAO;AACL,kBAAQ,MAAM,CAAC;AAAA,QACjB;AACA,cAAM,QAAQ,UAAU,GAAG;AAAA,MAC7B;AAAA,IACF,GAAG,YAAY,OAAO;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,mBAAmB,OAAO;AACxB,SAAK,kBAAkB;AACvB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,MAAM,MAAM,MAAM;AAChB,UAAM,QAAQ,KAAK,MAAM,IAAI;AAC7B,QAAI,OAAO,CAAC;AACZ,UAAM,oBAAoB;AAC1B,UAAM,gBAAgB,CAAC;AACvB,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,UAAI,OAAO,MAAM,CAAC;AAClB,aAAO,KAAK,KAAK;AACjB,UAAI,KAAK,WAAW,KAAK,KAAK,OAAO,CAAC,MAAM,KAAK;AAE/C;AAAA,MACF;AACA,YAAM,MAAM,KAAK,QAAQ,GAAG;AAC5B,UAAI,MAAM,OAAO,IAAI,KAAK,UAAU,GAAG,GAAG,IAAI;AAC9C,YAAM,IAAI,YAAY;AACtB,UAAI,QAAQ,OAAO,IAAI,KAAK,UAAU,MAAM,CAAC,IAAI;AACjD,cAAQ,MAAM,KAAK;AACnB,UAAI,QAAQ,UAAU;AAGpB,eAAO;AAAA,UACL,MAAM;AAAA,QACR;AACA,sBAAc,KAAK,IAAI;AAAA,MACzB,OAAO;AACL,YAAI,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,MAAM;AAChE,gBAAM,KAAK,MAAM,MAAM,mBAAmB,CAAC;AAC3C,eAAK,GAAG,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,GAAG,WAAW,GAAG,CAAC,CAAC,GAAG,WAAW,GAAG,CAAC,CAAC,CAAC;AAAA,QACtE,OAAO;AACL,eAAK,GAAG,IAAI;AAAA,QACd;AAAA,MACF;AAAA,IACF;AACA,UAAM,kBAAkB,IAAI,gBAAgB,KAAK,gBAAgB,MAAM,KAAK,eAAe;AAC3F,oBAAgB,eAAe,KAAK,WAAW;AAC/C,oBAAgB,WAAW,KAAK,OAAO;AACvC,oBAAgB,aAAa,aAAa;AAC1C,WAAO;AAAA,EACT;AACF;AAYA,IAAM,kBAAN,MAAsB;AAAA,EACpB,YAAY,UAAU,IAAI,UAAU,CAAC,GAAG;AACtC,SAAK,UAAU;AACf,SAAK,UAAU;AACf,SAAK,gBAAgB,CAAC;AACtB,SAAK,YAAY,CAAC;AAClB,SAAK,iBAAiB,CAAC;AACvB,SAAK,aAAa,CAAC;AACnB,SAAK,cAAc;AACnB,SAAK,OAAO,KAAK,QAAQ,SAAS,SAAY,KAAK,QAAQ,OAAO;AAClE,SAAK,OAAO,KAAK,QAAQ,SAAS,SAAY,KAAK,QAAQ,OAAO;AAAA,EACpE;AAAA,EACA,eAAe,OAAO;AACpB,SAAK,cAAc;AACnB,WAAO;AAAA,EACT;AAAA,EACA,WAAW,OAAO;AAChB,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,aAAa,eAAe;AAC1B,SAAK,gBAAgB,KAAK,QAAQ,aAAa;AAC/C,SAAK,YAAY,CAAC;AAClB,SAAK,iBAAiB,CAAC;AACvB,SAAK,aAAa,CAAC;AAAA,EACrB;AAAA,EACA,QAAQ,eAAe;AACrB,QAAI,CAAC,KAAK,QAAS,QAAO;AAC1B,UAAM,YAAY,CAAC;AACnB,eAAW,MAAM,eAAe;AAG9B,YAAM,MAAM,cAAc,EAAE;AAC5B,YAAM,SAAS,CAAC;AAChB,gBAAU,EAAE,IAAI;AAChB,iBAAW,QAAQ,KAAK;AACtB,YAAI,OAAO;AACX,YAAI,QAAQ,IAAI,IAAI;AACpB,cAAM,QAAQ,KAAK,YAAY;AAC/B,gBAAQ,OAAO;AAAA,UACb,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAGH,gBAAI,KAAK,WAAW,KAAK,QAAQ,cAAc;AAC7C,sBAAQ,CAAC,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,GAAG;AAAA,YACzD;AACA,gBAAI,KAAK,WAAW,KAAK,QAAQ,gBAAgB;AAC/C,kBAAI,MAAM,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM,GAAG;AAGtD,uBAAO;AAAA,cACT;AAAA,YACF;AACA;AAAA,UACF;AACE;AAAA,QACJ;AACA,YAAI,MAAM;AACR,iBAAO,KAAK,IAAI;AAAA,QAClB;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,UAAU;AACR,eAAW,MAAM,KAAK,eAAe;AACnC,WAAK,OAAO,EAAE;AAAA,IAChB;AAAA,EACF;AAAA,EACA,SAAS,cAAc;AACrB,WAAO,KAAK,WAAW,YAAY;AAAA,EACrC;AAAA,EACA,aAAa;AACX,QAAI,QAAQ;AACZ,eAAW,MAAM,KAAK,eAAe;AACnC,WAAK,eAAe,KAAK,IAAI,KAAK,OAAO,EAAE;AAC3C,WAAK,WAAW,EAAE,IAAI;AACtB;AAAA,IACF;AACA,WAAO,KAAK;AAAA,EACd;AAAA,EACA,OAAO,cAAc;AACnB,QAAI,KAAK,UAAU,YAAY,MAAM,QAAW;AAC9C,WAAK,gBAAgB,YAAY;AAAA,IACnC;AACA,WAAO,KAAK,UAAU,YAAY;AAAA,EACpC;AAAA,EACA,gBAAgB,cAAc;AAG5B,UAAM,QAAQ;AACd,UAAM,MAAM,KAAK,cAAc,YAAY;AAC3C,UAAM,SAAS;AAAA,MACb,MAAM;AAAA,MACN,MAAM,KAAK;AAAA,IACb;AACA,aAAS,WAAW,SAAS,KAAK;AAChC,UAAI,OAAO,QAAQ,YAAY,QAAQ,GAAI,QAAO;AAGlD,UAAI,gBAAgB,KAAK,GAAG,EAAG,QAAO;AACtC,aAAO,UAAU;AAAA,IACnB;AACA,aAAS,cAAc,SAAS,OAAO;AACrC,UAAI,OAAO,OAAO,EAAG;AAErB,YAAM,YAAY,MAAM,iBAAiB,OAAO,MAAM;AACtD,YAAM,MAAM,MAAM,YAAY,WAAW,MAAM,SAAS,UAAU,GAAG,CAAC;AACtE,UAAI,OAAO,KAAK,UAAU,KAAK;AAC/B,UAAI,OAAO,KAAK,UAAU,MAAM;AAChC,UAAI,QAAQ,MAAM;AAClB,UAAI,QAAQ,MAAM;AAClB,UAAI,YAAY,SAAS,YAAY,eAAe;AAClD,YAAI,aAAa;AAAA,MACnB;AACA,aAAO,OAAO,IAAI;AAAA,IACpB;AACA,eAAW,QAAQ,KAAK;AACtB,YAAM,QAAQ,IAAI,IAAI;AACtB,UAAI;AACJ,UAAI,UAAU,GAAI;AAClB,cAAQ,KAAK,YAAY,GAAG;AAAA;AAAA,QAG1B,KAAK;AAGH,iBAAO,QAAQ,gBAAgB,oBAAoB,IAAI,MAAM,EAAE,UAAU,KAAK,GAAG,cAAc;AAC/F;AAAA,QACF,KAAK;AAEH,iBAAO,WAAW,gBAAgB,oBAAoB,IAAI,MAAM,EAAE,UAAU,KAAK,GAAG,cAAc;AAClG;AAAA,QACF,KAAK;AAEH,iBAAO,WAAW,gBAAgB,oBAAoB,IAAI,MAAM,EAAE,UAAU,KAAK,GAAG,cAAc;AAClG;AAAA,QACF,KAAK;AAGH,wBAAc,OAAO,KAAK;AAC1B;AAAA,QACF,KAAK;AAGH,wBAAc,eAAe,KAAK;AAClC;AAAA,QACF,KAAK;AAGH,wBAAc,eAAe,KAAK;AAClC;AAAA,QACF,KAAK;AACH,wBAAc,aAAa,KAAK;AAChC;AAAA,QACF,KAAK;AAAA,QACL,KAAK;AAGH,wBAAc,WAAW,KAAK;AAC9B;AAAA,QACF,KAAK;AAGH,wBAAc,mBAAmB,KAAK;AACtC;AAAA,QACF,KAAK;AAGH,wBAAc,YAAY,KAAK;AAC/B,iBAAO,cAAc;AACrB;AAAA,QACF,KAAK;AAIH,iBAAO,YAAY,WAAW,KAAK;AACnC;AAAA,QACF,KAAK;AACH,cAAI,WAAW,KAAK;AACpB,cAAI,IAAI,GAAG;AACT,mBAAO,UAAU;AACjB,mBAAO,cAAc;AAAA,UACvB;AACA;AAAA,QACF,KAAK;AACH,cAAI,WAAW,KAAK;AACpB,cAAI,KAAK,WAAW,KAAK,QAAQ,iBAAkB,KAAI,IAAI;AAC3D,cAAI,IAAI,GAAG;AACT,mBAAO,UAAU,IAAI;AACrB,mBAAO,cAAc;AAAA,UACvB;AACA;AAAA,QACF;AACE;AAAA,MACJ;AAAA,IACF;AACA,SAAK,UAAU,YAAY,IAAI,IAAI,kBAAkB,MAAM;AAC3D,WAAO,KAAK,UAAU,YAAY;AAAA,EACpC;AAAA,EACA,iBAAiB,OAAO,WAAW;AACjC,UAAM,YAAY;AAAA,MAChB,OAAO,IAAI,QAAQ,GAAG,CAAC;AAAA,MACvB,QAAQ,IAAI,QAAQ,GAAG,CAAC;AAAA,IAC1B;AACA,UAAM,QAAQ,MAAM,MAAM,KAAK;AAC/B,QAAI;AACJ,UAAM,MAAM,QAAQ,KAAK;AACzB,QAAI,OAAO,GAAG;AACZ,gBAAU,YAAY,WAAW,MAAM,MAAM,CAAC,CAAC;AAC/C,YAAM,OAAO,KAAK,CAAC;AAAA,IACrB;AACA,UAAM,MAAM,QAAQ,KAAK;AACzB,QAAI,OAAO,GAAG;AACZ,gBAAU,mBAAmB,WAAW,MAAM,MAAM,CAAC,CAAC;AACtD,gBAAU,oBAAoB,WAAW,MAAM,MAAM,CAAC,CAAC;AACvD,YAAM,OAAO,KAAK,CAAC;AAAA,IACrB;AACA,UAAM,MAAM,QAAQ,IAAI;AACxB,QAAI,OAAO,GAAG;AACZ,gBAAU,MAAM,IAAI,WAAW,MAAM,MAAM,CAAC,CAAC,GAAG,WAAW,MAAM,MAAM,CAAC,CAAC,CAAC;AAC1E,YAAM,OAAO,KAAK,CAAC;AAAA,IACrB;AACA,UAAM,MAAM,QAAQ,IAAI;AACxB,QAAI,OAAO,GAAG;AACZ,gBAAU,OAAO,IAAI,WAAW,MAAM,MAAM,CAAC,CAAC,GAAG,WAAW,MAAM,MAAM,CAAC,CAAC,CAAC;AAC3E,YAAM,OAAO,KAAK,CAAC;AAAA,IACrB;AACA,cAAU,MAAM,MAAM,KAAK,GAAG,EAAE,KAAK;AACrC,WAAO;AAAA,EACT;AAAA,EACA,YAAY,KAAK,SAAS,QAAQ,YAAY,SAAS;AACrD,UAAM,UAAU,KAAK,YAAY,SAAY,KAAK,UAAU;AAC5D,QAAI,SAAS,QAAQ,WAAW,GAAG;AACnC,QAAI,WAAW,MAAM;AACnB,eAAS,IAAI,cAAc,OAAO;AAAA,IACpC;AACA,QAAI,OAAO,eAAgB,QAAO,eAAe,KAAK,WAAW;AACjE,UAAM,UAAU,OAAO,KAAK,KAAK,QAAQ,YAAY,OAAO;AAC5D,QAAI,YAAY,OAAW,SAAQ,UAAU;AAC7C,WAAO;AAAA,EACT;AACF;", "names": []}