import { CommonModule, isPlatformBrowser } from '@angular/common';
import { AfterViewInit, Component, ElementRef, Inject, PLATFORM_ID, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import * as THREE from 'three';
import { MTLLoader } from 'three/examples/jsm/loaders/MTLLoader.js';
import { OBJLoader } from 'three/examples/jsm/loaders/OBJLoader.js';
import { AuthService } from '../auth.service';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule
  ],
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.css']
})
export class LoginComponent implements AfterViewInit {
  @ViewChild('logoCanvas', { static: false }) logoCanvasRef!: ElementRef;

  loginForm: FormGroup;
  loginError: boolean = false;


  formConfig = {
    fields: [
      {
        name: 'Nom dutilisateur',
        label: 'Nom dutilisateur',
        type: 'text',
        placeholder: 'Entrez votre nom dutilisateur',
        validators: [Validators.required]
      },
      {
        name: 'Mot de passe',
        label: 'Mot de passe',
        type: 'password',
        placeholder: 'Entrez le mot de passe',
        validators: [Validators.required]
      }
    ]
  };

  constructor(
    private fb: FormBuilder,
    private authService: AuthService,
    private router: Router,
    @Inject(PLATFORM_ID) private platformId: Object
  ) {
    this.loginForm = this.createForm();
  }

  createForm(): FormGroup {
    const group = this.fb.group({});
    this.formConfig.fields.forEach(field => {
      group.addControl(
        field.name,
        this.fb.control('', field.validators)
      );
    });
    return group;
  }

  onSubmit() {
    if (this.loginForm.valid) {
      this.authService.login(
        this.loginForm.value['Nom dutilisateur'],
        this.loginForm.value['Mot de passe']
      ).subscribe({
        next: () => {
          this.loginError = false;
          const userRole = localStorage.getItem('userRole');
          if (userRole === 'ROLE_ADMIN') {
            this.router.navigate(['/backoffice']);
          } else if (userRole === 'ROLE_AGENT') {
            this.router.navigate(['/sinistre']);
          } else {
            this.router.navigate(['/login']);
          }
        },
        error: (error) => {
          this.loginError = true;
          console.error('Login error:', error);
        }
      });
    }
  }

  trackByName(index: number, field: any): string {
    return field.name;
  }

  ngAfterViewInit(): void {
    if (!isPlatformBrowser(this.platformId)) return; // Prevent SSR crash
  
    const container = this.logoCanvasRef.nativeElement;
  
    const scene = new THREE.Scene();
    const camera = new THREE.PerspectiveCamera(75, container.clientWidth / container.clientHeight, 0.1, 1000);
    const renderer = new THREE.WebGLRenderer({ alpha: true });
    renderer.setSize(container.clientWidth, container.clientHeight);
    container.appendChild(renderer.domElement);
  
    // Lighting
    const ambientLight = new THREE.AmbientLight(0xffffff, 1.5);
    scene.add(ambientLight);
  
    const directionalLight = new THREE.DirectionalLight(0xffffff, 2.5);
    directionalLight.position.set(5, 5, 5);
    scene.add(directionalLight);
  
    const pointLight = new THREE.PointLight(0xff0000, 1, 100);
    pointLight.position.set(2, 2, 2);
    scene.add(pointLight);
  
    const spotLight = new THREE.SpotLight(0x00ff00, 1, 10, Math.PI / 6);
    spotLight.position.set(0, 3, 5);
    spotLight.target.position.set(0, 0, 0);
    scene.add(spotLight);
    scene.add(spotLight.target);
  
    // Additional white light for more brightness
    const extraLight = new THREE.PointLight(0xffffff, 1.2);
    extraLight.position.set(-3, 3, 3);
    scene.add(extraLight);
  
    // Load MTL and OBJ
    const mtlLoader = new MTLLoader();
    mtlLoader.load('assets/logo.mtl', (materials) => {
      materials.preload();
  
      const objLoader = new OBJLoader();
      objLoader.setMaterials(materials);
      objLoader.load('assets/logo.obj', (obj: THREE.Object3D) => {
        obj.scale.set(1, 1, 1);        // Increased size
        obj.position.y = 2;         // Slightly moved to top
        scene.add(obj);
      });
    });
  
    camera.position.z = 5;
  
    const animate = () => {
      requestAnimationFrame(animate);
      scene.rotation.y += 0.005;
      renderer.render(scene, camera);
    };
  
    animate();
  }
}
