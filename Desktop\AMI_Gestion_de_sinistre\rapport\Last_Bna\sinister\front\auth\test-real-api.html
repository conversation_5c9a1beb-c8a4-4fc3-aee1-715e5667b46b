<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Real CSM.ai API</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        .button { padding: 12px 24px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; font-size: 16px; }
        .button:hover { background: #0056b3; }
        .button:disabled { background: #ccc; cursor: not-allowed; }
        .log { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; font-family: monospace; white-space: pre-wrap; max-height: 400px; overflow-y: auto; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .info { color: blue; }
        .warning { color: orange; }
        input[type="file"] { margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 5px; width: 100%; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .status.success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .status.error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .status.warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test Real CSM.ai API</h1>
        <p>Testez si l'API CSM.ai fonctionne vraiment avec votre clé API</p>
        
        <div class="section">
            <h2>📷 Upload Image for Real 3D Generation</h2>
            <input type="file" id="imageInput" accept="image/*">
            <button class="button" onclick="testRealAPI()" id="testBtn">🚀 Generate Real 3D Model</button>
            <div id="status" class="status" style="display: none;"></div>
        </div>

        <div class="section">
            <h2>📊 Check Session Status</h2>
            <input type="text" id="sessionIdInput" placeholder="Enter Session ID" style="width: 300px; padding: 8px; margin-right: 10px;">
            <button class="button" onclick="checkStatus()">🔍 Check Status</button>
        </div>

        <div class="section">
            <h2>📝 API Logs</h2>
            <div id="logs" class="log">Ready to test CSM.ai API...\nAPI Key: 793308e392807ce9d1Ad41F3b41EafDF\nEndpoint: https://api.csm.ai/v3\n\n</div>
            <button class="button" onclick="clearLogs()">🗑️ Clear Logs</button>
        </div>
    </div>

    <script>
        const API_KEY = '793308e392807ce9d1Ad41F3b41EafDF';
        const API_BASE = 'https://api.csm.ai/v3';
        let currentSessionId = null;

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('logs');
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : 'info';
            logElement.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function clearLogs() {
            document.getElementById('logs').innerHTML = 'Logs cleared...\n';
        }

        function showStatus(message, type) {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
            statusEl.style.display = 'block';
        }

        async function convertImageToBase64(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = () => resolve(reader.result);
                reader.onerror = reject;
                reader.readAsDataURL(file);
            });
        }

        async function testRealAPI() {
            const fileInput = document.getElementById('imageInput');
            const file = fileInput.files[0];
            
            if (!file) {
                log('❌ Please select an image file first', 'error');
                showStatus('Please select an image file first', 'error');
                return;
            }

            const testBtn = document.getElementById('testBtn');
            testBtn.disabled = true;
            testBtn.textContent = '⏳ Generating...';

            log(`🚀 Starting REAL 3D generation for: ${file.name} (${file.size} bytes)`);
            showStatus('Converting image and sending to CSM.ai...', 'warning');
            
            try {
                // Convert image to base64
                log('📸 Converting image to base64...');
                const imageDataUrl = await convertImageToBase64(file);
                log('✅ Image converted to base64');

                // Prepare request exactly as CSM.ai API expects
                const requestBody = {
                    type: 'image_to_3d',
                    input: {
                        image: imageDataUrl,
                        manual_segmentation: false,
                        num_variations: 1,
                        model: 'sculpt',
                        settings: {
                            geometry_model: 'base',
                            texture_model: 'none',
                            topology: 'tris',
                            resolution: 100000,
                            symmetry: 'off',
                            scaled_bbox: [-1, -1, -1],
                            preserve_aspect_ratio: false,
                            pivot_point: [0, -0.5, 0]
                        }
                    }
                };

                log('🌐 Sending request to CSM.ai API...');
                log(`📡 URL: ${API_BASE}/sessions/`);
                log(`🔑 API Key: ${API_KEY.substring(0, 8)}...`);
                
                const response = await fetch(`${API_BASE}/sessions/`, {
                    method: 'POST',
                    headers: {
                        'x-api-key': API_KEY,
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(requestBody)
                });

                log(`📡 Response status: ${response.status} ${response.statusText}`);
                
                if (!response.ok) {
                    const errorText = await response.text();
                    log(`❌ API Error Response: ${errorText}`, 'error');
                    throw new Error(`HTTP ${response.status}: ${response.statusText}\n${errorText}`);
                }

                const result = await response.json();
                currentSessionId = result._id;
                
                log(`✅ SUCCESS! Real 3D generation started!`, 'success');
                log(`📋 Session ID: ${currentSessionId}`, 'success');
                log(`📊 Status: ${result.status}`, 'info');
                log(`🎯 Type: ${result.type}`, 'info');
                
                // Auto-fill session ID for status check
                document.getElementById('sessionIdInput').value = currentSessionId;
                
                showStatus(`✅ 3D Generation Started! Session ID: ${currentSessionId}`, 'success');
                
                log('🔄 Starting automatic status polling...');
                pollSessionStatus(currentSessionId);

            } catch (error) {
                log(`❌ FAILED: ${error.message}`, 'error');
                showStatus(`❌ Failed: ${error.message}`, 'error');
                console.error('Full error:', error);
            } finally {
                testBtn.disabled = false;
                testBtn.textContent = '🚀 Generate Real 3D Model';
            }
        }

        async function checkStatus() {
            const sessionId = document.getElementById('sessionIdInput').value.trim();
            
            if (!sessionId) {
                log('❌ Please enter a Session ID', 'error');
                return;
            }

            log(`🔍 Checking status for session: ${sessionId}`);
            
            try {
                const response = await fetch(`${API_BASE}/sessions/${sessionId}`, {
                    method: 'GET',
                    headers: {
                        'x-api-key': API_KEY,
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    const errorText = await response.text();
                    log(`❌ Status check failed: ${response.status} ${response.statusText}`, 'error');
                    log(`❌ Error details: ${errorText}`, 'error');
                    return;
                }

                const result = await response.json();
                log(`📊 Status: ${result.status}`, 'info');
                
                if (result.status === 'complete') {
                    log('🎉 Generation COMPLETE!', 'success');
                    if (result.output?.meshes?.[0]?.data?.glb_url) {
                        const modelUrl = result.output.meshes[0].data.glb_url;
                        log(`🎯 GLB Model URL: ${modelUrl}`, 'success');
                        log(`🎯 You can now use this URL to load the real 3D model!`, 'success');
                        showStatus(`✅ 3D Model Ready! URL: ${modelUrl}`, 'success');
                    }
                    if (result.output?.meshes?.[0]?.data?.obj_url) {
                        log(`🎯 OBJ Model URL: ${result.output.meshes[0].data.obj_url}`, 'success');
                    }
                } else if (result.status === 'failed') {
                    log('❌ Generation FAILED', 'error');
                    showStatus('❌ 3D Generation Failed', 'error');
                } else {
                    log(`⏳ Still processing... Status: ${result.status}`, 'warning');
                    showStatus(`⏳ Processing... Status: ${result.status}`, 'warning');
                }

            } catch (error) {
                log(`❌ Error checking status: ${error.message}`, 'error');
                console.error('Full error:', error);
            }
        }

        async function pollSessionStatus(sessionId, maxAttempts = 20) {
            let attempts = 0;
            
            const poll = async () => {
                attempts++;
                log(`🔄 Polling attempt ${attempts}/${maxAttempts}...`);
                
                try {
                    const response = await fetch(`${API_BASE}/sessions/${sessionId}`, {
                        method: 'GET',
                        headers: {
                            'x-api-key': API_KEY,
                            'Content-Type': 'application/json'
                        }
                    });

                    if (response.ok) {
                        const result = await response.json();
                        
                        if (result.status === 'complete') {
                            log('🎉 REAL 3D MODEL GENERATED SUCCESSFULLY!', 'success');
                            if (result.output?.meshes?.[0]?.data?.glb_url) {
                                const modelUrl = result.output.meshes[0].data.glb_url;
                                log(`🎯 REAL MODEL URL: ${modelUrl}`, 'success');
                                showStatus(`✅ Real 3D Model Generated! URL: ${modelUrl}`, 'success');
                            }
                            return;
                        } else if (result.status === 'failed') {
                            log('❌ 3D Generation failed', 'error');
                            showStatus('❌ 3D Generation Failed', 'error');
                            return;
                        } else {
                            log(`⏳ Status: ${result.status} - continuing to poll...`);
                        }
                    }
                    
                    if (attempts < maxAttempts) {
                        setTimeout(poll, 10000); // Poll every 10 seconds
                    } else {
                        log('⏰ Polling timeout reached', 'warning');
                        showStatus('⏰ Polling timeout - check manually', 'warning');
                    }
                    
                } catch (error) {
                    log(`❌ Polling error: ${error.message}`, 'error');
                    if (attempts < maxAttempts) {
                        setTimeout(poll, 10000);
                    }
                }
            };
            
            poll();
        }

        // Log initial info
        log('🔑 API Key: ' + API_KEY);
        log('🌐 API Base: ' + API_BASE);
        log('📝 Ready to test REAL CSM.ai API');
        log('💡 Upload an image and click "Generate Real 3D Model" to test');
    </script>
</body>
</html>
