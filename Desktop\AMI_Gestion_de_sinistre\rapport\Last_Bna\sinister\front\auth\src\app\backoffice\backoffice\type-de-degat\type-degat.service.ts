// src/app/backoffice/type-degat/type-degat.service.ts
import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';

export interface TypeDegat {
  id: number;
  code: string;
  libelle: string;
  description?: string;
}

@Injectable({
  providedIn: 'root'
})
export class TypeDegatService {
  private apiUrl = 'http://localhost:8080/api/type-degats';

  constructor(private http: HttpClient) {}

  getAll(): Observable<TypeDegat[]> {
    return this.http.get<TypeDegat[]>(this.apiUrl);
  }

  create(typeDegat: Omit<TypeDegat, 'id'>): Observable<TypeDegat> {
    return this.http.post<TypeDegat>(this.apiUrl, typeDegat);
  }

  update(id: number, typeDegat: TypeDegat): Observable<TypeDegat> {
    return this.http.put<TypeDegat>(`${this.apiUrl}/${id}`, typeDegat);
  }

  delete(id: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}`);
  }
}