import { createRequire } from 'module';const require = createRequire(import.meta.url);
import {
  AnimationCurves,
  AnimationDurations,
  DateAdapter,
  MAT_DATE_FORMATS,
  MAT_DATE_LOCALE,
  MAT_DATE_LOCALE_FACTORY,
  MAT_NATIVE_DATE_FORMATS,
  MatLine,
  MatLineModule,
  MatNativeDateModule,
  MatRippleLoader,
  NativeDateAdapter,
  NativeDateModule,
  VERSION,
  provideNativeDateAdapter,
  setLines
} from "./chunk-NCOBIOKU.js";
import {
  MAT_OPTGROUP,
  MAT_OPTION_PARENT_COMPONENT,
  MatOptgroup,
  MatOption,
  MatOptionModule,
  MatOptionSelectionChange,
  MatPseudoCheckbox,
  MatPseudoCheckboxModule,
  MatRippleModule,
  _countGroupLabelsBeforeOption,
  _getOptionScrollPosition
} from "./chunk-Y6DNDRCT.js";
import {
  ErrorStateMatcher,
  ShowOnDirtyErrorStateMatcher,
  _ErrorStateTracker
} from "./chunk-EXS5KYT2.js";
import {
  _MatInternalFormField
} from "./chunk-DFN4QZJC.js";
import {
  MAT_RIPPLE_GLOBAL_OPTIONS,
  MatRipple,
  RippleRef,
  RippleRenderer,
  RippleState,
  _StructuralStylesLoader,
  defaultRippleAnimationConfig
} from "./chunk-SLU6INCT.js";
import {
  MATERIAL_SANITY_CHECKS,
  MatCommonModule
} from "./chunk-INSLS6GC.js";
import "./chunk-HJTKKQ3X.js";
import "./chunk-5YE3MF2B.js";
import "./chunk-4A3CXPF2.js";
import "./chunk-X33BODJB.js";
import "./chunk-PHAQPJFA.js";
import "./chunk-KXIDSWWM.js";
import "./chunk-YRPTRWJS.js";
import "./chunk-4UBR7WAK.js";
import "./chunk-ZUJ64LXG.js";
import "./chunk-XCIYP5SE.js";
import "./chunk-OYTRG5F6.js";
import "./chunk-YHCV7DAQ.js";
export {
  AnimationCurves,
  AnimationDurations,
  DateAdapter,
  ErrorStateMatcher,
  MATERIAL_SANITY_CHECKS,
  MAT_DATE_FORMATS,
  MAT_DATE_LOCALE,
  MAT_DATE_LOCALE_FACTORY,
  MAT_NATIVE_DATE_FORMATS,
  MAT_OPTGROUP,
  MAT_OPTION_PARENT_COMPONENT,
  MAT_RIPPLE_GLOBAL_OPTIONS,
  MatCommonModule,
  MatLine,
  MatLineModule,
  MatNativeDateModule,
  MatOptgroup,
  MatOption,
  MatOptionModule,
  MatOptionSelectionChange,
  MatPseudoCheckbox,
  MatPseudoCheckboxModule,
  MatRipple,
  MatRippleLoader,
  MatRippleModule,
  NativeDateAdapter,
  NativeDateModule,
  RippleRef,
  RippleRenderer,
  RippleState,
  ShowOnDirtyErrorStateMatcher,
  VERSION,
  _ErrorStateTracker,
  _MatInternalFormField,
  _StructuralStylesLoader,
  _countGroupLabelsBeforeOption,
  _getOptionScrollPosition,
  defaultRippleAnimationConfig,
  provideNativeDateAdapter,
  setLines
};
