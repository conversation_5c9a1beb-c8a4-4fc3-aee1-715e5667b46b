import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';

@Component({
  selector: 'app-survenance-sinistre',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './survenance-sinistre.component.html',
  styleUrls: ['./survenance-sinistre.component.css']
})
export class SurvenanceSinistreComponent implements OnInit {
  survenanceForm!: FormGroup;
  isParking: boolean = false;
  isDriver: boolean = false;

  constructor(private fb: FormBuilder) {
    this.initializeForm();
  }

  ngOnInit() {}

  private initializeForm() {
    this.survenanceForm = this.fb.group({
      gouvernorat: ['', Validators.required],
      region: ['', Validators.required],
      cite: ['', Validators.required],
      codePostal: ['', Validators.required],
      adresse: ['', Validators.required],
      isParking: [false],
      isDriver: [false]
    });
  }

  toggleParking() {
    this.isParking = !this.isParking;
    this.survenanceForm.patchValue({ isParking: this.isParking });
  }

  toggleDriver() {
    this.isDriver = !this.isDriver;
    this.survenanceForm.patchValue({ isDriver: this.isDriver });
  }

  onSubmit() {
    if (this.survenanceForm.valid) {
      console.log(this.survenanceForm.value);
      // Handle form submission here
    } else {
      this.markFormGroupTouched(this.survenanceForm);
    }
  }

  private markFormGroupTouched(formGroup: FormGroup) {
    Object.values(formGroup.controls).forEach(control => {
      control.markAsTouched();

      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      }
    });
  }

  // Getter methods for form controls
  get gouvernorat() { return this.survenanceForm.get('gouvernorat'); }
  get region() { return this.survenanceForm.get('region'); }
  get cite() { return this.survenanceForm.get('cite'); }
  get codePostal() { return this.survenanceForm.get('codePostal'); }
  get adresse() { return this.survenanceForm.get('adresse'); }
}
