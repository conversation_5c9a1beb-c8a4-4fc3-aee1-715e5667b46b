<div class="container">
  <!-- Loading overlay -->
  @if (isLoading) {
    <div class="loading-overlay">
      <div class="spinner"></div>
    </div>
  }

  <!-- Success and error messages -->
  @if (successMessage) {
    <div class="alert alert-success">
      {{ successMessage }}
      <button type="button" class="close" (click)="successMessage = null">&times;</button>
    </div>
  }
  
  @if (errorMessage) {
    <div class="alert alert-danger">
      {{ errorMessage }}
      <button type="button" class="close" (click)="errorMessage = null">&times;</button>
    </div>
  }

  <div class="header">
    <h2>Gestion des Cas de Barème</h2>
    <button class="create-btn" (click)="openModal()" [disabled]="isLoading">
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
        <path d="M12 5V19M5 12H19" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
      Créer un cas de barème
    </button>
  </div>

  <div class="table-container">
    <div class="table-responsive">
      <table>
        <thead>
          <tr>
            <th>Description de barème</th>
            <th>Resp X</th>
            <th>Resp Y</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          @for (cas of casBaremes; track cas.id) {
            <tr>
              <td>{{ cas.libelle }}</td>
              <td>{{ cas.resX }}</td>
              <td>{{ cas.resY }}</td>
              <td class="actions">
                <button class="icon-btn edit" (click)="openModal(cas)" title="Modifier" [disabled]="isLoading">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <path d="M11 4H4C3.46957 4 2.96086 4.21071 2.58579 4.58579C2.21071 4.96086 2 5.46957 2 6V20C2 20.5304 2.21071 21.0391 2.58579 21.4142C2.96086 21.7893 3.46957 22 4 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V13" stroke="#1C3F93" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M18.5 2.50001C18.8978 2.10219 19.4374 1.87869 20 1.87869C20.5626 1.87869 21.1022 2.10219 21.5 2.50001C21.8978 2.89784 22.1213 3.4374 22.1213 4.00001C22.1213 4.56262 21.8978 5.10219 21.5 5.50001L12 15L8 16L9 12L18.5 2.50001Z" stroke="#1C3F93" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                </button>
                <button class="icon-btn delete" (click)="deleteCasBareme(cas.id)" title="Supprimer" [disabled]="isLoading">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <path d="M3 6H5H21" stroke="#FF4B55" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M8 6V4C8 3.46957 8.21071 2.96086 8.58579 2.58579C8.96086 2.21071 9.46957 2 10 2H14C14.5304 2 15.0391 2.21071 15.4142 2.58579C15.7893 2.96086 16 3.46957 16 4V6M19 6V20C19 20.5304 18.7893 21.0391 18.4142 21.4142C18.0391 21.7893 17.5304 22 17 22H7C6.46957 22 5.96086 21.7893 5.58579 21.4142C5.21071 21.0391 5 20.5304 5 20V6H19Z" stroke="#FF4B55" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                </button>
              </td>
            </tr>
          } @empty {
            <tr>
              <td colspan="4" class="no-data">Aucun cas de barème trouvé</td>
            </tr>
          }
          <!-- Lignes de test pour vérifier le défilement -->
        
        </tbody>
      </table>
    </div>
  </div>
</div>

<!-- Modal -->
@if (showModal) {
  <div class="modal">
    <div class="modal-backdrop" (click)="closeModal()"></div>
    <div class="modal-content">
      <div class="modal-header">
        <h3>{{ editingCasBareme ? 'Modifier' : 'Créer' }} un cas de barème</h3>
        <button class="close-btn" (click)="closeModal()" [disabled]="isLoading">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <path d="M18 6L6 18" stroke="#6B7280" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M6 6L18 18" stroke="#6B7280" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </button>
      </div>

      @if (errorMessage) {
        <div class="alert alert-danger">
          {{ errorMessage }}
        </div>
      }

      <form [formGroup]="casBaremeForm" (ngSubmit)="onSubmit()">
        <div class="form-group">
          <label>
            Description du barème
            <span class="required">*</span>
          </label>
          <div class="input-wrapper">
            <textarea formControlName="libelle" placeholder="Entrez la description" rows="3"></textarea>
          </div>
          @if (casBaremeForm.get('libelle')?.invalid && casBaremeForm.get('libelle')?.touched) {
            <div class="error-message">
              @if (casBaremeForm.get('libelle')?.errors?.['required']) {
                <span>La description est requise</span>
              }
              @if (casBaremeForm.get('libelle')?.errors?.['maxlength']) {
                <span>La description est trop longue</span>
              }
            </div>
          }
        </div>

        <div class="form-row">
          <div class="form-group">
            <label>
              Resp X
              <span class="required">*</span>
            </label>
            <div class="input-wrapper">
              <input type="number" formControlName="resX" min="0" max="4" placeholder="0-4">
            </div>
            @if (casBaremeForm.get('resX')?.invalid && casBaremeForm.get('resX')?.touched) {
              <div class="error-message">
                @if (casBaremeForm.get('resX')?.errors?.['required']) {
                  <span>Ce champ est requis</span>
                }
                @if (casBaremeForm.get('resX')?.errors?.['min'] || casBaremeForm.get('resX')?.errors?.['max']) {
                  <span>Valeur entre 0 et 4 requise</span>
                }
              </div>
            }
          </div>

          <div class="form-group">
            <label>
              Resp Y
              <span class="required">*</span>
            </label>
            <div class="input-wrapper">
              <input type="number" formControlName="resY" min="0" max="4" placeholder="0-4">
            </div>
            @if (casBaremeForm.get('resY')?.invalid && casBaremeForm.get('resY')?.touched) {
              <div class="error-message">
                @if (casBaremeForm.get('resY')?.errors?.['required']) {
                  <span>Ce champ est requis</span>
                }
                @if (casBaremeForm.get('resY')?.errors?.['min'] || casBaremeForm.get('resY')?.errors?.['max']) {
                  <span>Valeur entre 0 et 4 requise</span>
                }
              </div>
            }
          </div>
        </div>

        <div class="modal-footer">
          <button type="button" class="btn-secondary" (click)="closeModal()" [disabled]="isLoading">
            Annuler
          </button>
          <button type="submit" class="btn-primary" [disabled]="!casBaremeForm.valid || isLoading">
            @if (isLoading) {
              <span class="spinner-btn"></span>
            }
            {{ editingCasBareme ? 'Modifier' : 'Créer' }}
          </button>
        </div>
      </form>
    </div>
  </div>
}