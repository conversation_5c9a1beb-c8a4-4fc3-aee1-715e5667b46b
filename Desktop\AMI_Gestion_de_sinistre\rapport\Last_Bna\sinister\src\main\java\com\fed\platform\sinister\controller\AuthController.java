package com.fed.platform.sinister.controller;

import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.fed.platform.sinister.domain.Utilisateur;
import com.fed.platform.sinister.dto.LoginRequest;
import com.fed.platform.sinister.dto.LoginResponse;
import com.fed.platform.sinister.repository.UtilisateurRepository;

@RestController
@RequestMapping("/api")
public class AuthController {

    @Autowired
    private UtilisateurRepository utilisateurRepository;

    @PostMapping("/authenticate")
    public ResponseEntity<?> authenticate(@RequestBody LoginRequest loginRequest) {
        Optional<Utilisateur> user = utilisateurRepository.findByUsername(
                loginRequest.getUsername());

        if (user.isPresent()) {
            Utilisateur utilisateur = user.get();
            return ResponseEntity.ok(new LoginResponse(utilisateur.getId(), utilisateur.getRole().name()));
        } else {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("Invalid credentials");
        }
    }
}
