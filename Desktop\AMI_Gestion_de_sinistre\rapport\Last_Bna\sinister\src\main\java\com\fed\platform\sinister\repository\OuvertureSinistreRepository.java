package com.fed.platform.sinister.repository;

import com.fed.platform.sinister.domain.OuvertureSinistre;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface OuvertureSinistreRepository extends JpaRepository<OuvertureSinistre, Long> {
    
    /**
     * Find by numero sinistre
     */
    Optional<OuvertureSinistre> findByNumeroSinistre(String numeroSinistre);
    
    /**
     * Find by evenement
     */
    List<OuvertureSinistre> findByEvenement(String evenement);
    
    /**
     * Find by type de degat
     */
    List<OuvertureSinistre> findByTypeDeDegat(String typeDeDegat);
    
    /**
     * Find by tier status
     */
    List<OuvertureSinistre> findByTier(Boolean tier);
    
    /**
     * Find by responsabilite
     */
    List<OuvertureSinistre> findByResponsabilite(Integer responsabilite);
    
    /**
     * Find by evenement and type de degat
     */
    List<OuvertureSinistre> findByEvenementAndTypeDeDegat(String evenement, String typeDeDegat);
    
    /**
     * Find by evenement and tier
     */
    List<OuvertureSinistre> findByEvenementAndTier(String evenement, Boolean tier);
    
    /**
     * Check if numero sinistre exists
     */
    boolean existsByNumeroSinistre(String numeroSinistre);
    
    /**
     * Get the latest numero sinistre for sequence generation
     */
    @Query("SELECT o.numeroSinistre FROM OuvertureSinistre o ORDER BY o.id DESC")
    List<String> findLatestNumeroSinistre();
    
    /**
     * Find all ordered by creation date desc
     */
    @Query("SELECT o FROM OuvertureSinistre o ORDER BY o.dateCreation DESC")
    List<OuvertureSinistre> findAllOrderByDateCreationDesc();
    
    /**
     * Search by multiple criteria
     */
    @Query("SELECT o FROM OuvertureSinistre o WHERE " +
           "(:evenement IS NULL OR o.evenement = :evenement) AND " +
           "(:typeDeDegat IS NULL OR o.typeDeDegat = :typeDeDegat) AND " +
           "(:tier IS NULL OR o.tier = :tier) AND " +
           "(:responsabilite IS NULL OR o.responsabilite = :responsabilite)")
    List<OuvertureSinistre> findByCriteria(
        @Param("evenement") String evenement,
        @Param("typeDeDegat") String typeDeDegat,
        @Param("tier") Boolean tier,
        @Param("responsabilite") Integer responsabilite
    );
}
