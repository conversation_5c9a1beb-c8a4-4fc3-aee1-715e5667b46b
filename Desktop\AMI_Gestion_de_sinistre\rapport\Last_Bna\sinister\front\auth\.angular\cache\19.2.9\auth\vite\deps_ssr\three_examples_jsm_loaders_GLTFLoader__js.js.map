{"version": 3, "sources": ["../../../../../../node_modules/three/examples/jsm/utils/BufferGeometryUtils.js", "../../../../../../node_modules/three/examples/jsm/loaders/GLTFLoader.js"], "sourcesContent": ["import { BufferAttribute, BufferGeometry, Float32BufferAttribute, InstancedBufferAttribute, InterleavedBuffer, InterleavedBufferAttribute, TriangleFanDrawMode, TriangleStripDrawMode, TrianglesDrawMode, Vector3 } from 'three';\n\n/** @module BufferGeometryUtils */\n\n/**\n * Computes vertex tangents using the MikkTSpace algorithm. MikkTSpace generates the same tangents consistently,\n * and is used in most modelling tools and normal map bakers. Use MikkTSpace for materials with normal maps,\n * because inconsistent tangents may lead to subtle visual issues in the normal map, particularly around mirrored\n * UV seams.\n *\n * In comparison to this method, {@link BufferGeometry#computeTangents} (a custom algorithm) generates tangents that\n * probably will not match the tangents in other software. The custom algorithm is sufficient for general use with a\n * custom material, and may be faster than MikkTSpace.\n *\n * Returns the original BufferGeometry. Indexed geometries will be de-indexed. Requires position, normal, and uv attributes.\n *\n * @param {BufferGeometry} geometry - The geometry to compute tangents for.\n * @param {Object} MikkTSpace - Instance of `examples/jsm/libs/mikktspace.module.js`, or `mikktspace` npm package.\n * Await `MikkTSpace.ready` before use.\n * @param {boolean} [negateSign=true] - Whether to negate the sign component (.w) of each tangent.\n * Required for normal map conventions in some formats, including glTF.\n * @return {BufferGeometry} The updated geometry.\n */\nfunction computeMikkTSpaceTangents(geometry, MikkTSpace, negateSign = true) {\n  if (!MikkTSpace || !MikkTSpace.isReady) {\n    throw new Error('BufferGeometryUtils: Initialized MikkTSpace library required.');\n  }\n  if (!geometry.hasAttribute('position') || !geometry.hasAttribute('normal') || !geometry.hasAttribute('uv')) {\n    throw new Error('BufferGeometryUtils: Tangents require \"position\", \"normal\", and \"uv\" attributes.');\n  }\n  function getAttributeArray(attribute) {\n    if (attribute.normalized || attribute.isInterleavedBufferAttribute) {\n      const dstArray = new Float32Array(attribute.count * attribute.itemSize);\n      for (let i = 0, j = 0; i < attribute.count; i++) {\n        dstArray[j++] = attribute.getX(i);\n        dstArray[j++] = attribute.getY(i);\n        if (attribute.itemSize > 2) {\n          dstArray[j++] = attribute.getZ(i);\n        }\n      }\n      return dstArray;\n    }\n    if (attribute.array instanceof Float32Array) {\n      return attribute.array;\n    }\n    return new Float32Array(attribute.array);\n  }\n\n  // MikkTSpace algorithm requires non-indexed input.\n\n  const _geometry = geometry.index ? geometry.toNonIndexed() : geometry;\n\n  // Compute vertex tangents.\n\n  const tangents = MikkTSpace.generateTangents(getAttributeArray(_geometry.attributes.position), getAttributeArray(_geometry.attributes.normal), getAttributeArray(_geometry.attributes.uv));\n\n  // Texture coordinate convention of glTF differs from the apparent\n  // default of the MikkTSpace library; .w component must be flipped.\n\n  if (negateSign) {\n    for (let i = 3; i < tangents.length; i += 4) {\n      tangents[i] *= -1;\n    }\n  }\n\n  //\n\n  _geometry.setAttribute('tangent', new BufferAttribute(tangents, 4));\n  if (geometry !== _geometry) {\n    geometry.copy(_geometry);\n  }\n  return geometry;\n}\n\n/**\n * Merges a set of geometries into a single instance. All geometries must have compatible attributes.\n *\n * @param {Array<BufferGeometry>} geometries - The geometries to merge.\n * @param {boolean} [useGroups=false] - Whether to use groups or not.\n * @return {?BufferGeometry} The merged geometry. Returns `null` if the merge does not succeed.\n */\nfunction mergeGeometries(geometries, useGroups = false) {\n  const isIndexed = geometries[0].index !== null;\n  const attributesUsed = new Set(Object.keys(geometries[0].attributes));\n  const morphAttributesUsed = new Set(Object.keys(geometries[0].morphAttributes));\n  const attributes = {};\n  const morphAttributes = {};\n  const morphTargetsRelative = geometries[0].morphTargetsRelative;\n  const mergedGeometry = new BufferGeometry();\n  let offset = 0;\n  for (let i = 0; i < geometries.length; ++i) {\n    const geometry = geometries[i];\n    let attributesCount = 0;\n\n    // ensure that all geometries are indexed, or none\n\n    if (isIndexed !== (geometry.index !== null)) {\n      console.error('THREE.BufferGeometryUtils: .mergeGeometries() failed with geometry at index ' + i + '. All geometries must have compatible attributes; make sure index attribute exists among all geometries, or in none of them.');\n      return null;\n    }\n\n    // gather attributes, exit early if they're different\n\n    for (const name in geometry.attributes) {\n      if (!attributesUsed.has(name)) {\n        console.error('THREE.BufferGeometryUtils: .mergeGeometries() failed with geometry at index ' + i + '. All geometries must have compatible attributes; make sure \"' + name + '\" attribute exists among all geometries, or in none of them.');\n        return null;\n      }\n      if (attributes[name] === undefined) attributes[name] = [];\n      attributes[name].push(geometry.attributes[name]);\n      attributesCount++;\n    }\n\n    // ensure geometries have the same number of attributes\n\n    if (attributesCount !== attributesUsed.size) {\n      console.error('THREE.BufferGeometryUtils: .mergeGeometries() failed with geometry at index ' + i + '. Make sure all geometries have the same number of attributes.');\n      return null;\n    }\n\n    // gather morph attributes, exit early if they're different\n\n    if (morphTargetsRelative !== geometry.morphTargetsRelative) {\n      console.error('THREE.BufferGeometryUtils: .mergeGeometries() failed with geometry at index ' + i + '. .morphTargetsRelative must be consistent throughout all geometries.');\n      return null;\n    }\n    for (const name in geometry.morphAttributes) {\n      if (!morphAttributesUsed.has(name)) {\n        console.error('THREE.BufferGeometryUtils: .mergeGeometries() failed with geometry at index ' + i + '.  .morphAttributes must be consistent throughout all geometries.');\n        return null;\n      }\n      if (morphAttributes[name] === undefined) morphAttributes[name] = [];\n      morphAttributes[name].push(geometry.morphAttributes[name]);\n    }\n    if (useGroups) {\n      let count;\n      if (isIndexed) {\n        count = geometry.index.count;\n      } else if (geometry.attributes.position !== undefined) {\n        count = geometry.attributes.position.count;\n      } else {\n        console.error('THREE.BufferGeometryUtils: .mergeGeometries() failed with geometry at index ' + i + '. The geometry must have either an index or a position attribute');\n        return null;\n      }\n      mergedGeometry.addGroup(offset, count, i);\n      offset += count;\n    }\n  }\n\n  // merge indices\n\n  if (isIndexed) {\n    let indexOffset = 0;\n    const mergedIndex = [];\n    for (let i = 0; i < geometries.length; ++i) {\n      const index = geometries[i].index;\n      for (let j = 0; j < index.count; ++j) {\n        mergedIndex.push(index.getX(j) + indexOffset);\n      }\n      indexOffset += geometries[i].attributes.position.count;\n    }\n    mergedGeometry.setIndex(mergedIndex);\n  }\n\n  // merge attributes\n\n  for (const name in attributes) {\n    const mergedAttribute = mergeAttributes(attributes[name]);\n    if (!mergedAttribute) {\n      console.error('THREE.BufferGeometryUtils: .mergeGeometries() failed while trying to merge the ' + name + ' attribute.');\n      return null;\n    }\n    mergedGeometry.setAttribute(name, mergedAttribute);\n  }\n\n  // merge morph attributes\n\n  for (const name in morphAttributes) {\n    const numMorphTargets = morphAttributes[name][0].length;\n    if (numMorphTargets === 0) break;\n    mergedGeometry.morphAttributes = mergedGeometry.morphAttributes || {};\n    mergedGeometry.morphAttributes[name] = [];\n    for (let i = 0; i < numMorphTargets; ++i) {\n      const morphAttributesToMerge = [];\n      for (let j = 0; j < morphAttributes[name].length; ++j) {\n        morphAttributesToMerge.push(morphAttributes[name][j][i]);\n      }\n      const mergedMorphAttribute = mergeAttributes(morphAttributesToMerge);\n      if (!mergedMorphAttribute) {\n        console.error('THREE.BufferGeometryUtils: .mergeGeometries() failed while trying to merge the ' + name + ' morphAttribute.');\n        return null;\n      }\n      mergedGeometry.morphAttributes[name].push(mergedMorphAttribute);\n    }\n  }\n  return mergedGeometry;\n}\n\n/**\n * Merges a set of attributes into a single instance. All attributes must have compatible properties and types.\n * Instances of {@link InterleavedBufferAttribute} are not supported.\n *\n * @param {Array<BufferAttribute>} attributes - The attributes to merge.\n * @return {?BufferAttribute} The merged attribute. Returns `null` if the merge does not succeed.\n */\nfunction mergeAttributes(attributes) {\n  let TypedArray;\n  let itemSize;\n  let normalized;\n  let gpuType = -1;\n  let arrayLength = 0;\n  for (let i = 0; i < attributes.length; ++i) {\n    const attribute = attributes[i];\n    if (TypedArray === undefined) TypedArray = attribute.array.constructor;\n    if (TypedArray !== attribute.array.constructor) {\n      console.error('THREE.BufferGeometryUtils: .mergeAttributes() failed. BufferAttribute.array must be of consistent array types across matching attributes.');\n      return null;\n    }\n    if (itemSize === undefined) itemSize = attribute.itemSize;\n    if (itemSize !== attribute.itemSize) {\n      console.error('THREE.BufferGeometryUtils: .mergeAttributes() failed. BufferAttribute.itemSize must be consistent across matching attributes.');\n      return null;\n    }\n    if (normalized === undefined) normalized = attribute.normalized;\n    if (normalized !== attribute.normalized) {\n      console.error('THREE.BufferGeometryUtils: .mergeAttributes() failed. BufferAttribute.normalized must be consistent across matching attributes.');\n      return null;\n    }\n    if (gpuType === -1) gpuType = attribute.gpuType;\n    if (gpuType !== attribute.gpuType) {\n      console.error('THREE.BufferGeometryUtils: .mergeAttributes() failed. BufferAttribute.gpuType must be consistent across matching attributes.');\n      return null;\n    }\n    arrayLength += attribute.count * itemSize;\n  }\n  const array = new TypedArray(arrayLength);\n  const result = new BufferAttribute(array, itemSize, normalized);\n  let offset = 0;\n  for (let i = 0; i < attributes.length; ++i) {\n    const attribute = attributes[i];\n    if (attribute.isInterleavedBufferAttribute) {\n      const tupleOffset = offset / itemSize;\n      for (let j = 0, l = attribute.count; j < l; j++) {\n        for (let c = 0; c < itemSize; c++) {\n          const value = attribute.getComponent(j, c);\n          result.setComponent(j + tupleOffset, c, value);\n        }\n      }\n    } else {\n      array.set(attribute.array, offset);\n    }\n    offset += attribute.count * itemSize;\n  }\n  if (gpuType !== undefined) {\n    result.gpuType = gpuType;\n  }\n  return result;\n}\n\n/**\n * Performs a deep clone of the given buffer attribute.\n *\n * @param {BufferAttribute} attribute - The attribute to clone.\n * @return {BufferAttribute} The cloned attribute.\n */\nfunction deepCloneAttribute(attribute) {\n  if (attribute.isInstancedInterleavedBufferAttribute || attribute.isInterleavedBufferAttribute) {\n    return deinterleaveAttribute(attribute);\n  }\n  if (attribute.isInstancedBufferAttribute) {\n    return new InstancedBufferAttribute().copy(attribute);\n  }\n  return new BufferAttribute().copy(attribute);\n}\n\n/**\n * Interleaves a set of attributes and returns a new array of corresponding attributes that share a\n * single {@link InterleavedBuffer} instance. All attributes must have compatible types.\n *\n * @param {Array<BufferAttribute>} attributes - The attributes to interleave.\n * @return {Array<InterleavedBufferAttribute>} An array of interleaved attributes. If interleave does not succeed, the method returns `null`.\n */\nfunction interleaveAttributes(attributes) {\n  // Interleaves the provided attributes into an InterleavedBuffer and returns\n  // a set of InterleavedBufferAttributes for each attribute\n  let TypedArray;\n  let arrayLength = 0;\n  let stride = 0;\n\n  // calculate the length and type of the interleavedBuffer\n  for (let i = 0, l = attributes.length; i < l; ++i) {\n    const attribute = attributes[i];\n    if (TypedArray === undefined) TypedArray = attribute.array.constructor;\n    if (TypedArray !== attribute.array.constructor) {\n      console.error('AttributeBuffers of different types cannot be interleaved');\n      return null;\n    }\n    arrayLength += attribute.array.length;\n    stride += attribute.itemSize;\n  }\n\n  // Create the set of buffer attributes\n  const interleavedBuffer = new InterleavedBuffer(new TypedArray(arrayLength), stride);\n  let offset = 0;\n  const res = [];\n  const getters = ['getX', 'getY', 'getZ', 'getW'];\n  const setters = ['setX', 'setY', 'setZ', 'setW'];\n  for (let j = 0, l = attributes.length; j < l; j++) {\n    const attribute = attributes[j];\n    const itemSize = attribute.itemSize;\n    const count = attribute.count;\n    const iba = new InterleavedBufferAttribute(interleavedBuffer, itemSize, offset, attribute.normalized);\n    res.push(iba);\n    offset += itemSize;\n\n    // Move the data for each attribute into the new interleavedBuffer\n    // at the appropriate offset\n    for (let c = 0; c < count; c++) {\n      for (let k = 0; k < itemSize; k++) {\n        iba[setters[k]](c, attribute[getters[k]](c));\n      }\n    }\n  }\n  return res;\n}\n\n/**\n * Returns a new, non-interleaved version of the given attribute.\n *\n * @param {InterleavedBufferAttribute} attribute - The interleaved attribute.\n * @return {BufferAttribute} The non-interleaved attribute.\n */\nfunction deinterleaveAttribute(attribute) {\n  const cons = attribute.data.array.constructor;\n  const count = attribute.count;\n  const itemSize = attribute.itemSize;\n  const normalized = attribute.normalized;\n  const array = new cons(count * itemSize);\n  let newAttribute;\n  if (attribute.isInstancedInterleavedBufferAttribute) {\n    newAttribute = new InstancedBufferAttribute(array, itemSize, normalized, attribute.meshPerAttribute);\n  } else {\n    newAttribute = new BufferAttribute(array, itemSize, normalized);\n  }\n  for (let i = 0; i < count; i++) {\n    newAttribute.setX(i, attribute.getX(i));\n    if (itemSize >= 2) {\n      newAttribute.setY(i, attribute.getY(i));\n    }\n    if (itemSize >= 3) {\n      newAttribute.setZ(i, attribute.getZ(i));\n    }\n    if (itemSize >= 4) {\n      newAttribute.setW(i, attribute.getW(i));\n    }\n  }\n  return newAttribute;\n}\n\n/**\n * Deinterleaves all attributes on the given geometry.\n *\n * @param {BufferGeometry} geometry - The geometry to deinterleave.\n */\nfunction deinterleaveGeometry(geometry) {\n  const attributes = geometry.attributes;\n  const morphTargets = geometry.morphTargets;\n  const attrMap = new Map();\n  for (const key in attributes) {\n    const attr = attributes[key];\n    if (attr.isInterleavedBufferAttribute) {\n      if (!attrMap.has(attr)) {\n        attrMap.set(attr, deinterleaveAttribute(attr));\n      }\n      attributes[key] = attrMap.get(attr);\n    }\n  }\n  for (const key in morphTargets) {\n    const attr = morphTargets[key];\n    if (attr.isInterleavedBufferAttribute) {\n      if (!attrMap.has(attr)) {\n        attrMap.set(attr, deinterleaveAttribute(attr));\n      }\n      morphTargets[key] = attrMap.get(attr);\n    }\n  }\n}\n\n/**\n * Returns the amount of bytes used by all attributes to represent the geometry.\n *\n * @param {BufferGeometry} geometry - The geometry.\n * @return {number} The estimate bytes used.\n */\nfunction estimateBytesUsed(geometry) {\n  // Return the estimated memory used by this geometry in bytes\n  // Calculate using itemSize, count, and BYTES_PER_ELEMENT to account\n  // for InterleavedBufferAttributes.\n  let mem = 0;\n  for (const name in geometry.attributes) {\n    const attr = geometry.getAttribute(name);\n    mem += attr.count * attr.itemSize * attr.array.BYTES_PER_ELEMENT;\n  }\n  const indices = geometry.getIndex();\n  mem += indices ? indices.count * indices.itemSize * indices.array.BYTES_PER_ELEMENT : 0;\n  return mem;\n}\n\n/**\n * Returns a new geometry with vertices for which all similar vertex attributes (within tolerance) are merged.\n *\n * @param {BufferGeometry} geometry - The geometry to merge vertices for.\n * @param {number} [tolerance=1e-4] - The tolerance value.\n * @return {BufferGeometry} - The new geometry with merged vertices.\n */\nfunction mergeVertices(geometry, tolerance = 1e-4) {\n  tolerance = Math.max(tolerance, Number.EPSILON);\n\n  // Generate an index buffer if the geometry doesn't have one, or optimize it\n  // if it's already available.\n  const hashToIndex = {};\n  const indices = geometry.getIndex();\n  const positions = geometry.getAttribute('position');\n  const vertexCount = indices ? indices.count : positions.count;\n\n  // next value for triangle indices\n  let nextIndex = 0;\n\n  // attributes and new attribute arrays\n  const attributeNames = Object.keys(geometry.attributes);\n  const tmpAttributes = {};\n  const tmpMorphAttributes = {};\n  const newIndices = [];\n  const getters = ['getX', 'getY', 'getZ', 'getW'];\n  const setters = ['setX', 'setY', 'setZ', 'setW'];\n\n  // Initialize the arrays, allocating space conservatively. Extra\n  // space will be trimmed in the last step.\n  for (let i = 0, l = attributeNames.length; i < l; i++) {\n    const name = attributeNames[i];\n    const attr = geometry.attributes[name];\n    tmpAttributes[name] = new attr.constructor(new attr.array.constructor(attr.count * attr.itemSize), attr.itemSize, attr.normalized);\n    const morphAttributes = geometry.morphAttributes[name];\n    if (morphAttributes) {\n      if (!tmpMorphAttributes[name]) tmpMorphAttributes[name] = [];\n      morphAttributes.forEach((morphAttr, i) => {\n        const array = new morphAttr.array.constructor(morphAttr.count * morphAttr.itemSize);\n        tmpMorphAttributes[name][i] = new morphAttr.constructor(array, morphAttr.itemSize, morphAttr.normalized);\n      });\n    }\n  }\n\n  // convert the error tolerance to an amount of decimal places to truncate to\n  const halfTolerance = tolerance * 0.5;\n  const exponent = Math.log10(1 / tolerance);\n  const hashMultiplier = Math.pow(10, exponent);\n  const hashAdditive = halfTolerance * hashMultiplier;\n  for (let i = 0; i < vertexCount; i++) {\n    const index = indices ? indices.getX(i) : i;\n\n    // Generate a hash for the vertex attributes at the current index 'i'\n    let hash = '';\n    for (let j = 0, l = attributeNames.length; j < l; j++) {\n      const name = attributeNames[j];\n      const attribute = geometry.getAttribute(name);\n      const itemSize = attribute.itemSize;\n      for (let k = 0; k < itemSize; k++) {\n        // double tilde truncates the decimal value\n        hash += `${~~(attribute[getters[k]](index) * hashMultiplier + hashAdditive)},`;\n      }\n    }\n\n    // Add another reference to the vertex if it's already\n    // used by another index\n    if (hash in hashToIndex) {\n      newIndices.push(hashToIndex[hash]);\n    } else {\n      // copy data to the new index in the temporary attributes\n      for (let j = 0, l = attributeNames.length; j < l; j++) {\n        const name = attributeNames[j];\n        const attribute = geometry.getAttribute(name);\n        const morphAttributes = geometry.morphAttributes[name];\n        const itemSize = attribute.itemSize;\n        const newArray = tmpAttributes[name];\n        const newMorphArrays = tmpMorphAttributes[name];\n        for (let k = 0; k < itemSize; k++) {\n          const getterFunc = getters[k];\n          const setterFunc = setters[k];\n          newArray[setterFunc](nextIndex, attribute[getterFunc](index));\n          if (morphAttributes) {\n            for (let m = 0, ml = morphAttributes.length; m < ml; m++) {\n              newMorphArrays[m][setterFunc](nextIndex, morphAttributes[m][getterFunc](index));\n            }\n          }\n        }\n      }\n      hashToIndex[hash] = nextIndex;\n      newIndices.push(nextIndex);\n      nextIndex++;\n    }\n  }\n\n  // generate result BufferGeometry\n  const result = geometry.clone();\n  for (const name in geometry.attributes) {\n    const tmpAttribute = tmpAttributes[name];\n    result.setAttribute(name, new tmpAttribute.constructor(tmpAttribute.array.slice(0, nextIndex * tmpAttribute.itemSize), tmpAttribute.itemSize, tmpAttribute.normalized));\n    if (!(name in tmpMorphAttributes)) continue;\n    for (let j = 0; j < tmpMorphAttributes[name].length; j++) {\n      const tmpMorphAttribute = tmpMorphAttributes[name][j];\n      result.morphAttributes[name][j] = new tmpMorphAttribute.constructor(tmpMorphAttribute.array.slice(0, nextIndex * tmpMorphAttribute.itemSize), tmpMorphAttribute.itemSize, tmpMorphAttribute.normalized);\n    }\n  }\n\n  // indices\n\n  result.setIndex(newIndices);\n  return result;\n}\n\n/**\n * Returns a new indexed geometry based on `TrianglesDrawMode` draw mode.\n * This mode corresponds to the `gl.TRIANGLES` primitive in WebGL.\n *\n * @param {BufferGeometry} geometry - The geometry to convert.\n * @param {number} drawMode - The current draw mode.\n * @return {BufferGeometry} The new geometry using `TrianglesDrawMode`.\n */\nfunction toTrianglesDrawMode(geometry, drawMode) {\n  if (drawMode === TrianglesDrawMode) {\n    console.warn('THREE.BufferGeometryUtils.toTrianglesDrawMode(): Geometry already defined as triangles.');\n    return geometry;\n  }\n  if (drawMode === TriangleFanDrawMode || drawMode === TriangleStripDrawMode) {\n    let index = geometry.getIndex();\n\n    // generate index if not present\n\n    if (index === null) {\n      const indices = [];\n      const position = geometry.getAttribute('position');\n      if (position !== undefined) {\n        for (let i = 0; i < position.count; i++) {\n          indices.push(i);\n        }\n        geometry.setIndex(indices);\n        index = geometry.getIndex();\n      } else {\n        console.error('THREE.BufferGeometryUtils.toTrianglesDrawMode(): Undefined position attribute. Processing not possible.');\n        return geometry;\n      }\n    }\n\n    //\n\n    const numberOfTriangles = index.count - 2;\n    const newIndices = [];\n    if (drawMode === TriangleFanDrawMode) {\n      // gl.TRIANGLE_FAN\n\n      for (let i = 1; i <= numberOfTriangles; i++) {\n        newIndices.push(index.getX(0));\n        newIndices.push(index.getX(i));\n        newIndices.push(index.getX(i + 1));\n      }\n    } else {\n      // gl.TRIANGLE_STRIP\n\n      for (let i = 0; i < numberOfTriangles; i++) {\n        if (i % 2 === 0) {\n          newIndices.push(index.getX(i));\n          newIndices.push(index.getX(i + 1));\n          newIndices.push(index.getX(i + 2));\n        } else {\n          newIndices.push(index.getX(i + 2));\n          newIndices.push(index.getX(i + 1));\n          newIndices.push(index.getX(i));\n        }\n      }\n    }\n    if (newIndices.length / 3 !== numberOfTriangles) {\n      console.error('THREE.BufferGeometryUtils.toTrianglesDrawMode(): Unable to generate correct amount of triangles.');\n    }\n\n    // build final geometry\n\n    const newGeometry = geometry.clone();\n    newGeometry.setIndex(newIndices);\n    newGeometry.clearGroups();\n    return newGeometry;\n  } else {\n    console.error('THREE.BufferGeometryUtils.toTrianglesDrawMode(): Unknown draw mode:', drawMode);\n    return geometry;\n  }\n}\n\n/**\n * Calculates the morphed attributes of a morphed/skinned BufferGeometry.\n *\n * Helpful for Raytracing or Decals (i.e. a `DecalGeometry` applied to a morphed Object with a `BufferGeometry`\n * will use the original `BufferGeometry`, not the morphed/skinned one, generating an incorrect result.\n * Using this function to create a shadow `Object3`D the `DecalGeometry` can be correctly generated).\n *\n * @param {Mesh|Line|Points} object - The 3D object to compute morph attributes for.\n * @return {Object} An object with original position/normal attributes and morphed ones.\n */\nfunction computeMorphedAttributes(object) {\n  const _vA = new Vector3();\n  const _vB = new Vector3();\n  const _vC = new Vector3();\n  const _tempA = new Vector3();\n  const _tempB = new Vector3();\n  const _tempC = new Vector3();\n  const _morphA = new Vector3();\n  const _morphB = new Vector3();\n  const _morphC = new Vector3();\n  function _calculateMorphedAttributeData(object, attribute, morphAttribute, morphTargetsRelative, a, b, c, modifiedAttributeArray) {\n    _vA.fromBufferAttribute(attribute, a);\n    _vB.fromBufferAttribute(attribute, b);\n    _vC.fromBufferAttribute(attribute, c);\n    const morphInfluences = object.morphTargetInfluences;\n    if (morphAttribute && morphInfluences) {\n      _morphA.set(0, 0, 0);\n      _morphB.set(0, 0, 0);\n      _morphC.set(0, 0, 0);\n      for (let i = 0, il = morphAttribute.length; i < il; i++) {\n        const influence = morphInfluences[i];\n        const morph = morphAttribute[i];\n        if (influence === 0) continue;\n        _tempA.fromBufferAttribute(morph, a);\n        _tempB.fromBufferAttribute(morph, b);\n        _tempC.fromBufferAttribute(morph, c);\n        if (morphTargetsRelative) {\n          _morphA.addScaledVector(_tempA, influence);\n          _morphB.addScaledVector(_tempB, influence);\n          _morphC.addScaledVector(_tempC, influence);\n        } else {\n          _morphA.addScaledVector(_tempA.sub(_vA), influence);\n          _morphB.addScaledVector(_tempB.sub(_vB), influence);\n          _morphC.addScaledVector(_tempC.sub(_vC), influence);\n        }\n      }\n      _vA.add(_morphA);\n      _vB.add(_morphB);\n      _vC.add(_morphC);\n    }\n    if (object.isSkinnedMesh) {\n      object.applyBoneTransform(a, _vA);\n      object.applyBoneTransform(b, _vB);\n      object.applyBoneTransform(c, _vC);\n    }\n    modifiedAttributeArray[a * 3 + 0] = _vA.x;\n    modifiedAttributeArray[a * 3 + 1] = _vA.y;\n    modifiedAttributeArray[a * 3 + 2] = _vA.z;\n    modifiedAttributeArray[b * 3 + 0] = _vB.x;\n    modifiedAttributeArray[b * 3 + 1] = _vB.y;\n    modifiedAttributeArray[b * 3 + 2] = _vB.z;\n    modifiedAttributeArray[c * 3 + 0] = _vC.x;\n    modifiedAttributeArray[c * 3 + 1] = _vC.y;\n    modifiedAttributeArray[c * 3 + 2] = _vC.z;\n  }\n  const geometry = object.geometry;\n  const material = object.material;\n  let a, b, c;\n  const index = geometry.index;\n  const positionAttribute = geometry.attributes.position;\n  const morphPosition = geometry.morphAttributes.position;\n  const morphTargetsRelative = geometry.morphTargetsRelative;\n  const normalAttribute = geometry.attributes.normal;\n  const morphNormal = geometry.morphAttributes.position;\n  const groups = geometry.groups;\n  const drawRange = geometry.drawRange;\n  let i, j, il, jl;\n  let group;\n  let start, end;\n  const modifiedPosition = new Float32Array(positionAttribute.count * positionAttribute.itemSize);\n  const modifiedNormal = new Float32Array(normalAttribute.count * normalAttribute.itemSize);\n  if (index !== null) {\n    // indexed buffer geometry\n\n    if (Array.isArray(material)) {\n      for (i = 0, il = groups.length; i < il; i++) {\n        group = groups[i];\n        start = Math.max(group.start, drawRange.start);\n        end = Math.min(group.start + group.count, drawRange.start + drawRange.count);\n        for (j = start, jl = end; j < jl; j += 3) {\n          a = index.getX(j);\n          b = index.getX(j + 1);\n          c = index.getX(j + 2);\n          _calculateMorphedAttributeData(object, positionAttribute, morphPosition, morphTargetsRelative, a, b, c, modifiedPosition);\n          _calculateMorphedAttributeData(object, normalAttribute, morphNormal, morphTargetsRelative, a, b, c, modifiedNormal);\n        }\n      }\n    } else {\n      start = Math.max(0, drawRange.start);\n      end = Math.min(index.count, drawRange.start + drawRange.count);\n      for (i = start, il = end; i < il; i += 3) {\n        a = index.getX(i);\n        b = index.getX(i + 1);\n        c = index.getX(i + 2);\n        _calculateMorphedAttributeData(object, positionAttribute, morphPosition, morphTargetsRelative, a, b, c, modifiedPosition);\n        _calculateMorphedAttributeData(object, normalAttribute, morphNormal, morphTargetsRelative, a, b, c, modifiedNormal);\n      }\n    }\n  } else {\n    // non-indexed buffer geometry\n\n    if (Array.isArray(material)) {\n      for (i = 0, il = groups.length; i < il; i++) {\n        group = groups[i];\n        start = Math.max(group.start, drawRange.start);\n        end = Math.min(group.start + group.count, drawRange.start + drawRange.count);\n        for (j = start, jl = end; j < jl; j += 3) {\n          a = j;\n          b = j + 1;\n          c = j + 2;\n          _calculateMorphedAttributeData(object, positionAttribute, morphPosition, morphTargetsRelative, a, b, c, modifiedPosition);\n          _calculateMorphedAttributeData(object, normalAttribute, morphNormal, morphTargetsRelative, a, b, c, modifiedNormal);\n        }\n      }\n    } else {\n      start = Math.max(0, drawRange.start);\n      end = Math.min(positionAttribute.count, drawRange.start + drawRange.count);\n      for (i = start, il = end; i < il; i += 3) {\n        a = i;\n        b = i + 1;\n        c = i + 2;\n        _calculateMorphedAttributeData(object, positionAttribute, morphPosition, morphTargetsRelative, a, b, c, modifiedPosition);\n        _calculateMorphedAttributeData(object, normalAttribute, morphNormal, morphTargetsRelative, a, b, c, modifiedNormal);\n      }\n    }\n  }\n  const morphedPositionAttribute = new Float32BufferAttribute(modifiedPosition, 3);\n  const morphedNormalAttribute = new Float32BufferAttribute(modifiedNormal, 3);\n  return {\n    positionAttribute: positionAttribute,\n    normalAttribute: normalAttribute,\n    morphedPositionAttribute: morphedPositionAttribute,\n    morphedNormalAttribute: morphedNormalAttribute\n  };\n}\n\n/**\n * Merges the {@link BufferGeometry#groups} for the given geometry.\n *\n * @param {BufferGeometry} geometry - The geometry to modify.\n * @return {BufferGeometry} - The updated geometry\n */\nfunction mergeGroups(geometry) {\n  if (geometry.groups.length === 0) {\n    console.warn('THREE.BufferGeometryUtils.mergeGroups(): No groups are defined. Nothing to merge.');\n    return geometry;\n  }\n  let groups = geometry.groups;\n\n  // sort groups by material index\n\n  groups = groups.sort((a, b) => {\n    if (a.materialIndex !== b.materialIndex) return a.materialIndex - b.materialIndex;\n    return a.start - b.start;\n  });\n\n  // create index for non-indexed geometries\n\n  if (geometry.getIndex() === null) {\n    const positionAttribute = geometry.getAttribute('position');\n    const indices = [];\n    for (let i = 0; i < positionAttribute.count; i += 3) {\n      indices.push(i, i + 1, i + 2);\n    }\n    geometry.setIndex(indices);\n  }\n\n  // sort index\n\n  const index = geometry.getIndex();\n  const newIndices = [];\n  for (let i = 0; i < groups.length; i++) {\n    const group = groups[i];\n    const groupStart = group.start;\n    const groupLength = groupStart + group.count;\n    for (let j = groupStart; j < groupLength; j++) {\n      newIndices.push(index.getX(j));\n    }\n  }\n  geometry.dispose(); // Required to force buffer recreation\n  geometry.setIndex(newIndices);\n\n  // update groups indices\n\n  let start = 0;\n  for (let i = 0; i < groups.length; i++) {\n    const group = groups[i];\n    group.start = start;\n    start += group.count;\n  }\n\n  // merge groups\n\n  let currentGroup = groups[0];\n  geometry.groups = [currentGroup];\n  for (let i = 1; i < groups.length; i++) {\n    const group = groups[i];\n    if (currentGroup.materialIndex === group.materialIndex) {\n      currentGroup.count += group.count;\n    } else {\n      currentGroup = group;\n      geometry.groups.push(currentGroup);\n    }\n  }\n  return geometry;\n}\n\n/**\n * Modifies the supplied geometry if it is non-indexed, otherwise creates a new,\n * non-indexed geometry. Returns the geometry with smooth normals everywhere except\n * faces that meet at an angle greater than the crease angle.\n *\n * @param {BufferGeometry} geometry - The geometry to modify.\n * @param {number} [creaseAngle=Math.PI/3] - The crease angle in radians.\n * @return {BufferGeometry} - The updated geometry\n */\nfunction toCreasedNormals(geometry, creaseAngle = Math.PI / 3 /* 60 degrees */) {\n  const creaseDot = Math.cos(creaseAngle);\n  const hashMultiplier = (1 + 1e-10) * 1e2;\n\n  // reusable vectors\n  const verts = [new Vector3(), new Vector3(), new Vector3()];\n  const tempVec1 = new Vector3();\n  const tempVec2 = new Vector3();\n  const tempNorm = new Vector3();\n  const tempNorm2 = new Vector3();\n\n  // hashes a vector\n  function hashVertex(v) {\n    const x = ~~(v.x * hashMultiplier);\n    const y = ~~(v.y * hashMultiplier);\n    const z = ~~(v.z * hashMultiplier);\n    return `${x},${y},${z}`;\n  }\n\n  // BufferGeometry.toNonIndexed() warns if the geometry is non-indexed\n  // and returns the original geometry\n  const resultGeometry = geometry.index ? geometry.toNonIndexed() : geometry;\n  const posAttr = resultGeometry.attributes.position;\n  const vertexMap = {};\n\n  // find all the normals shared by commonly located vertices\n  for (let i = 0, l = posAttr.count / 3; i < l; i++) {\n    const i3 = 3 * i;\n    const a = verts[0].fromBufferAttribute(posAttr, i3 + 0);\n    const b = verts[1].fromBufferAttribute(posAttr, i3 + 1);\n    const c = verts[2].fromBufferAttribute(posAttr, i3 + 2);\n    tempVec1.subVectors(c, b);\n    tempVec2.subVectors(a, b);\n\n    // add the normal to the map for all vertices\n    const normal = new Vector3().crossVectors(tempVec1, tempVec2).normalize();\n    for (let n = 0; n < 3; n++) {\n      const vert = verts[n];\n      const hash = hashVertex(vert);\n      if (!(hash in vertexMap)) {\n        vertexMap[hash] = [];\n      }\n      vertexMap[hash].push(normal);\n    }\n  }\n\n  // average normals from all vertices that share a common location if they are within the\n  // provided crease threshold\n  const normalArray = new Float32Array(posAttr.count * 3);\n  const normAttr = new BufferAttribute(normalArray, 3, false);\n  for (let i = 0, l = posAttr.count / 3; i < l; i++) {\n    // get the face normal for this vertex\n    const i3 = 3 * i;\n    const a = verts[0].fromBufferAttribute(posAttr, i3 + 0);\n    const b = verts[1].fromBufferAttribute(posAttr, i3 + 1);\n    const c = verts[2].fromBufferAttribute(posAttr, i3 + 2);\n    tempVec1.subVectors(c, b);\n    tempVec2.subVectors(a, b);\n    tempNorm.crossVectors(tempVec1, tempVec2).normalize();\n\n    // average all normals that meet the threshold and set the normal value\n    for (let n = 0; n < 3; n++) {\n      const vert = verts[n];\n      const hash = hashVertex(vert);\n      const otherNormals = vertexMap[hash];\n      tempNorm2.set(0, 0, 0);\n      for (let k = 0, lk = otherNormals.length; k < lk; k++) {\n        const otherNorm = otherNormals[k];\n        if (tempNorm.dot(otherNorm) > creaseDot) {\n          tempNorm2.add(otherNorm);\n        }\n      }\n      tempNorm2.normalize();\n      normAttr.setXYZ(i3 + n, tempNorm2.x, tempNorm2.y, tempNorm2.z);\n    }\n  }\n  resultGeometry.setAttribute('normal', normAttr);\n  return resultGeometry;\n}\nexport { computeMikkTSpaceTangents, mergeGeometries, mergeAttributes, deepCloneAttribute, deinterleaveAttribute, deinterleaveGeometry, interleaveAttributes, estimateBytesUsed, mergeVertices, toTrianglesDrawMode, computeMorphedAttributes, mergeGroups, toCreasedNormals };", "import { AnimationClip, Bone, Box3, BufferAttribute, BufferGeometry, ClampToEdgeWrapping, Color, ColorManagement, DirectionalLight, DoubleSide, FileLoader, FrontSide, Group, ImageBitmapLoader, InstancedMesh, InterleavedBuffer, InterleavedBufferAttribute, Interpolant, InterpolateDiscrete, InterpolateLinear, Line, LineBasicMaterial, LineLoop, LineSegments, LinearFilter, LinearMipmapLinearFilter, LinearMipmapNearestFilter, LinearSRGBColorSpace, Loader, LoaderUtils, Material, MathUtils, Matrix4, Mesh, MeshBasicMaterial, MeshPhysicalMaterial, MeshStandardMaterial, MirroredRepeatWrapping, NearestFilter, NearestMipmapLinearFilter, NearestMipmapNearestFilter, NumberKeyframeTrack, Object3D, OrthographicCamera, PerspectiveCamera, PointLight, Points, PointsMaterial, PropertyBinding, Quaternion, QuaternionKeyframeTrack, RepeatWrapping, Skeleton, SkinnedMesh, Sphere, SpotLight, Texture, TextureLoader, TriangleFanDrawMode, TriangleStripDrawMode, Vector2, Vector3, VectorKeyframeTrack, SRGBColorSpace, InstancedBufferAttribute } from 'three';\nimport { toTrianglesDrawMode } from '../utils/BufferGeometryUtils.js';\n\n/**\n * A loader for the glTF 2.0 format.\n *\n * [glTF]{@link https://www.khronos.org/gltf/} (GL Transmission Format) is an [open format specification]{@link https://github.com/KhronosGroup/glTF/tree/main/specification/2.0}\n * for efficient delivery and loading of 3D content. Assets may be provided either in JSON (.gltf) or binary (.glb)\n * format. External files store textures (.jpg, .png) and additional binary data (.bin). A glTF asset may deliver\n * one or more scenes, including meshes, materials, textures, skins, skeletons, morph targets, animations, lights,\n * and/or cameras.\n *\n * `GLTFLoader` uses {@link ImageBitmapLoader} whenever possible. Be advised that image bitmaps are not\n * automatically GC-collected when they are no longer referenced, and they require special handling during\n * the disposal process.\n *\n * `GLTFLoader` supports the following glTF 2.0 extensions:\n * - KHR_draco_mesh_compression\n * - KHR_materials_clearcoat\n * - KHR_materials_dispersion\n * - KHR_materials_ior\n * - KHR_materials_specular\n * - KHR_materials_transmission\n * - KHR_materials_iridescence\n * - KHR_materials_unlit\n * - KHR_materials_volume\n * - KHR_mesh_quantization\n * - KHR_lights_punctual\n * - KHR_texture_basisu\n * - KHR_texture_transform\n * - EXT_texture_webp\n * - EXT_meshopt_compression\n * - EXT_mesh_gpu_instancing\n *\n * The following glTF 2.0 extension is supported by an external user plugin:\n * - [KHR_materials_variants]{@link https://github.com/takahirox/three-gltf-extensions}\n * - [MSFT_texture_dds]{@link https://github.com/takahirox/three-gltf-extensions}\n *\n * ```js\n * const loader = new GLTFLoader();\n *\n * // Optional: Provide a DRACOLoader instance to decode compressed mesh data\n * const dracoLoader = new DRACOLoader();\n * dracoLoader.setDecoderPath( '/examples/jsm/libs/draco/' );\n * loader.setDRACOLoader( dracoLoader );\n *\n * const gltf = await loader.loadAsync( 'models/gltf/duck/duck.gltf' );\n * scene.add( gltf.scene );\n * ```\n *\n * @augments Loader\n */\nclass GLTFLoader extends Loader {\n  /**\n   * Constructs a new glTF loader.\n   *\n   * @param {LoadingManager} [manager] - The loading manager.\n   */\n  constructor(manager) {\n    super(manager);\n    this.dracoLoader = null;\n    this.ktx2Loader = null;\n    this.meshoptDecoder = null;\n    this.pluginCallbacks = [];\n    this.register(function (parser) {\n      return new GLTFMaterialsClearcoatExtension(parser);\n    });\n    this.register(function (parser) {\n      return new GLTFMaterialsDispersionExtension(parser);\n    });\n    this.register(function (parser) {\n      return new GLTFTextureBasisUExtension(parser);\n    });\n    this.register(function (parser) {\n      return new GLTFTextureWebPExtension(parser);\n    });\n    this.register(function (parser) {\n      return new GLTFTextureAVIFExtension(parser);\n    });\n    this.register(function (parser) {\n      return new GLTFMaterialsSheenExtension(parser);\n    });\n    this.register(function (parser) {\n      return new GLTFMaterialsTransmissionExtension(parser);\n    });\n    this.register(function (parser) {\n      return new GLTFMaterialsVolumeExtension(parser);\n    });\n    this.register(function (parser) {\n      return new GLTFMaterialsIorExtension(parser);\n    });\n    this.register(function (parser) {\n      return new GLTFMaterialsEmissiveStrengthExtension(parser);\n    });\n    this.register(function (parser) {\n      return new GLTFMaterialsSpecularExtension(parser);\n    });\n    this.register(function (parser) {\n      return new GLTFMaterialsIridescenceExtension(parser);\n    });\n    this.register(function (parser) {\n      return new GLTFMaterialsAnisotropyExtension(parser);\n    });\n    this.register(function (parser) {\n      return new GLTFMaterialsBumpExtension(parser);\n    });\n    this.register(function (parser) {\n      return new GLTFLightsExtension(parser);\n    });\n    this.register(function (parser) {\n      return new GLTFMeshoptCompression(parser);\n    });\n    this.register(function (parser) {\n      return new GLTFMeshGpuInstancing(parser);\n    });\n  }\n\n  /**\n   * Starts loading from the given URL and passes the loaded glTF asset\n   * to the `onLoad()` callback.\n   *\n   * @param {string} url - The path/URL of the file to be loaded. This can also be a data URI.\n   * @param {function(GLTFLoader~LoadObject)} onLoad - Executed when the loading process has been finished.\n   * @param {onProgressCallback} onProgress - Executed while the loading is in progress.\n   * @param {onErrorCallback} onError - Executed when errors occur.\n   */\n  load(url, onLoad, onProgress, onError) {\n    const scope = this;\n    let resourcePath;\n    if (this.resourcePath !== '') {\n      resourcePath = this.resourcePath;\n    } else if (this.path !== '') {\n      // If a base path is set, resources will be relative paths from that plus the relative path of the gltf file\n      // Example  path = 'https://my-cnd-server.com/', url = 'assets/models/model.gltf'\n      // resourcePath = 'https://my-cnd-server.com/assets/models/'\n      // referenced resource 'model.bin' will be loaded from 'https://my-cnd-server.com/assets/models/model.bin'\n      // referenced resource '../textures/texture.png' will be loaded from 'https://my-cnd-server.com/assets/textures/texture.png'\n      const relativeUrl = LoaderUtils.extractUrlBase(url);\n      resourcePath = LoaderUtils.resolveURL(relativeUrl, this.path);\n    } else {\n      resourcePath = LoaderUtils.extractUrlBase(url);\n    }\n\n    // Tells the LoadingManager to track an extra item, which resolves after\n    // the model is fully loaded. This means the count of items loaded will\n    // be incorrect, but ensures manager.onLoad() does not fire early.\n    this.manager.itemStart(url);\n    const _onError = function (e) {\n      if (onError) {\n        onError(e);\n      } else {\n        console.error(e);\n      }\n      scope.manager.itemError(url);\n      scope.manager.itemEnd(url);\n    };\n    const loader = new FileLoader(this.manager);\n    loader.setPath(this.path);\n    loader.setResponseType('arraybuffer');\n    loader.setRequestHeader(this.requestHeader);\n    loader.setWithCredentials(this.withCredentials);\n    loader.load(url, function (data) {\n      try {\n        scope.parse(data, resourcePath, function (gltf) {\n          onLoad(gltf);\n          scope.manager.itemEnd(url);\n        }, _onError);\n      } catch (e) {\n        _onError(e);\n      }\n    }, onProgress, _onError);\n  }\n\n  /**\n   * Sets the given Draco loader to this loader. Required for decoding assets\n   * compressed with the `KHR_draco_mesh_compression` extension.\n   *\n   * @param {DRACOLoader} dracoLoader - The Draco loader to set.\n   * @return {GLTFLoader} A reference to this loader.\n   */\n  setDRACOLoader(dracoLoader) {\n    this.dracoLoader = dracoLoader;\n    return this;\n  }\n\n  /**\n   * Sets the given KTX2 loader to this loader. Required for loading KTX2\n   * compressed textures.\n   *\n   * @param {KTX2Loader} ktx2Loader - The KTX2 loader to set.\n   * @return {GLTFLoader} A reference to this loader.\n   */\n  setKTX2Loader(ktx2Loader) {\n    this.ktx2Loader = ktx2Loader;\n    return this;\n  }\n\n  /**\n   * Sets the given meshopt decoder. Required for decoding assets\n   * compressed with the `EXT_meshopt_compression` extension.\n   *\n   * @param {Object} meshoptDecoder - The meshopt decoder to set.\n   * @return {GLTFLoader} A reference to this loader.\n   */\n  setMeshoptDecoder(meshoptDecoder) {\n    this.meshoptDecoder = meshoptDecoder;\n    return this;\n  }\n\n  /**\n   * Registers a plugin callback. This API is internally used to implement the various\n   * glTF extensions but can also used by third-party code to add additional logic\n   * to the loader.\n   *\n   * @param {function(parser:GLTFParser)} callback - The callback function to register.\n   * @return {GLTFLoader} A reference to this loader.\n   */\n  register(callback) {\n    if (this.pluginCallbacks.indexOf(callback) === -1) {\n      this.pluginCallbacks.push(callback);\n    }\n    return this;\n  }\n\n  /**\n   * Unregisters a plugin callback.\n   *\n   * @param {Function} callback - The callback function to unregister.\n   * @return {GLTFLoader} A reference to this loader.\n   */\n  unregister(callback) {\n    if (this.pluginCallbacks.indexOf(callback) !== -1) {\n      this.pluginCallbacks.splice(this.pluginCallbacks.indexOf(callback), 1);\n    }\n    return this;\n  }\n\n  /**\n   * Parses the given FBX data and returns the resulting group.\n   *\n   * @param {string|ArrayBuffer} data - The raw glTF data.\n   * @param {string} path - The URL base path.\n   * @param {function(GLTFLoader~LoadObject)} onLoad - Executed when the loading process has been finished.\n   * @param {onErrorCallback} onError - Executed when errors occur.\n   */\n  parse(data, path, onLoad, onError) {\n    let json;\n    const extensions = {};\n    const plugins = {};\n    const textDecoder = new TextDecoder();\n    if (typeof data === 'string') {\n      json = JSON.parse(data);\n    } else if (data instanceof ArrayBuffer) {\n      const magic = textDecoder.decode(new Uint8Array(data, 0, 4));\n      if (magic === BINARY_EXTENSION_HEADER_MAGIC) {\n        try {\n          extensions[EXTENSIONS.KHR_BINARY_GLTF] = new GLTFBinaryExtension(data);\n        } catch (error) {\n          if (onError) onError(error);\n          return;\n        }\n        json = JSON.parse(extensions[EXTENSIONS.KHR_BINARY_GLTF].content);\n      } else {\n        json = JSON.parse(textDecoder.decode(data));\n      }\n    } else {\n      json = data;\n    }\n    if (json.asset === undefined || json.asset.version[0] < 2) {\n      if (onError) onError(new Error('THREE.GLTFLoader: Unsupported asset. glTF versions >=2.0 are supported.'));\n      return;\n    }\n    const parser = new GLTFParser(json, {\n      path: path || this.resourcePath || '',\n      crossOrigin: this.crossOrigin,\n      requestHeader: this.requestHeader,\n      manager: this.manager,\n      ktx2Loader: this.ktx2Loader,\n      meshoptDecoder: this.meshoptDecoder\n    });\n    parser.fileLoader.setRequestHeader(this.requestHeader);\n    for (let i = 0; i < this.pluginCallbacks.length; i++) {\n      const plugin = this.pluginCallbacks[i](parser);\n      if (!plugin.name) console.error('THREE.GLTFLoader: Invalid plugin found: missing name');\n      plugins[plugin.name] = plugin;\n\n      // Workaround to avoid determining as unknown extension\n      // in addUnknownExtensionsToUserData().\n      // Remove this workaround if we move all the existing\n      // extension handlers to plugin system\n      extensions[plugin.name] = true;\n    }\n    if (json.extensionsUsed) {\n      for (let i = 0; i < json.extensionsUsed.length; ++i) {\n        const extensionName = json.extensionsUsed[i];\n        const extensionsRequired = json.extensionsRequired || [];\n        switch (extensionName) {\n          case EXTENSIONS.KHR_MATERIALS_UNLIT:\n            extensions[extensionName] = new GLTFMaterialsUnlitExtension();\n            break;\n          case EXTENSIONS.KHR_DRACO_MESH_COMPRESSION:\n            extensions[extensionName] = new GLTFDracoMeshCompressionExtension(json, this.dracoLoader);\n            break;\n          case EXTENSIONS.KHR_TEXTURE_TRANSFORM:\n            extensions[extensionName] = new GLTFTextureTransformExtension();\n            break;\n          case EXTENSIONS.KHR_MESH_QUANTIZATION:\n            extensions[extensionName] = new GLTFMeshQuantizationExtension();\n            break;\n          default:\n            if (extensionsRequired.indexOf(extensionName) >= 0 && plugins[extensionName] === undefined) {\n              console.warn('THREE.GLTFLoader: Unknown extension \"' + extensionName + '\".');\n            }\n        }\n      }\n    }\n    parser.setExtensions(extensions);\n    parser.setPlugins(plugins);\n    parser.parse(onLoad, onError);\n  }\n\n  /**\n   * Async version of {@link GLTFLoader#parse}.\n   *\n   * @async\n   * @param {string|ArrayBuffer} data - The raw glTF data.\n   * @param {string} path - The URL base path.\n   * @return {Promise<GLTFLoader~LoadObject>} A Promise that resolves with the loaded glTF when the parsing has been finished.\n   */\n  parseAsync(data, path) {\n    const scope = this;\n    return new Promise(function (resolve, reject) {\n      scope.parse(data, path, resolve, reject);\n    });\n  }\n}\n\n/* GLTFREGISTRY */\n\nfunction GLTFRegistry() {\n  let objects = {};\n  return {\n    get: function (key) {\n      return objects[key];\n    },\n    add: function (key, object) {\n      objects[key] = object;\n    },\n    remove: function (key) {\n      delete objects[key];\n    },\n    removeAll: function () {\n      objects = {};\n    }\n  };\n}\n\n/*********************************/\n/********** EXTENSIONS ***********/\n/*********************************/\n\nconst EXTENSIONS = {\n  KHR_BINARY_GLTF: 'KHR_binary_glTF',\n  KHR_DRACO_MESH_COMPRESSION: 'KHR_draco_mesh_compression',\n  KHR_LIGHTS_PUNCTUAL: 'KHR_lights_punctual',\n  KHR_MATERIALS_CLEARCOAT: 'KHR_materials_clearcoat',\n  KHR_MATERIALS_DISPERSION: 'KHR_materials_dispersion',\n  KHR_MATERIALS_IOR: 'KHR_materials_ior',\n  KHR_MATERIALS_SHEEN: 'KHR_materials_sheen',\n  KHR_MATERIALS_SPECULAR: 'KHR_materials_specular',\n  KHR_MATERIALS_TRANSMISSION: 'KHR_materials_transmission',\n  KHR_MATERIALS_IRIDESCENCE: 'KHR_materials_iridescence',\n  KHR_MATERIALS_ANISOTROPY: 'KHR_materials_anisotropy',\n  KHR_MATERIALS_UNLIT: 'KHR_materials_unlit',\n  KHR_MATERIALS_VOLUME: 'KHR_materials_volume',\n  KHR_TEXTURE_BASISU: 'KHR_texture_basisu',\n  KHR_TEXTURE_TRANSFORM: 'KHR_texture_transform',\n  KHR_MESH_QUANTIZATION: 'KHR_mesh_quantization',\n  KHR_MATERIALS_EMISSIVE_STRENGTH: 'KHR_materials_emissive_strength',\n  EXT_MATERIALS_BUMP: 'EXT_materials_bump',\n  EXT_TEXTURE_WEBP: 'EXT_texture_webp',\n  EXT_TEXTURE_AVIF: 'EXT_texture_avif',\n  EXT_MESHOPT_COMPRESSION: 'EXT_meshopt_compression',\n  EXT_MESH_GPU_INSTANCING: 'EXT_mesh_gpu_instancing'\n};\n\n/**\n * Punctual Lights Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_lights_punctual\n *\n * @private\n */\nclass GLTFLightsExtension {\n  constructor(parser) {\n    this.parser = parser;\n    this.name = EXTENSIONS.KHR_LIGHTS_PUNCTUAL;\n\n    // Object3D instance caches\n    this.cache = {\n      refs: {},\n      uses: {}\n    };\n  }\n  _markDefs() {\n    const parser = this.parser;\n    const nodeDefs = this.parser.json.nodes || [];\n    for (let nodeIndex = 0, nodeLength = nodeDefs.length; nodeIndex < nodeLength; nodeIndex++) {\n      const nodeDef = nodeDefs[nodeIndex];\n      if (nodeDef.extensions && nodeDef.extensions[this.name] && nodeDef.extensions[this.name].light !== undefined) {\n        parser._addNodeRef(this.cache, nodeDef.extensions[this.name].light);\n      }\n    }\n  }\n  _loadLight(lightIndex) {\n    const parser = this.parser;\n    const cacheKey = 'light:' + lightIndex;\n    let dependency = parser.cache.get(cacheKey);\n    if (dependency) return dependency;\n    const json = parser.json;\n    const extensions = json.extensions && json.extensions[this.name] || {};\n    const lightDefs = extensions.lights || [];\n    const lightDef = lightDefs[lightIndex];\n    let lightNode;\n    const color = new Color(0xffffff);\n    if (lightDef.color !== undefined) color.setRGB(lightDef.color[0], lightDef.color[1], lightDef.color[2], LinearSRGBColorSpace);\n    const range = lightDef.range !== undefined ? lightDef.range : 0;\n    switch (lightDef.type) {\n      case 'directional':\n        lightNode = new DirectionalLight(color);\n        lightNode.target.position.set(0, 0, -1);\n        lightNode.add(lightNode.target);\n        break;\n      case 'point':\n        lightNode = new PointLight(color);\n        lightNode.distance = range;\n        break;\n      case 'spot':\n        lightNode = new SpotLight(color);\n        lightNode.distance = range;\n        // Handle spotlight properties.\n        lightDef.spot = lightDef.spot || {};\n        lightDef.spot.innerConeAngle = lightDef.spot.innerConeAngle !== undefined ? lightDef.spot.innerConeAngle : 0;\n        lightDef.spot.outerConeAngle = lightDef.spot.outerConeAngle !== undefined ? lightDef.spot.outerConeAngle : Math.PI / 4.0;\n        lightNode.angle = lightDef.spot.outerConeAngle;\n        lightNode.penumbra = 1.0 - lightDef.spot.innerConeAngle / lightDef.spot.outerConeAngle;\n        lightNode.target.position.set(0, 0, -1);\n        lightNode.add(lightNode.target);\n        break;\n      default:\n        throw new Error('THREE.GLTFLoader: Unexpected light type: ' + lightDef.type);\n    }\n\n    // Some lights (e.g. spot) default to a position other than the origin. Reset the position\n    // here, because node-level parsing will only override position if explicitly specified.\n    lightNode.position.set(0, 0, 0);\n    assignExtrasToUserData(lightNode, lightDef);\n    if (lightDef.intensity !== undefined) lightNode.intensity = lightDef.intensity;\n    lightNode.name = parser.createUniqueName(lightDef.name || 'light_' + lightIndex);\n    dependency = Promise.resolve(lightNode);\n    parser.cache.add(cacheKey, dependency);\n    return dependency;\n  }\n  getDependency(type, index) {\n    if (type !== 'light') return;\n    return this._loadLight(index);\n  }\n  createNodeAttachment(nodeIndex) {\n    const self = this;\n    const parser = this.parser;\n    const json = parser.json;\n    const nodeDef = json.nodes[nodeIndex];\n    const lightDef = nodeDef.extensions && nodeDef.extensions[this.name] || {};\n    const lightIndex = lightDef.light;\n    if (lightIndex === undefined) return null;\n    return this._loadLight(lightIndex).then(function (light) {\n      return parser._getNodeRef(self.cache, lightIndex, light);\n    });\n  }\n}\n\n/**\n * Unlit Materials Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_unlit\n *\n * @private\n */\nclass GLTFMaterialsUnlitExtension {\n  constructor() {\n    this.name = EXTENSIONS.KHR_MATERIALS_UNLIT;\n  }\n  getMaterialType() {\n    return MeshBasicMaterial;\n  }\n  extendParams(materialParams, materialDef, parser) {\n    const pending = [];\n    materialParams.color = new Color(1.0, 1.0, 1.0);\n    materialParams.opacity = 1.0;\n    const metallicRoughness = materialDef.pbrMetallicRoughness;\n    if (metallicRoughness) {\n      if (Array.isArray(metallicRoughness.baseColorFactor)) {\n        const array = metallicRoughness.baseColorFactor;\n        materialParams.color.setRGB(array[0], array[1], array[2], LinearSRGBColorSpace);\n        materialParams.opacity = array[3];\n      }\n      if (metallicRoughness.baseColorTexture !== undefined) {\n        pending.push(parser.assignTexture(materialParams, 'map', metallicRoughness.baseColorTexture, SRGBColorSpace));\n      }\n    }\n    return Promise.all(pending);\n  }\n}\n\n/**\n * Materials Emissive Strength Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/blob/5768b3ce0ef32bc39cdf1bef10b948586635ead3/extensions/2.0/Khronos/KHR_materials_emissive_strength/README.md\n *\n * @private\n */\nclass GLTFMaterialsEmissiveStrengthExtension {\n  constructor(parser) {\n    this.parser = parser;\n    this.name = EXTENSIONS.KHR_MATERIALS_EMISSIVE_STRENGTH;\n  }\n  extendMaterialParams(materialIndex, materialParams) {\n    const parser = this.parser;\n    const materialDef = parser.json.materials[materialIndex];\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) {\n      return Promise.resolve();\n    }\n    const emissiveStrength = materialDef.extensions[this.name].emissiveStrength;\n    if (emissiveStrength !== undefined) {\n      materialParams.emissiveIntensity = emissiveStrength;\n    }\n    return Promise.resolve();\n  }\n}\n\n/**\n * Clearcoat Materials Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_clearcoat\n *\n * @private\n */\nclass GLTFMaterialsClearcoatExtension {\n  constructor(parser) {\n    this.parser = parser;\n    this.name = EXTENSIONS.KHR_MATERIALS_CLEARCOAT;\n  }\n  getMaterialType(materialIndex) {\n    const parser = this.parser;\n    const materialDef = parser.json.materials[materialIndex];\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) return null;\n    return MeshPhysicalMaterial;\n  }\n  extendMaterialParams(materialIndex, materialParams) {\n    const parser = this.parser;\n    const materialDef = parser.json.materials[materialIndex];\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) {\n      return Promise.resolve();\n    }\n    const pending = [];\n    const extension = materialDef.extensions[this.name];\n    if (extension.clearcoatFactor !== undefined) {\n      materialParams.clearcoat = extension.clearcoatFactor;\n    }\n    if (extension.clearcoatTexture !== undefined) {\n      pending.push(parser.assignTexture(materialParams, 'clearcoatMap', extension.clearcoatTexture));\n    }\n    if (extension.clearcoatRoughnessFactor !== undefined) {\n      materialParams.clearcoatRoughness = extension.clearcoatRoughnessFactor;\n    }\n    if (extension.clearcoatRoughnessTexture !== undefined) {\n      pending.push(parser.assignTexture(materialParams, 'clearcoatRoughnessMap', extension.clearcoatRoughnessTexture));\n    }\n    if (extension.clearcoatNormalTexture !== undefined) {\n      pending.push(parser.assignTexture(materialParams, 'clearcoatNormalMap', extension.clearcoatNormalTexture));\n      if (extension.clearcoatNormalTexture.scale !== undefined) {\n        const scale = extension.clearcoatNormalTexture.scale;\n        materialParams.clearcoatNormalScale = new Vector2(scale, scale);\n      }\n    }\n    return Promise.all(pending);\n  }\n}\n\n/**\n * Materials dispersion Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/main/extensions/2.0/Khronos/KHR_materials_dispersion\n *\n * @private\n */\nclass GLTFMaterialsDispersionExtension {\n  constructor(parser) {\n    this.parser = parser;\n    this.name = EXTENSIONS.KHR_MATERIALS_DISPERSION;\n  }\n  getMaterialType(materialIndex) {\n    const parser = this.parser;\n    const materialDef = parser.json.materials[materialIndex];\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) return null;\n    return MeshPhysicalMaterial;\n  }\n  extendMaterialParams(materialIndex, materialParams) {\n    const parser = this.parser;\n    const materialDef = parser.json.materials[materialIndex];\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) {\n      return Promise.resolve();\n    }\n    const extension = materialDef.extensions[this.name];\n    materialParams.dispersion = extension.dispersion !== undefined ? extension.dispersion : 0;\n    return Promise.resolve();\n  }\n}\n\n/**\n * Iridescence Materials Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_iridescence\n *\n * @private\n */\nclass GLTFMaterialsIridescenceExtension {\n  constructor(parser) {\n    this.parser = parser;\n    this.name = EXTENSIONS.KHR_MATERIALS_IRIDESCENCE;\n  }\n  getMaterialType(materialIndex) {\n    const parser = this.parser;\n    const materialDef = parser.json.materials[materialIndex];\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) return null;\n    return MeshPhysicalMaterial;\n  }\n  extendMaterialParams(materialIndex, materialParams) {\n    const parser = this.parser;\n    const materialDef = parser.json.materials[materialIndex];\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) {\n      return Promise.resolve();\n    }\n    const pending = [];\n    const extension = materialDef.extensions[this.name];\n    if (extension.iridescenceFactor !== undefined) {\n      materialParams.iridescence = extension.iridescenceFactor;\n    }\n    if (extension.iridescenceTexture !== undefined) {\n      pending.push(parser.assignTexture(materialParams, 'iridescenceMap', extension.iridescenceTexture));\n    }\n    if (extension.iridescenceIor !== undefined) {\n      materialParams.iridescenceIOR = extension.iridescenceIor;\n    }\n    if (materialParams.iridescenceThicknessRange === undefined) {\n      materialParams.iridescenceThicknessRange = [100, 400];\n    }\n    if (extension.iridescenceThicknessMinimum !== undefined) {\n      materialParams.iridescenceThicknessRange[0] = extension.iridescenceThicknessMinimum;\n    }\n    if (extension.iridescenceThicknessMaximum !== undefined) {\n      materialParams.iridescenceThicknessRange[1] = extension.iridescenceThicknessMaximum;\n    }\n    if (extension.iridescenceThicknessTexture !== undefined) {\n      pending.push(parser.assignTexture(materialParams, 'iridescenceThicknessMap', extension.iridescenceThicknessTexture));\n    }\n    return Promise.all(pending);\n  }\n}\n\n/**\n * Sheen Materials Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/main/extensions/2.0/Khronos/KHR_materials_sheen\n *\n * @private\n */\nclass GLTFMaterialsSheenExtension {\n  constructor(parser) {\n    this.parser = parser;\n    this.name = EXTENSIONS.KHR_MATERIALS_SHEEN;\n  }\n  getMaterialType(materialIndex) {\n    const parser = this.parser;\n    const materialDef = parser.json.materials[materialIndex];\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) return null;\n    return MeshPhysicalMaterial;\n  }\n  extendMaterialParams(materialIndex, materialParams) {\n    const parser = this.parser;\n    const materialDef = parser.json.materials[materialIndex];\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) {\n      return Promise.resolve();\n    }\n    const pending = [];\n    materialParams.sheenColor = new Color(0, 0, 0);\n    materialParams.sheenRoughness = 0;\n    materialParams.sheen = 1;\n    const extension = materialDef.extensions[this.name];\n    if (extension.sheenColorFactor !== undefined) {\n      const colorFactor = extension.sheenColorFactor;\n      materialParams.sheenColor.setRGB(colorFactor[0], colorFactor[1], colorFactor[2], LinearSRGBColorSpace);\n    }\n    if (extension.sheenRoughnessFactor !== undefined) {\n      materialParams.sheenRoughness = extension.sheenRoughnessFactor;\n    }\n    if (extension.sheenColorTexture !== undefined) {\n      pending.push(parser.assignTexture(materialParams, 'sheenColorMap', extension.sheenColorTexture, SRGBColorSpace));\n    }\n    if (extension.sheenRoughnessTexture !== undefined) {\n      pending.push(parser.assignTexture(materialParams, 'sheenRoughnessMap', extension.sheenRoughnessTexture));\n    }\n    return Promise.all(pending);\n  }\n}\n\n/**\n * Transmission Materials Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_transmission\n * Draft: https://github.com/KhronosGroup/glTF/pull/1698\n *\n * @private\n */\nclass GLTFMaterialsTransmissionExtension {\n  constructor(parser) {\n    this.parser = parser;\n    this.name = EXTENSIONS.KHR_MATERIALS_TRANSMISSION;\n  }\n  getMaterialType(materialIndex) {\n    const parser = this.parser;\n    const materialDef = parser.json.materials[materialIndex];\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) return null;\n    return MeshPhysicalMaterial;\n  }\n  extendMaterialParams(materialIndex, materialParams) {\n    const parser = this.parser;\n    const materialDef = parser.json.materials[materialIndex];\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) {\n      return Promise.resolve();\n    }\n    const pending = [];\n    const extension = materialDef.extensions[this.name];\n    if (extension.transmissionFactor !== undefined) {\n      materialParams.transmission = extension.transmissionFactor;\n    }\n    if (extension.transmissionTexture !== undefined) {\n      pending.push(parser.assignTexture(materialParams, 'transmissionMap', extension.transmissionTexture));\n    }\n    return Promise.all(pending);\n  }\n}\n\n/**\n * Materials Volume Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_volume\n *\n * @private\n */\nclass GLTFMaterialsVolumeExtension {\n  constructor(parser) {\n    this.parser = parser;\n    this.name = EXTENSIONS.KHR_MATERIALS_VOLUME;\n  }\n  getMaterialType(materialIndex) {\n    const parser = this.parser;\n    const materialDef = parser.json.materials[materialIndex];\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) return null;\n    return MeshPhysicalMaterial;\n  }\n  extendMaterialParams(materialIndex, materialParams) {\n    const parser = this.parser;\n    const materialDef = parser.json.materials[materialIndex];\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) {\n      return Promise.resolve();\n    }\n    const pending = [];\n    const extension = materialDef.extensions[this.name];\n    materialParams.thickness = extension.thicknessFactor !== undefined ? extension.thicknessFactor : 0;\n    if (extension.thicknessTexture !== undefined) {\n      pending.push(parser.assignTexture(materialParams, 'thicknessMap', extension.thicknessTexture));\n    }\n    materialParams.attenuationDistance = extension.attenuationDistance || Infinity;\n    const colorArray = extension.attenuationColor || [1, 1, 1];\n    materialParams.attenuationColor = new Color().setRGB(colorArray[0], colorArray[1], colorArray[2], LinearSRGBColorSpace);\n    return Promise.all(pending);\n  }\n}\n\n/**\n * Materials ior Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_ior\n *\n * @private\n */\nclass GLTFMaterialsIorExtension {\n  constructor(parser) {\n    this.parser = parser;\n    this.name = EXTENSIONS.KHR_MATERIALS_IOR;\n  }\n  getMaterialType(materialIndex) {\n    const parser = this.parser;\n    const materialDef = parser.json.materials[materialIndex];\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) return null;\n    return MeshPhysicalMaterial;\n  }\n  extendMaterialParams(materialIndex, materialParams) {\n    const parser = this.parser;\n    const materialDef = parser.json.materials[materialIndex];\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) {\n      return Promise.resolve();\n    }\n    const extension = materialDef.extensions[this.name];\n    materialParams.ior = extension.ior !== undefined ? extension.ior : 1.5;\n    return Promise.resolve();\n  }\n}\n\n/**\n * Materials specular Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_specular\n *\n * @private\n */\nclass GLTFMaterialsSpecularExtension {\n  constructor(parser) {\n    this.parser = parser;\n    this.name = EXTENSIONS.KHR_MATERIALS_SPECULAR;\n  }\n  getMaterialType(materialIndex) {\n    const parser = this.parser;\n    const materialDef = parser.json.materials[materialIndex];\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) return null;\n    return MeshPhysicalMaterial;\n  }\n  extendMaterialParams(materialIndex, materialParams) {\n    const parser = this.parser;\n    const materialDef = parser.json.materials[materialIndex];\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) {\n      return Promise.resolve();\n    }\n    const pending = [];\n    const extension = materialDef.extensions[this.name];\n    materialParams.specularIntensity = extension.specularFactor !== undefined ? extension.specularFactor : 1.0;\n    if (extension.specularTexture !== undefined) {\n      pending.push(parser.assignTexture(materialParams, 'specularIntensityMap', extension.specularTexture));\n    }\n    const colorArray = extension.specularColorFactor || [1, 1, 1];\n    materialParams.specularColor = new Color().setRGB(colorArray[0], colorArray[1], colorArray[2], LinearSRGBColorSpace);\n    if (extension.specularColorTexture !== undefined) {\n      pending.push(parser.assignTexture(materialParams, 'specularColorMap', extension.specularColorTexture, SRGBColorSpace));\n    }\n    return Promise.all(pending);\n  }\n}\n\n/**\n * Materials bump Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/EXT_materials_bump\n *\n * @private\n */\nclass GLTFMaterialsBumpExtension {\n  constructor(parser) {\n    this.parser = parser;\n    this.name = EXTENSIONS.EXT_MATERIALS_BUMP;\n  }\n  getMaterialType(materialIndex) {\n    const parser = this.parser;\n    const materialDef = parser.json.materials[materialIndex];\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) return null;\n    return MeshPhysicalMaterial;\n  }\n  extendMaterialParams(materialIndex, materialParams) {\n    const parser = this.parser;\n    const materialDef = parser.json.materials[materialIndex];\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) {\n      return Promise.resolve();\n    }\n    const pending = [];\n    const extension = materialDef.extensions[this.name];\n    materialParams.bumpScale = extension.bumpFactor !== undefined ? extension.bumpFactor : 1.0;\n    if (extension.bumpTexture !== undefined) {\n      pending.push(parser.assignTexture(materialParams, 'bumpMap', extension.bumpTexture));\n    }\n    return Promise.all(pending);\n  }\n}\n\n/**\n * Materials anisotropy Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_anisotropy\n *\n * @private\n */\nclass GLTFMaterialsAnisotropyExtension {\n  constructor(parser) {\n    this.parser = parser;\n    this.name = EXTENSIONS.KHR_MATERIALS_ANISOTROPY;\n  }\n  getMaterialType(materialIndex) {\n    const parser = this.parser;\n    const materialDef = parser.json.materials[materialIndex];\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) return null;\n    return MeshPhysicalMaterial;\n  }\n  extendMaterialParams(materialIndex, materialParams) {\n    const parser = this.parser;\n    const materialDef = parser.json.materials[materialIndex];\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) {\n      return Promise.resolve();\n    }\n    const pending = [];\n    const extension = materialDef.extensions[this.name];\n    if (extension.anisotropyStrength !== undefined) {\n      materialParams.anisotropy = extension.anisotropyStrength;\n    }\n    if (extension.anisotropyRotation !== undefined) {\n      materialParams.anisotropyRotation = extension.anisotropyRotation;\n    }\n    if (extension.anisotropyTexture !== undefined) {\n      pending.push(parser.assignTexture(materialParams, 'anisotropyMap', extension.anisotropyTexture));\n    }\n    return Promise.all(pending);\n  }\n}\n\n/**\n * BasisU Texture Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_texture_basisu\n *\n * @private\n */\nclass GLTFTextureBasisUExtension {\n  constructor(parser) {\n    this.parser = parser;\n    this.name = EXTENSIONS.KHR_TEXTURE_BASISU;\n  }\n  loadTexture(textureIndex) {\n    const parser = this.parser;\n    const json = parser.json;\n    const textureDef = json.textures[textureIndex];\n    if (!textureDef.extensions || !textureDef.extensions[this.name]) {\n      return null;\n    }\n    const extension = textureDef.extensions[this.name];\n    const loader = parser.options.ktx2Loader;\n    if (!loader) {\n      if (json.extensionsRequired && json.extensionsRequired.indexOf(this.name) >= 0) {\n        throw new Error('THREE.GLTFLoader: setKTX2Loader must be called before loading KTX2 textures');\n      } else {\n        // Assumes that the extension is optional and that a fallback texture is present\n        return null;\n      }\n    }\n    return parser.loadTextureImage(textureIndex, extension.source, loader);\n  }\n}\n\n/**\n * WebP Texture Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Vendor/EXT_texture_webp\n *\n * @private\n */\nclass GLTFTextureWebPExtension {\n  constructor(parser) {\n    this.parser = parser;\n    this.name = EXTENSIONS.EXT_TEXTURE_WEBP;\n    this.isSupported = null;\n  }\n  loadTexture(textureIndex) {\n    const name = this.name;\n    const parser = this.parser;\n    const json = parser.json;\n    const textureDef = json.textures[textureIndex];\n    if (!textureDef.extensions || !textureDef.extensions[name]) {\n      return null;\n    }\n    const extension = textureDef.extensions[name];\n    const source = json.images[extension.source];\n    let loader = parser.textureLoader;\n    if (source.uri) {\n      const handler = parser.options.manager.getHandler(source.uri);\n      if (handler !== null) loader = handler;\n    }\n    return this.detectSupport().then(function (isSupported) {\n      if (isSupported) return parser.loadTextureImage(textureIndex, extension.source, loader);\n      if (json.extensionsRequired && json.extensionsRequired.indexOf(name) >= 0) {\n        throw new Error('THREE.GLTFLoader: WebP required by asset but unsupported.');\n      }\n\n      // Fall back to PNG or JPEG.\n      return parser.loadTexture(textureIndex);\n    });\n  }\n  detectSupport() {\n    if (!this.isSupported) {\n      this.isSupported = new Promise(function (resolve) {\n        const image = new Image();\n\n        // Lossy test image. Support for lossy images doesn't guarantee support for all\n        // WebP images, unfortunately.\n        image.src = 'data:image/webp;base64,UklGRiIAAABXRUJQVlA4IBYAAAAwAQCdASoBAAEADsD+JaQAA3AAAAAA';\n        image.onload = image.onerror = function () {\n          resolve(image.height === 1);\n        };\n      });\n    }\n    return this.isSupported;\n  }\n}\n\n/**\n * AVIF Texture Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Vendor/EXT_texture_avif\n *\n * @private\n */\nclass GLTFTextureAVIFExtension {\n  constructor(parser) {\n    this.parser = parser;\n    this.name = EXTENSIONS.EXT_TEXTURE_AVIF;\n    this.isSupported = null;\n  }\n  loadTexture(textureIndex) {\n    const name = this.name;\n    const parser = this.parser;\n    const json = parser.json;\n    const textureDef = json.textures[textureIndex];\n    if (!textureDef.extensions || !textureDef.extensions[name]) {\n      return null;\n    }\n    const extension = textureDef.extensions[name];\n    const source = json.images[extension.source];\n    let loader = parser.textureLoader;\n    if (source.uri) {\n      const handler = parser.options.manager.getHandler(source.uri);\n      if (handler !== null) loader = handler;\n    }\n    return this.detectSupport().then(function (isSupported) {\n      if (isSupported) return parser.loadTextureImage(textureIndex, extension.source, loader);\n      if (json.extensionsRequired && json.extensionsRequired.indexOf(name) >= 0) {\n        throw new Error('THREE.GLTFLoader: AVIF required by asset but unsupported.');\n      }\n\n      // Fall back to PNG or JPEG.\n      return parser.loadTexture(textureIndex);\n    });\n  }\n  detectSupport() {\n    if (!this.isSupported) {\n      this.isSupported = new Promise(function (resolve) {\n        const image = new Image();\n\n        // Lossy test image.\n        image.src = 'data:image/avif;base64,AAAAIGZ0eXBhdmlmAAAAAGF2aWZtaWYxbWlhZk1BMUIAAADybWV0YQAAAAAAAAAoaGRscgAAAAAAAAAAcGljdAAAAAAAAAAAAAAAAGxpYmF2aWYAAAAADnBpdG0AAAAAAAEAAAAeaWxvYwAAAABEAAABAAEAAAABAAABGgAAABcAAAAoaWluZgAAAAAAAQAAABppbmZlAgAAAAABAABhdjAxQ29sb3IAAAAAamlwcnAAAABLaXBjbwAAABRpc3BlAAAAAAAAAAEAAAABAAAAEHBpeGkAAAAAAwgICAAAAAxhdjFDgQAMAAAAABNjb2xybmNseAACAAIABoAAAAAXaXBtYQAAAAAAAAABAAEEAQKDBAAAAB9tZGF0EgAKCBgABogQEDQgMgkQAAAAB8dSLfI=';\n        image.onload = image.onerror = function () {\n          resolve(image.height === 1);\n        };\n      });\n    }\n    return this.isSupported;\n  }\n}\n\n/**\n * meshopt BufferView Compression Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Vendor/EXT_meshopt_compression\n *\n * @private\n */\nclass GLTFMeshoptCompression {\n  constructor(parser) {\n    this.name = EXTENSIONS.EXT_MESHOPT_COMPRESSION;\n    this.parser = parser;\n  }\n  loadBufferView(index) {\n    const json = this.parser.json;\n    const bufferView = json.bufferViews[index];\n    if (bufferView.extensions && bufferView.extensions[this.name]) {\n      const extensionDef = bufferView.extensions[this.name];\n      const buffer = this.parser.getDependency('buffer', extensionDef.buffer);\n      const decoder = this.parser.options.meshoptDecoder;\n      if (!decoder || !decoder.supported) {\n        if (json.extensionsRequired && json.extensionsRequired.indexOf(this.name) >= 0) {\n          throw new Error('THREE.GLTFLoader: setMeshoptDecoder must be called before loading compressed files');\n        } else {\n          // Assumes that the extension is optional and that fallback buffer data is present\n          return null;\n        }\n      }\n      return buffer.then(function (res) {\n        const byteOffset = extensionDef.byteOffset || 0;\n        const byteLength = extensionDef.byteLength || 0;\n        const count = extensionDef.count;\n        const stride = extensionDef.byteStride;\n        const source = new Uint8Array(res, byteOffset, byteLength);\n        if (decoder.decodeGltfBufferAsync) {\n          return decoder.decodeGltfBufferAsync(count, stride, source, extensionDef.mode, extensionDef.filter).then(function (res) {\n            return res.buffer;\n          });\n        } else {\n          // Support for MeshoptDecoder 0.18 or earlier, without decodeGltfBufferAsync\n          return decoder.ready.then(function () {\n            const result = new ArrayBuffer(count * stride);\n            decoder.decodeGltfBuffer(new Uint8Array(result), count, stride, source, extensionDef.mode, extensionDef.filter);\n            return result;\n          });\n        }\n      });\n    } else {\n      return null;\n    }\n  }\n}\n\n/**\n * GPU Instancing Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Vendor/EXT_mesh_gpu_instancing\n *\n * @private\n */\nclass GLTFMeshGpuInstancing {\n  constructor(parser) {\n    this.name = EXTENSIONS.EXT_MESH_GPU_INSTANCING;\n    this.parser = parser;\n  }\n  createNodeMesh(nodeIndex) {\n    const json = this.parser.json;\n    const nodeDef = json.nodes[nodeIndex];\n    if (!nodeDef.extensions || !nodeDef.extensions[this.name] || nodeDef.mesh === undefined) {\n      return null;\n    }\n    const meshDef = json.meshes[nodeDef.mesh];\n\n    // No Points or Lines + Instancing support yet\n\n    for (const primitive of meshDef.primitives) {\n      if (primitive.mode !== WEBGL_CONSTANTS.TRIANGLES && primitive.mode !== WEBGL_CONSTANTS.TRIANGLE_STRIP && primitive.mode !== WEBGL_CONSTANTS.TRIANGLE_FAN && primitive.mode !== undefined) {\n        return null;\n      }\n    }\n    const extensionDef = nodeDef.extensions[this.name];\n    const attributesDef = extensionDef.attributes;\n\n    // @TODO: Can we support InstancedMesh + SkinnedMesh?\n\n    const pending = [];\n    const attributes = {};\n    for (const key in attributesDef) {\n      pending.push(this.parser.getDependency('accessor', attributesDef[key]).then(accessor => {\n        attributes[key] = accessor;\n        return attributes[key];\n      }));\n    }\n    if (pending.length < 1) {\n      return null;\n    }\n    pending.push(this.parser.createNodeMesh(nodeIndex));\n    return Promise.all(pending).then(results => {\n      const nodeObject = results.pop();\n      const meshes = nodeObject.isGroup ? nodeObject.children : [nodeObject];\n      const count = results[0].count; // All attribute counts should be same\n      const instancedMeshes = [];\n      for (const mesh of meshes) {\n        // Temporal variables\n        const m = new Matrix4();\n        const p = new Vector3();\n        const q = new Quaternion();\n        const s = new Vector3(1, 1, 1);\n        const instancedMesh = new InstancedMesh(mesh.geometry, mesh.material, count);\n        for (let i = 0; i < count; i++) {\n          if (attributes.TRANSLATION) {\n            p.fromBufferAttribute(attributes.TRANSLATION, i);\n          }\n          if (attributes.ROTATION) {\n            q.fromBufferAttribute(attributes.ROTATION, i);\n          }\n          if (attributes.SCALE) {\n            s.fromBufferAttribute(attributes.SCALE, i);\n          }\n          instancedMesh.setMatrixAt(i, m.compose(p, q, s));\n        }\n\n        // Add instance attributes to the geometry, excluding TRS.\n        for (const attributeName in attributes) {\n          if (attributeName === '_COLOR_0') {\n            const attr = attributes[attributeName];\n            instancedMesh.instanceColor = new InstancedBufferAttribute(attr.array, attr.itemSize, attr.normalized);\n          } else if (attributeName !== 'TRANSLATION' && attributeName !== 'ROTATION' && attributeName !== 'SCALE') {\n            mesh.geometry.setAttribute(attributeName, attributes[attributeName]);\n          }\n        }\n\n        // Just in case\n        Object3D.prototype.copy.call(instancedMesh, mesh);\n        this.parser.assignFinalMaterial(instancedMesh);\n        instancedMeshes.push(instancedMesh);\n      }\n      if (nodeObject.isGroup) {\n        nodeObject.clear();\n        nodeObject.add(...instancedMeshes);\n        return nodeObject;\n      }\n      return instancedMeshes[0];\n    });\n  }\n}\n\n/* BINARY EXTENSION */\nconst BINARY_EXTENSION_HEADER_MAGIC = 'glTF';\nconst BINARY_EXTENSION_HEADER_LENGTH = 12;\nconst BINARY_EXTENSION_CHUNK_TYPES = {\n  JSON: 0x4E4F534A,\n  BIN: 0x004E4942\n};\nclass GLTFBinaryExtension {\n  constructor(data) {\n    this.name = EXTENSIONS.KHR_BINARY_GLTF;\n    this.content = null;\n    this.body = null;\n    const headerView = new DataView(data, 0, BINARY_EXTENSION_HEADER_LENGTH);\n    const textDecoder = new TextDecoder();\n    this.header = {\n      magic: textDecoder.decode(new Uint8Array(data.slice(0, 4))),\n      version: headerView.getUint32(4, true),\n      length: headerView.getUint32(8, true)\n    };\n    if (this.header.magic !== BINARY_EXTENSION_HEADER_MAGIC) {\n      throw new Error('THREE.GLTFLoader: Unsupported glTF-Binary header.');\n    } else if (this.header.version < 2.0) {\n      throw new Error('THREE.GLTFLoader: Legacy binary file detected.');\n    }\n    const chunkContentsLength = this.header.length - BINARY_EXTENSION_HEADER_LENGTH;\n    const chunkView = new DataView(data, BINARY_EXTENSION_HEADER_LENGTH);\n    let chunkIndex = 0;\n    while (chunkIndex < chunkContentsLength) {\n      const chunkLength = chunkView.getUint32(chunkIndex, true);\n      chunkIndex += 4;\n      const chunkType = chunkView.getUint32(chunkIndex, true);\n      chunkIndex += 4;\n      if (chunkType === BINARY_EXTENSION_CHUNK_TYPES.JSON) {\n        const contentArray = new Uint8Array(data, BINARY_EXTENSION_HEADER_LENGTH + chunkIndex, chunkLength);\n        this.content = textDecoder.decode(contentArray);\n      } else if (chunkType === BINARY_EXTENSION_CHUNK_TYPES.BIN) {\n        const byteOffset = BINARY_EXTENSION_HEADER_LENGTH + chunkIndex;\n        this.body = data.slice(byteOffset, byteOffset + chunkLength);\n      }\n\n      // Clients must ignore chunks with unknown types.\n\n      chunkIndex += chunkLength;\n    }\n    if (this.content === null) {\n      throw new Error('THREE.GLTFLoader: JSON content not found.');\n    }\n  }\n}\n\n/**\n * DRACO Mesh Compression Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_draco_mesh_compression\n *\n * @private\n */\nclass GLTFDracoMeshCompressionExtension {\n  constructor(json, dracoLoader) {\n    if (!dracoLoader) {\n      throw new Error('THREE.GLTFLoader: No DRACOLoader instance provided.');\n    }\n    this.name = EXTENSIONS.KHR_DRACO_MESH_COMPRESSION;\n    this.json = json;\n    this.dracoLoader = dracoLoader;\n    this.dracoLoader.preload();\n  }\n  decodePrimitive(primitive, parser) {\n    const json = this.json;\n    const dracoLoader = this.dracoLoader;\n    const bufferViewIndex = primitive.extensions[this.name].bufferView;\n    const gltfAttributeMap = primitive.extensions[this.name].attributes;\n    const threeAttributeMap = {};\n    const attributeNormalizedMap = {};\n    const attributeTypeMap = {};\n    for (const attributeName in gltfAttributeMap) {\n      const threeAttributeName = ATTRIBUTES[attributeName] || attributeName.toLowerCase();\n      threeAttributeMap[threeAttributeName] = gltfAttributeMap[attributeName];\n    }\n    for (const attributeName in primitive.attributes) {\n      const threeAttributeName = ATTRIBUTES[attributeName] || attributeName.toLowerCase();\n      if (gltfAttributeMap[attributeName] !== undefined) {\n        const accessorDef = json.accessors[primitive.attributes[attributeName]];\n        const componentType = WEBGL_COMPONENT_TYPES[accessorDef.componentType];\n        attributeTypeMap[threeAttributeName] = componentType.name;\n        attributeNormalizedMap[threeAttributeName] = accessorDef.normalized === true;\n      }\n    }\n    return parser.getDependency('bufferView', bufferViewIndex).then(function (bufferView) {\n      return new Promise(function (resolve, reject) {\n        dracoLoader.decodeDracoFile(bufferView, function (geometry) {\n          for (const attributeName in geometry.attributes) {\n            const attribute = geometry.attributes[attributeName];\n            const normalized = attributeNormalizedMap[attributeName];\n            if (normalized !== undefined) attribute.normalized = normalized;\n          }\n          resolve(geometry);\n        }, threeAttributeMap, attributeTypeMap, LinearSRGBColorSpace, reject);\n      });\n    });\n  }\n}\n\n/**\n * Texture Transform Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_texture_transform\n *\n * @private\n */\nclass GLTFTextureTransformExtension {\n  constructor() {\n    this.name = EXTENSIONS.KHR_TEXTURE_TRANSFORM;\n  }\n  extendTexture(texture, transform) {\n    if ((transform.texCoord === undefined || transform.texCoord === texture.channel) && transform.offset === undefined && transform.rotation === undefined && transform.scale === undefined) {\n      // See https://github.com/mrdoob/three.js/issues/21819.\n      return texture;\n    }\n    texture = texture.clone();\n    if (transform.texCoord !== undefined) {\n      texture.channel = transform.texCoord;\n    }\n    if (transform.offset !== undefined) {\n      texture.offset.fromArray(transform.offset);\n    }\n    if (transform.rotation !== undefined) {\n      texture.rotation = transform.rotation;\n    }\n    if (transform.scale !== undefined) {\n      texture.repeat.fromArray(transform.scale);\n    }\n    texture.needsUpdate = true;\n    return texture;\n  }\n}\n\n/**\n * Mesh Quantization Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_mesh_quantization\n *\n * @private\n */\nclass GLTFMeshQuantizationExtension {\n  constructor() {\n    this.name = EXTENSIONS.KHR_MESH_QUANTIZATION;\n  }\n}\n\n/*********************************/\n/********** INTERPOLATION ********/\n/*********************************/\n\n// Spline Interpolation\n// Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#appendix-c-spline-interpolation\nclass GLTFCubicSplineInterpolant extends Interpolant {\n  constructor(parameterPositions, sampleValues, sampleSize, resultBuffer) {\n    super(parameterPositions, sampleValues, sampleSize, resultBuffer);\n  }\n  copySampleValue_(index) {\n    // Copies a sample value to the result buffer. See description of glTF\n    // CUBICSPLINE values layout in interpolate_() function below.\n\n    const result = this.resultBuffer,\n      values = this.sampleValues,\n      valueSize = this.valueSize,\n      offset = index * valueSize * 3 + valueSize;\n    for (let i = 0; i !== valueSize; i++) {\n      result[i] = values[offset + i];\n    }\n    return result;\n  }\n  interpolate_(i1, t0, t, t1) {\n    const result = this.resultBuffer;\n    const values = this.sampleValues;\n    const stride = this.valueSize;\n    const stride2 = stride * 2;\n    const stride3 = stride * 3;\n    const td = t1 - t0;\n    const p = (t - t0) / td;\n    const pp = p * p;\n    const ppp = pp * p;\n    const offset1 = i1 * stride3;\n    const offset0 = offset1 - stride3;\n    const s2 = -2 * ppp + 3 * pp;\n    const s3 = ppp - pp;\n    const s0 = 1 - s2;\n    const s1 = s3 - pp + p;\n\n    // Layout of keyframe output values for CUBICSPLINE animations:\n    //   [ inTangent_1, splineVertex_1, outTangent_1, inTangent_2, splineVertex_2, ... ]\n    for (let i = 0; i !== stride; i++) {\n      const p0 = values[offset0 + i + stride]; // splineVertex_k\n      const m0 = values[offset0 + i + stride2] * td; // outTangent_k * (t_k+1 - t_k)\n      const p1 = values[offset1 + i + stride]; // splineVertex_k+1\n      const m1 = values[offset1 + i] * td; // inTangent_k+1 * (t_k+1 - t_k)\n\n      result[i] = s0 * p0 + s1 * m0 + s2 * p1 + s3 * m1;\n    }\n    return result;\n  }\n}\nconst _q = new Quaternion();\nclass GLTFCubicSplineQuaternionInterpolant extends GLTFCubicSplineInterpolant {\n  interpolate_(i1, t0, t, t1) {\n    const result = super.interpolate_(i1, t0, t, t1);\n    _q.fromArray(result).normalize().toArray(result);\n    return result;\n  }\n}\n\n/*********************************/\n/********** INTERNALS ************/\n/*********************************/\n\n/* CONSTANTS */\n\nconst WEBGL_CONSTANTS = {\n  FLOAT: 5126,\n  //FLOAT_MAT2: 35674,\n  FLOAT_MAT3: 35675,\n  FLOAT_MAT4: 35676,\n  FLOAT_VEC2: 35664,\n  FLOAT_VEC3: 35665,\n  FLOAT_VEC4: 35666,\n  LINEAR: 9729,\n  REPEAT: 10497,\n  SAMPLER_2D: 35678,\n  POINTS: 0,\n  LINES: 1,\n  LINE_LOOP: 2,\n  LINE_STRIP: 3,\n  TRIANGLES: 4,\n  TRIANGLE_STRIP: 5,\n  TRIANGLE_FAN: 6,\n  UNSIGNED_BYTE: 5121,\n  UNSIGNED_SHORT: 5123\n};\nconst WEBGL_COMPONENT_TYPES = {\n  5120: Int8Array,\n  5121: Uint8Array,\n  5122: Int16Array,\n  5123: Uint16Array,\n  5125: Uint32Array,\n  5126: Float32Array\n};\nconst WEBGL_FILTERS = {\n  9728: NearestFilter,\n  9729: LinearFilter,\n  9984: NearestMipmapNearestFilter,\n  9985: LinearMipmapNearestFilter,\n  9986: NearestMipmapLinearFilter,\n  9987: LinearMipmapLinearFilter\n};\nconst WEBGL_WRAPPINGS = {\n  33071: ClampToEdgeWrapping,\n  33648: MirroredRepeatWrapping,\n  10497: RepeatWrapping\n};\nconst WEBGL_TYPE_SIZES = {\n  'SCALAR': 1,\n  'VEC2': 2,\n  'VEC3': 3,\n  'VEC4': 4,\n  'MAT2': 4,\n  'MAT3': 9,\n  'MAT4': 16\n};\nconst ATTRIBUTES = {\n  POSITION: 'position',\n  NORMAL: 'normal',\n  TANGENT: 'tangent',\n  TEXCOORD_0: 'uv',\n  TEXCOORD_1: 'uv1',\n  TEXCOORD_2: 'uv2',\n  TEXCOORD_3: 'uv3',\n  COLOR_0: 'color',\n  WEIGHTS_0: 'skinWeight',\n  JOINTS_0: 'skinIndex'\n};\nconst PATH_PROPERTIES = {\n  scale: 'scale',\n  translation: 'position',\n  rotation: 'quaternion',\n  weights: 'morphTargetInfluences'\n};\nconst INTERPOLATION = {\n  CUBICSPLINE: undefined,\n  // We use a custom interpolant (GLTFCubicSplineInterpolation) for CUBICSPLINE tracks. Each\n  // keyframe track will be initialized with a default interpolation type, then modified.\n  LINEAR: InterpolateLinear,\n  STEP: InterpolateDiscrete\n};\nconst ALPHA_MODES = {\n  OPAQUE: 'OPAQUE',\n  MASK: 'MASK',\n  BLEND: 'BLEND'\n};\n\n/**\n * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#default-material\n *\n * @private\n * @param {Object<string, Material>} cache\n * @return {Material}\n */\nfunction createDefaultMaterial(cache) {\n  if (cache['DefaultMaterial'] === undefined) {\n    cache['DefaultMaterial'] = new MeshStandardMaterial({\n      color: 0xFFFFFF,\n      emissive: 0x000000,\n      metalness: 1,\n      roughness: 1,\n      transparent: false,\n      depthTest: true,\n      side: FrontSide\n    });\n  }\n  return cache['DefaultMaterial'];\n}\nfunction addUnknownExtensionsToUserData(knownExtensions, object, objectDef) {\n  // Add unknown glTF extensions to an object's userData.\n\n  for (const name in objectDef.extensions) {\n    if (knownExtensions[name] === undefined) {\n      object.userData.gltfExtensions = object.userData.gltfExtensions || {};\n      object.userData.gltfExtensions[name] = objectDef.extensions[name];\n    }\n  }\n}\n\n/**\n *\n * @private\n * @param {Object3D|Material|BufferGeometry|Object} object\n * @param {GLTF.definition} gltfDef\n */\nfunction assignExtrasToUserData(object, gltfDef) {\n  if (gltfDef.extras !== undefined) {\n    if (typeof gltfDef.extras === 'object') {\n      Object.assign(object.userData, gltfDef.extras);\n    } else {\n      console.warn('THREE.GLTFLoader: Ignoring primitive type .extras, ' + gltfDef.extras);\n    }\n  }\n}\n\n/**\n * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#morph-targets\n *\n * @private\n * @param {BufferGeometry} geometry\n * @param {Array<GLTF.Target>} targets\n * @param {GLTFParser} parser\n * @return {Promise<BufferGeometry>}\n */\nfunction addMorphTargets(geometry, targets, parser) {\n  let hasMorphPosition = false;\n  let hasMorphNormal = false;\n  let hasMorphColor = false;\n  for (let i = 0, il = targets.length; i < il; i++) {\n    const target = targets[i];\n    if (target.POSITION !== undefined) hasMorphPosition = true;\n    if (target.NORMAL !== undefined) hasMorphNormal = true;\n    if (target.COLOR_0 !== undefined) hasMorphColor = true;\n    if (hasMorphPosition && hasMorphNormal && hasMorphColor) break;\n  }\n  if (!hasMorphPosition && !hasMorphNormal && !hasMorphColor) return Promise.resolve(geometry);\n  const pendingPositionAccessors = [];\n  const pendingNormalAccessors = [];\n  const pendingColorAccessors = [];\n  for (let i = 0, il = targets.length; i < il; i++) {\n    const target = targets[i];\n    if (hasMorphPosition) {\n      const pendingAccessor = target.POSITION !== undefined ? parser.getDependency('accessor', target.POSITION) : geometry.attributes.position;\n      pendingPositionAccessors.push(pendingAccessor);\n    }\n    if (hasMorphNormal) {\n      const pendingAccessor = target.NORMAL !== undefined ? parser.getDependency('accessor', target.NORMAL) : geometry.attributes.normal;\n      pendingNormalAccessors.push(pendingAccessor);\n    }\n    if (hasMorphColor) {\n      const pendingAccessor = target.COLOR_0 !== undefined ? parser.getDependency('accessor', target.COLOR_0) : geometry.attributes.color;\n      pendingColorAccessors.push(pendingAccessor);\n    }\n  }\n  return Promise.all([Promise.all(pendingPositionAccessors), Promise.all(pendingNormalAccessors), Promise.all(pendingColorAccessors)]).then(function (accessors) {\n    const morphPositions = accessors[0];\n    const morphNormals = accessors[1];\n    const morphColors = accessors[2];\n    if (hasMorphPosition) geometry.morphAttributes.position = morphPositions;\n    if (hasMorphNormal) geometry.morphAttributes.normal = morphNormals;\n    if (hasMorphColor) geometry.morphAttributes.color = morphColors;\n    geometry.morphTargetsRelative = true;\n    return geometry;\n  });\n}\n\n/**\n *\n * @private\n * @param {Mesh} mesh\n * @param {GLTF.Mesh} meshDef\n */\nfunction updateMorphTargets(mesh, meshDef) {\n  mesh.updateMorphTargets();\n  if (meshDef.weights !== undefined) {\n    for (let i = 0, il = meshDef.weights.length; i < il; i++) {\n      mesh.morphTargetInfluences[i] = meshDef.weights[i];\n    }\n  }\n\n  // .extras has user-defined data, so check that .extras.targetNames is an array.\n  if (meshDef.extras && Array.isArray(meshDef.extras.targetNames)) {\n    const targetNames = meshDef.extras.targetNames;\n    if (mesh.morphTargetInfluences.length === targetNames.length) {\n      mesh.morphTargetDictionary = {};\n      for (let i = 0, il = targetNames.length; i < il; i++) {\n        mesh.morphTargetDictionary[targetNames[i]] = i;\n      }\n    } else {\n      console.warn('THREE.GLTFLoader: Invalid extras.targetNames length. Ignoring names.');\n    }\n  }\n}\nfunction createPrimitiveKey(primitiveDef) {\n  let geometryKey;\n  const dracoExtension = primitiveDef.extensions && primitiveDef.extensions[EXTENSIONS.KHR_DRACO_MESH_COMPRESSION];\n  if (dracoExtension) {\n    geometryKey = 'draco:' + dracoExtension.bufferView + ':' + dracoExtension.indices + ':' + createAttributesKey(dracoExtension.attributes);\n  } else {\n    geometryKey = primitiveDef.indices + ':' + createAttributesKey(primitiveDef.attributes) + ':' + primitiveDef.mode;\n  }\n  if (primitiveDef.targets !== undefined) {\n    for (let i = 0, il = primitiveDef.targets.length; i < il; i++) {\n      geometryKey += ':' + createAttributesKey(primitiveDef.targets[i]);\n    }\n  }\n  return geometryKey;\n}\nfunction createAttributesKey(attributes) {\n  let attributesKey = '';\n  const keys = Object.keys(attributes).sort();\n  for (let i = 0, il = keys.length; i < il; i++) {\n    attributesKey += keys[i] + ':' + attributes[keys[i]] + ';';\n  }\n  return attributesKey;\n}\nfunction getNormalizedComponentScale(constructor) {\n  // Reference:\n  // https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_mesh_quantization#encoding-quantized-data\n\n  switch (constructor) {\n    case Int8Array:\n      return 1 / 127;\n    case Uint8Array:\n      return 1 / 255;\n    case Int16Array:\n      return 1 / 32767;\n    case Uint16Array:\n      return 1 / 65535;\n    default:\n      throw new Error('THREE.GLTFLoader: Unsupported normalized accessor component type.');\n  }\n}\nfunction getImageURIMimeType(uri) {\n  if (uri.search(/\\.jpe?g($|\\?)/i) > 0 || uri.search(/^data\\:image\\/jpeg/) === 0) return 'image/jpeg';\n  if (uri.search(/\\.webp($|\\?)/i) > 0 || uri.search(/^data\\:image\\/webp/) === 0) return 'image/webp';\n  if (uri.search(/\\.ktx2($|\\?)/i) > 0 || uri.search(/^data\\:image\\/ktx2/) === 0) return 'image/ktx2';\n  return 'image/png';\n}\nconst _identityMatrix = new Matrix4();\n\n/* GLTF PARSER */\n\nclass GLTFParser {\n  constructor(json = {}, options = {}) {\n    this.json = json;\n    this.extensions = {};\n    this.plugins = {};\n    this.options = options;\n\n    // loader object cache\n    this.cache = new GLTFRegistry();\n\n    // associations between Three.js objects and glTF elements\n    this.associations = new Map();\n\n    // BufferGeometry caching\n    this.primitiveCache = {};\n\n    // Node cache\n    this.nodeCache = {};\n\n    // Object3D instance caches\n    this.meshCache = {\n      refs: {},\n      uses: {}\n    };\n    this.cameraCache = {\n      refs: {},\n      uses: {}\n    };\n    this.lightCache = {\n      refs: {},\n      uses: {}\n    };\n    this.sourceCache = {};\n    this.textureCache = {};\n\n    // Track node names, to ensure no duplicates\n    this.nodeNamesUsed = {};\n\n    // Use an ImageBitmapLoader if imageBitmaps are supported. Moves much of the\n    // expensive work of uploading a texture to the GPU off the main thread.\n\n    let isSafari = false;\n    let safariVersion = -1;\n    let isFirefox = false;\n    let firefoxVersion = -1;\n    if (typeof navigator !== 'undefined') {\n      const userAgent = navigator.userAgent;\n      isSafari = /^((?!chrome|android).)*safari/i.test(userAgent) === true;\n      const safariMatch = userAgent.match(/Version\\/(\\d+)/);\n      safariVersion = isSafari && safariMatch ? parseInt(safariMatch[1], 10) : -1;\n      isFirefox = userAgent.indexOf('Firefox') > -1;\n      firefoxVersion = isFirefox ? userAgent.match(/Firefox\\/([0-9]+)\\./)[1] : -1;\n    }\n    if (typeof createImageBitmap === 'undefined' || isSafari && safariVersion < 17 || isFirefox && firefoxVersion < 98) {\n      this.textureLoader = new TextureLoader(this.options.manager);\n    } else {\n      this.textureLoader = new ImageBitmapLoader(this.options.manager);\n    }\n    this.textureLoader.setCrossOrigin(this.options.crossOrigin);\n    this.textureLoader.setRequestHeader(this.options.requestHeader);\n    this.fileLoader = new FileLoader(this.options.manager);\n    this.fileLoader.setResponseType('arraybuffer');\n    if (this.options.crossOrigin === 'use-credentials') {\n      this.fileLoader.setWithCredentials(true);\n    }\n  }\n  setExtensions(extensions) {\n    this.extensions = extensions;\n  }\n  setPlugins(plugins) {\n    this.plugins = plugins;\n  }\n  parse(onLoad, onError) {\n    const parser = this;\n    const json = this.json;\n    const extensions = this.extensions;\n\n    // Clear the loader cache\n    this.cache.removeAll();\n    this.nodeCache = {};\n\n    // Mark the special nodes/meshes in json for efficient parse\n    this._invokeAll(function (ext) {\n      return ext._markDefs && ext._markDefs();\n    });\n    Promise.all(this._invokeAll(function (ext) {\n      return ext.beforeRoot && ext.beforeRoot();\n    })).then(function () {\n      return Promise.all([parser.getDependencies('scene'), parser.getDependencies('animation'), parser.getDependencies('camera')]);\n    }).then(function (dependencies) {\n      const result = {\n        scene: dependencies[0][json.scene || 0],\n        scenes: dependencies[0],\n        animations: dependencies[1],\n        cameras: dependencies[2],\n        asset: json.asset,\n        parser: parser,\n        userData: {}\n      };\n      addUnknownExtensionsToUserData(extensions, result, json);\n      assignExtrasToUserData(result, json);\n      return Promise.all(parser._invokeAll(function (ext) {\n        return ext.afterRoot && ext.afterRoot(result);\n      })).then(function () {\n        for (const scene of result.scenes) {\n          scene.updateMatrixWorld();\n        }\n        onLoad(result);\n      });\n    }).catch(onError);\n  }\n\n  /**\n   * Marks the special nodes/meshes in json for efficient parse.\n   *\n   * @private\n   */\n  _markDefs() {\n    const nodeDefs = this.json.nodes || [];\n    const skinDefs = this.json.skins || [];\n    const meshDefs = this.json.meshes || [];\n\n    // Nothing in the node definition indicates whether it is a Bone or an\n    // Object3D. Use the skins' joint references to mark bones.\n    for (let skinIndex = 0, skinLength = skinDefs.length; skinIndex < skinLength; skinIndex++) {\n      const joints = skinDefs[skinIndex].joints;\n      for (let i = 0, il = joints.length; i < il; i++) {\n        nodeDefs[joints[i]].isBone = true;\n      }\n    }\n\n    // Iterate over all nodes, marking references to shared resources,\n    // as well as skeleton joints.\n    for (let nodeIndex = 0, nodeLength = nodeDefs.length; nodeIndex < nodeLength; nodeIndex++) {\n      const nodeDef = nodeDefs[nodeIndex];\n      if (nodeDef.mesh !== undefined) {\n        this._addNodeRef(this.meshCache, nodeDef.mesh);\n\n        // Nothing in the mesh definition indicates whether it is\n        // a SkinnedMesh or Mesh. Use the node's mesh reference\n        // to mark SkinnedMesh if node has skin.\n        if (nodeDef.skin !== undefined) {\n          meshDefs[nodeDef.mesh].isSkinnedMesh = true;\n        }\n      }\n      if (nodeDef.camera !== undefined) {\n        this._addNodeRef(this.cameraCache, nodeDef.camera);\n      }\n    }\n  }\n\n  /**\n   * Counts references to shared node / Object3D resources. These resources\n   * can be reused, or \"instantiated\", at multiple nodes in the scene\n   * hierarchy. Mesh, Camera, and Light instances are instantiated and must\n   * be marked. Non-scenegraph resources (like Materials, Geometries, and\n   * Textures) can be reused directly and are not marked here.\n   *\n   * Example: CesiumMilkTruck sample model reuses \"Wheel\" meshes.\n   *\n   * @private\n   * @param {Object} cache\n   * @param {Object3D} index\n   */\n  _addNodeRef(cache, index) {\n    if (index === undefined) return;\n    if (cache.refs[index] === undefined) {\n      cache.refs[index] = cache.uses[index] = 0;\n    }\n    cache.refs[index]++;\n  }\n\n  /**\n   * Returns a reference to a shared resource, cloning it if necessary.\n   *\n   * @private\n   * @param {Object} cache\n   * @param {number} index\n   * @param {Object} object\n   * @return {Object}\n   */\n  _getNodeRef(cache, index, object) {\n    if (cache.refs[index] <= 1) return object;\n    const ref = object.clone();\n\n    // Propagates mappings to the cloned object, prevents mappings on the\n    // original object from being lost.\n    const updateMappings = (original, clone) => {\n      const mappings = this.associations.get(original);\n      if (mappings != null) {\n        this.associations.set(clone, mappings);\n      }\n      for (const [i, child] of original.children.entries()) {\n        updateMappings(child, clone.children[i]);\n      }\n    };\n    updateMappings(object, ref);\n    ref.name += '_instance_' + cache.uses[index]++;\n    return ref;\n  }\n  _invokeOne(func) {\n    const extensions = Object.values(this.plugins);\n    extensions.push(this);\n    for (let i = 0; i < extensions.length; i++) {\n      const result = func(extensions[i]);\n      if (result) return result;\n    }\n    return null;\n  }\n  _invokeAll(func) {\n    const extensions = Object.values(this.plugins);\n    extensions.unshift(this);\n    const pending = [];\n    for (let i = 0; i < extensions.length; i++) {\n      const result = func(extensions[i]);\n      if (result) pending.push(result);\n    }\n    return pending;\n  }\n\n  /**\n   * Requests the specified dependency asynchronously, with caching.\n   *\n   * @private\n   * @param {string} type\n   * @param {number} index\n   * @return {Promise<Object3D|Material|THREE.Texture|AnimationClip|ArrayBuffer|Object>}\n   */\n  getDependency(type, index) {\n    const cacheKey = type + ':' + index;\n    let dependency = this.cache.get(cacheKey);\n    if (!dependency) {\n      switch (type) {\n        case 'scene':\n          dependency = this.loadScene(index);\n          break;\n        case 'node':\n          dependency = this._invokeOne(function (ext) {\n            return ext.loadNode && ext.loadNode(index);\n          });\n          break;\n        case 'mesh':\n          dependency = this._invokeOne(function (ext) {\n            return ext.loadMesh && ext.loadMesh(index);\n          });\n          break;\n        case 'accessor':\n          dependency = this.loadAccessor(index);\n          break;\n        case 'bufferView':\n          dependency = this._invokeOne(function (ext) {\n            return ext.loadBufferView && ext.loadBufferView(index);\n          });\n          break;\n        case 'buffer':\n          dependency = this.loadBuffer(index);\n          break;\n        case 'material':\n          dependency = this._invokeOne(function (ext) {\n            return ext.loadMaterial && ext.loadMaterial(index);\n          });\n          break;\n        case 'texture':\n          dependency = this._invokeOne(function (ext) {\n            return ext.loadTexture && ext.loadTexture(index);\n          });\n          break;\n        case 'skin':\n          dependency = this.loadSkin(index);\n          break;\n        case 'animation':\n          dependency = this._invokeOne(function (ext) {\n            return ext.loadAnimation && ext.loadAnimation(index);\n          });\n          break;\n        case 'camera':\n          dependency = this.loadCamera(index);\n          break;\n        default:\n          dependency = this._invokeOne(function (ext) {\n            return ext != this && ext.getDependency && ext.getDependency(type, index);\n          });\n          if (!dependency) {\n            throw new Error('Unknown type: ' + type);\n          }\n          break;\n      }\n      this.cache.add(cacheKey, dependency);\n    }\n    return dependency;\n  }\n\n  /**\n   * Requests all dependencies of the specified type asynchronously, with caching.\n   *\n   * @private\n   * @param {string} type\n   * @return {Promise<Array<Object>>}\n   */\n  getDependencies(type) {\n    let dependencies = this.cache.get(type);\n    if (!dependencies) {\n      const parser = this;\n      const defs = this.json[type + (type === 'mesh' ? 'es' : 's')] || [];\n      dependencies = Promise.all(defs.map(function (def, index) {\n        return parser.getDependency(type, index);\n      }));\n      this.cache.add(type, dependencies);\n    }\n    return dependencies;\n  }\n\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#buffers-and-buffer-views\n   *\n   * @private\n   * @param {number} bufferIndex\n   * @return {Promise<ArrayBuffer>}\n   */\n  loadBuffer(bufferIndex) {\n    const bufferDef = this.json.buffers[bufferIndex];\n    const loader = this.fileLoader;\n    if (bufferDef.type && bufferDef.type !== 'arraybuffer') {\n      throw new Error('THREE.GLTFLoader: ' + bufferDef.type + ' buffer type is not supported.');\n    }\n\n    // If present, GLB container is required to be the first buffer.\n    if (bufferDef.uri === undefined && bufferIndex === 0) {\n      return Promise.resolve(this.extensions[EXTENSIONS.KHR_BINARY_GLTF].body);\n    }\n    const options = this.options;\n    return new Promise(function (resolve, reject) {\n      loader.load(LoaderUtils.resolveURL(bufferDef.uri, options.path), resolve, undefined, function () {\n        reject(new Error('THREE.GLTFLoader: Failed to load buffer \"' + bufferDef.uri + '\".'));\n      });\n    });\n  }\n\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#buffers-and-buffer-views\n   *\n   * @private\n   * @param {number} bufferViewIndex\n   * @return {Promise<ArrayBuffer>}\n   */\n  loadBufferView(bufferViewIndex) {\n    const bufferViewDef = this.json.bufferViews[bufferViewIndex];\n    return this.getDependency('buffer', bufferViewDef.buffer).then(function (buffer) {\n      const byteLength = bufferViewDef.byteLength || 0;\n      const byteOffset = bufferViewDef.byteOffset || 0;\n      return buffer.slice(byteOffset, byteOffset + byteLength);\n    });\n  }\n\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#accessors\n   *\n   * @private\n   * @param {number} accessorIndex\n   * @return {Promise<BufferAttribute|InterleavedBufferAttribute>}\n   */\n  loadAccessor(accessorIndex) {\n    const parser = this;\n    const json = this.json;\n    const accessorDef = this.json.accessors[accessorIndex];\n    if (accessorDef.bufferView === undefined && accessorDef.sparse === undefined) {\n      const itemSize = WEBGL_TYPE_SIZES[accessorDef.type];\n      const TypedArray = WEBGL_COMPONENT_TYPES[accessorDef.componentType];\n      const normalized = accessorDef.normalized === true;\n      const array = new TypedArray(accessorDef.count * itemSize);\n      return Promise.resolve(new BufferAttribute(array, itemSize, normalized));\n    }\n    const pendingBufferViews = [];\n    if (accessorDef.bufferView !== undefined) {\n      pendingBufferViews.push(this.getDependency('bufferView', accessorDef.bufferView));\n    } else {\n      pendingBufferViews.push(null);\n    }\n    if (accessorDef.sparse !== undefined) {\n      pendingBufferViews.push(this.getDependency('bufferView', accessorDef.sparse.indices.bufferView));\n      pendingBufferViews.push(this.getDependency('bufferView', accessorDef.sparse.values.bufferView));\n    }\n    return Promise.all(pendingBufferViews).then(function (bufferViews) {\n      const bufferView = bufferViews[0];\n      const itemSize = WEBGL_TYPE_SIZES[accessorDef.type];\n      const TypedArray = WEBGL_COMPONENT_TYPES[accessorDef.componentType];\n\n      // For VEC3: itemSize is 3, elementBytes is 4, itemBytes is 12.\n      const elementBytes = TypedArray.BYTES_PER_ELEMENT;\n      const itemBytes = elementBytes * itemSize;\n      const byteOffset = accessorDef.byteOffset || 0;\n      const byteStride = accessorDef.bufferView !== undefined ? json.bufferViews[accessorDef.bufferView].byteStride : undefined;\n      const normalized = accessorDef.normalized === true;\n      let array, bufferAttribute;\n\n      // The buffer is not interleaved if the stride is the item size in bytes.\n      if (byteStride && byteStride !== itemBytes) {\n        // Each \"slice\" of the buffer, as defined by 'count' elements of 'byteStride' bytes, gets its own InterleavedBuffer\n        // This makes sure that IBA.count reflects accessor.count properly\n        const ibSlice = Math.floor(byteOffset / byteStride);\n        const ibCacheKey = 'InterleavedBuffer:' + accessorDef.bufferView + ':' + accessorDef.componentType + ':' + ibSlice + ':' + accessorDef.count;\n        let ib = parser.cache.get(ibCacheKey);\n        if (!ib) {\n          array = new TypedArray(bufferView, ibSlice * byteStride, accessorDef.count * byteStride / elementBytes);\n\n          // Integer parameters to IB/IBA are in array elements, not bytes.\n          ib = new InterleavedBuffer(array, byteStride / elementBytes);\n          parser.cache.add(ibCacheKey, ib);\n        }\n        bufferAttribute = new InterleavedBufferAttribute(ib, itemSize, byteOffset % byteStride / elementBytes, normalized);\n      } else {\n        if (bufferView === null) {\n          array = new TypedArray(accessorDef.count * itemSize);\n        } else {\n          array = new TypedArray(bufferView, byteOffset, accessorDef.count * itemSize);\n        }\n        bufferAttribute = new BufferAttribute(array, itemSize, normalized);\n      }\n\n      // https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#sparse-accessors\n      if (accessorDef.sparse !== undefined) {\n        const itemSizeIndices = WEBGL_TYPE_SIZES.SCALAR;\n        const TypedArrayIndices = WEBGL_COMPONENT_TYPES[accessorDef.sparse.indices.componentType];\n        const byteOffsetIndices = accessorDef.sparse.indices.byteOffset || 0;\n        const byteOffsetValues = accessorDef.sparse.values.byteOffset || 0;\n        const sparseIndices = new TypedArrayIndices(bufferViews[1], byteOffsetIndices, accessorDef.sparse.count * itemSizeIndices);\n        const sparseValues = new TypedArray(bufferViews[2], byteOffsetValues, accessorDef.sparse.count * itemSize);\n        if (bufferView !== null) {\n          // Avoid modifying the original ArrayBuffer, if the bufferView wasn't initialized with zeroes.\n          bufferAttribute = new BufferAttribute(bufferAttribute.array.slice(), bufferAttribute.itemSize, bufferAttribute.normalized);\n        }\n\n        // Ignore normalized since we copy from sparse\n        bufferAttribute.normalized = false;\n        for (let i = 0, il = sparseIndices.length; i < il; i++) {\n          const index = sparseIndices[i];\n          bufferAttribute.setX(index, sparseValues[i * itemSize]);\n          if (itemSize >= 2) bufferAttribute.setY(index, sparseValues[i * itemSize + 1]);\n          if (itemSize >= 3) bufferAttribute.setZ(index, sparseValues[i * itemSize + 2]);\n          if (itemSize >= 4) bufferAttribute.setW(index, sparseValues[i * itemSize + 3]);\n          if (itemSize >= 5) throw new Error('THREE.GLTFLoader: Unsupported itemSize in sparse BufferAttribute.');\n        }\n        bufferAttribute.normalized = normalized;\n      }\n      return bufferAttribute;\n    });\n  }\n\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#textures\n   *\n   * @private\n   * @param {number} textureIndex\n   * @return {Promise<THREE.Texture|null>}\n   */\n  loadTexture(textureIndex) {\n    const json = this.json;\n    const options = this.options;\n    const textureDef = json.textures[textureIndex];\n    const sourceIndex = textureDef.source;\n    const sourceDef = json.images[sourceIndex];\n    let loader = this.textureLoader;\n    if (sourceDef.uri) {\n      const handler = options.manager.getHandler(sourceDef.uri);\n      if (handler !== null) loader = handler;\n    }\n    return this.loadTextureImage(textureIndex, sourceIndex, loader);\n  }\n  loadTextureImage(textureIndex, sourceIndex, loader) {\n    const parser = this;\n    const json = this.json;\n    const textureDef = json.textures[textureIndex];\n    const sourceDef = json.images[sourceIndex];\n    const cacheKey = (sourceDef.uri || sourceDef.bufferView) + ':' + textureDef.sampler;\n    if (this.textureCache[cacheKey]) {\n      // See https://github.com/mrdoob/three.js/issues/21559.\n      return this.textureCache[cacheKey];\n    }\n    const promise = this.loadImageSource(sourceIndex, loader).then(function (texture) {\n      texture.flipY = false;\n      texture.name = textureDef.name || sourceDef.name || '';\n      if (texture.name === '' && typeof sourceDef.uri === 'string' && sourceDef.uri.startsWith('data:image/') === false) {\n        texture.name = sourceDef.uri;\n      }\n      const samplers = json.samplers || {};\n      const sampler = samplers[textureDef.sampler] || {};\n      texture.magFilter = WEBGL_FILTERS[sampler.magFilter] || LinearFilter;\n      texture.minFilter = WEBGL_FILTERS[sampler.minFilter] || LinearMipmapLinearFilter;\n      texture.wrapS = WEBGL_WRAPPINGS[sampler.wrapS] || RepeatWrapping;\n      texture.wrapT = WEBGL_WRAPPINGS[sampler.wrapT] || RepeatWrapping;\n      texture.generateMipmaps = !texture.isCompressedTexture && texture.minFilter !== NearestFilter && texture.minFilter !== LinearFilter;\n      parser.associations.set(texture, {\n        textures: textureIndex\n      });\n      return texture;\n    }).catch(function () {\n      return null;\n    });\n    this.textureCache[cacheKey] = promise;\n    return promise;\n  }\n  loadImageSource(sourceIndex, loader) {\n    const parser = this;\n    const json = this.json;\n    const options = this.options;\n    if (this.sourceCache[sourceIndex] !== undefined) {\n      return this.sourceCache[sourceIndex].then(texture => texture.clone());\n    }\n    const sourceDef = json.images[sourceIndex];\n    const URL = self.URL || self.webkitURL;\n    let sourceURI = sourceDef.uri || '';\n    let isObjectURL = false;\n    if (sourceDef.bufferView !== undefined) {\n      // Load binary image data from bufferView, if provided.\n\n      sourceURI = parser.getDependency('bufferView', sourceDef.bufferView).then(function (bufferView) {\n        isObjectURL = true;\n        const blob = new Blob([bufferView], {\n          type: sourceDef.mimeType\n        });\n        sourceURI = URL.createObjectURL(blob);\n        return sourceURI;\n      });\n    } else if (sourceDef.uri === undefined) {\n      throw new Error('THREE.GLTFLoader: Image ' + sourceIndex + ' is missing URI and bufferView');\n    }\n    const promise = Promise.resolve(sourceURI).then(function (sourceURI) {\n      return new Promise(function (resolve, reject) {\n        let onLoad = resolve;\n        if (loader.isImageBitmapLoader === true) {\n          onLoad = function (imageBitmap) {\n            const texture = new Texture(imageBitmap);\n            texture.needsUpdate = true;\n            resolve(texture);\n          };\n        }\n        loader.load(LoaderUtils.resolveURL(sourceURI, options.path), onLoad, undefined, reject);\n      });\n    }).then(function (texture) {\n      // Clean up resources and configure Texture.\n\n      if (isObjectURL === true) {\n        URL.revokeObjectURL(sourceURI);\n      }\n      assignExtrasToUserData(texture, sourceDef);\n      texture.userData.mimeType = sourceDef.mimeType || getImageURIMimeType(sourceDef.uri);\n      return texture;\n    }).catch(function (error) {\n      console.error('THREE.GLTFLoader: Couldn\\'t load texture', sourceURI);\n      throw error;\n    });\n    this.sourceCache[sourceIndex] = promise;\n    return promise;\n  }\n\n  /**\n   * Asynchronously assigns a texture to the given material parameters.\n   *\n   * @private\n   * @param {Object} materialParams\n   * @param {string} mapName\n   * @param {Object} mapDef\n   * @param {string} [colorSpace]\n   * @return {Promise<Texture>}\n   */\n  assignTexture(materialParams, mapName, mapDef, colorSpace) {\n    const parser = this;\n    return this.getDependency('texture', mapDef.index).then(function (texture) {\n      if (!texture) return null;\n      if (mapDef.texCoord !== undefined && mapDef.texCoord > 0) {\n        texture = texture.clone();\n        texture.channel = mapDef.texCoord;\n      }\n      if (parser.extensions[EXTENSIONS.KHR_TEXTURE_TRANSFORM]) {\n        const transform = mapDef.extensions !== undefined ? mapDef.extensions[EXTENSIONS.KHR_TEXTURE_TRANSFORM] : undefined;\n        if (transform) {\n          const gltfReference = parser.associations.get(texture);\n          texture = parser.extensions[EXTENSIONS.KHR_TEXTURE_TRANSFORM].extendTexture(texture, transform);\n          parser.associations.set(texture, gltfReference);\n        }\n      }\n      if (colorSpace !== undefined) {\n        texture.colorSpace = colorSpace;\n      }\n      materialParams[mapName] = texture;\n      return texture;\n    });\n  }\n\n  /**\n   * Assigns final material to a Mesh, Line, or Points instance. The instance\n   * already has a material (generated from the glTF material options alone)\n   * but reuse of the same glTF material may require multiple threejs materials\n   * to accommodate different primitive types, defines, etc. New materials will\n   * be created if necessary, and reused from a cache.\n   *\n   * @private\n   * @param {Object3D} mesh Mesh, Line, or Points instance.\n   */\n  assignFinalMaterial(mesh) {\n    const geometry = mesh.geometry;\n    let material = mesh.material;\n    const useDerivativeTangents = geometry.attributes.tangent === undefined;\n    const useVertexColors = geometry.attributes.color !== undefined;\n    const useFlatShading = geometry.attributes.normal === undefined;\n    if (mesh.isPoints) {\n      const cacheKey = 'PointsMaterial:' + material.uuid;\n      let pointsMaterial = this.cache.get(cacheKey);\n      if (!pointsMaterial) {\n        pointsMaterial = new PointsMaterial();\n        Material.prototype.copy.call(pointsMaterial, material);\n        pointsMaterial.color.copy(material.color);\n        pointsMaterial.map = material.map;\n        pointsMaterial.sizeAttenuation = false; // glTF spec says points should be 1px\n\n        this.cache.add(cacheKey, pointsMaterial);\n      }\n      material = pointsMaterial;\n    } else if (mesh.isLine) {\n      const cacheKey = 'LineBasicMaterial:' + material.uuid;\n      let lineMaterial = this.cache.get(cacheKey);\n      if (!lineMaterial) {\n        lineMaterial = new LineBasicMaterial();\n        Material.prototype.copy.call(lineMaterial, material);\n        lineMaterial.color.copy(material.color);\n        lineMaterial.map = material.map;\n        this.cache.add(cacheKey, lineMaterial);\n      }\n      material = lineMaterial;\n    }\n\n    // Clone the material if it will be modified\n    if (useDerivativeTangents || useVertexColors || useFlatShading) {\n      let cacheKey = 'ClonedMaterial:' + material.uuid + ':';\n      if (useDerivativeTangents) cacheKey += 'derivative-tangents:';\n      if (useVertexColors) cacheKey += 'vertex-colors:';\n      if (useFlatShading) cacheKey += 'flat-shading:';\n      let cachedMaterial = this.cache.get(cacheKey);\n      if (!cachedMaterial) {\n        cachedMaterial = material.clone();\n        if (useVertexColors) cachedMaterial.vertexColors = true;\n        if (useFlatShading) cachedMaterial.flatShading = true;\n        if (useDerivativeTangents) {\n          // https://github.com/mrdoob/three.js/issues/11438#issuecomment-507003995\n          if (cachedMaterial.normalScale) cachedMaterial.normalScale.y *= -1;\n          if (cachedMaterial.clearcoatNormalScale) cachedMaterial.clearcoatNormalScale.y *= -1;\n        }\n        this.cache.add(cacheKey, cachedMaterial);\n        this.associations.set(cachedMaterial, this.associations.get(material));\n      }\n      material = cachedMaterial;\n    }\n    mesh.material = material;\n  }\n  getMaterialType(/* materialIndex */\n  ) {\n    return MeshStandardMaterial;\n  }\n\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#materials\n   *\n   * @private\n   * @param {number} materialIndex\n   * @return {Promise<Material>}\n   */\n  loadMaterial(materialIndex) {\n    const parser = this;\n    const json = this.json;\n    const extensions = this.extensions;\n    const materialDef = json.materials[materialIndex];\n    let materialType;\n    const materialParams = {};\n    const materialExtensions = materialDef.extensions || {};\n    const pending = [];\n    if (materialExtensions[EXTENSIONS.KHR_MATERIALS_UNLIT]) {\n      const kmuExtension = extensions[EXTENSIONS.KHR_MATERIALS_UNLIT];\n      materialType = kmuExtension.getMaterialType();\n      pending.push(kmuExtension.extendParams(materialParams, materialDef, parser));\n    } else {\n      // Specification:\n      // https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#metallic-roughness-material\n\n      const metallicRoughness = materialDef.pbrMetallicRoughness || {};\n      materialParams.color = new Color(1.0, 1.0, 1.0);\n      materialParams.opacity = 1.0;\n      if (Array.isArray(metallicRoughness.baseColorFactor)) {\n        const array = metallicRoughness.baseColorFactor;\n        materialParams.color.setRGB(array[0], array[1], array[2], LinearSRGBColorSpace);\n        materialParams.opacity = array[3];\n      }\n      if (metallicRoughness.baseColorTexture !== undefined) {\n        pending.push(parser.assignTexture(materialParams, 'map', metallicRoughness.baseColorTexture, SRGBColorSpace));\n      }\n      materialParams.metalness = metallicRoughness.metallicFactor !== undefined ? metallicRoughness.metallicFactor : 1.0;\n      materialParams.roughness = metallicRoughness.roughnessFactor !== undefined ? metallicRoughness.roughnessFactor : 1.0;\n      if (metallicRoughness.metallicRoughnessTexture !== undefined) {\n        pending.push(parser.assignTexture(materialParams, 'metalnessMap', metallicRoughness.metallicRoughnessTexture));\n        pending.push(parser.assignTexture(materialParams, 'roughnessMap', metallicRoughness.metallicRoughnessTexture));\n      }\n      materialType = this._invokeOne(function (ext) {\n        return ext.getMaterialType && ext.getMaterialType(materialIndex);\n      });\n      pending.push(Promise.all(this._invokeAll(function (ext) {\n        return ext.extendMaterialParams && ext.extendMaterialParams(materialIndex, materialParams);\n      })));\n    }\n    if (materialDef.doubleSided === true) {\n      materialParams.side = DoubleSide;\n    }\n    const alphaMode = materialDef.alphaMode || ALPHA_MODES.OPAQUE;\n    if (alphaMode === ALPHA_MODES.BLEND) {\n      materialParams.transparent = true;\n\n      // See: https://github.com/mrdoob/three.js/issues/17706\n      materialParams.depthWrite = false;\n    } else {\n      materialParams.transparent = false;\n      if (alphaMode === ALPHA_MODES.MASK) {\n        materialParams.alphaTest = materialDef.alphaCutoff !== undefined ? materialDef.alphaCutoff : 0.5;\n      }\n    }\n    if (materialDef.normalTexture !== undefined && materialType !== MeshBasicMaterial) {\n      pending.push(parser.assignTexture(materialParams, 'normalMap', materialDef.normalTexture));\n      materialParams.normalScale = new Vector2(1, 1);\n      if (materialDef.normalTexture.scale !== undefined) {\n        const scale = materialDef.normalTexture.scale;\n        materialParams.normalScale.set(scale, scale);\n      }\n    }\n    if (materialDef.occlusionTexture !== undefined && materialType !== MeshBasicMaterial) {\n      pending.push(parser.assignTexture(materialParams, 'aoMap', materialDef.occlusionTexture));\n      if (materialDef.occlusionTexture.strength !== undefined) {\n        materialParams.aoMapIntensity = materialDef.occlusionTexture.strength;\n      }\n    }\n    if (materialDef.emissiveFactor !== undefined && materialType !== MeshBasicMaterial) {\n      const emissiveFactor = materialDef.emissiveFactor;\n      materialParams.emissive = new Color().setRGB(emissiveFactor[0], emissiveFactor[1], emissiveFactor[2], LinearSRGBColorSpace);\n    }\n    if (materialDef.emissiveTexture !== undefined && materialType !== MeshBasicMaterial) {\n      pending.push(parser.assignTexture(materialParams, 'emissiveMap', materialDef.emissiveTexture, SRGBColorSpace));\n    }\n    return Promise.all(pending).then(function () {\n      const material = new materialType(materialParams);\n      if (materialDef.name) material.name = materialDef.name;\n      assignExtrasToUserData(material, materialDef);\n      parser.associations.set(material, {\n        materials: materialIndex\n      });\n      if (materialDef.extensions) addUnknownExtensionsToUserData(extensions, material, materialDef);\n      return material;\n    });\n  }\n\n  /**\n   * When Object3D instances are targeted by animation, they need unique names.\n   *\n   * @private\n   * @param {string} originalName\n   * @return {string}\n   */\n  createUniqueName(originalName) {\n    const sanitizedName = PropertyBinding.sanitizeNodeName(originalName || '');\n    if (sanitizedName in this.nodeNamesUsed) {\n      return sanitizedName + '_' + ++this.nodeNamesUsed[sanitizedName];\n    } else {\n      this.nodeNamesUsed[sanitizedName] = 0;\n      return sanitizedName;\n    }\n  }\n\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#geometry\n   *\n   * Creates BufferGeometries from primitives.\n   *\n   * @private\n   * @param {Array<GLTF.Primitive>} primitives\n   * @return {Promise<Array<BufferGeometry>>}\n   */\n  loadGeometries(primitives) {\n    const parser = this;\n    const extensions = this.extensions;\n    const cache = this.primitiveCache;\n    function createDracoPrimitive(primitive) {\n      return extensions[EXTENSIONS.KHR_DRACO_MESH_COMPRESSION].decodePrimitive(primitive, parser).then(function (geometry) {\n        return addPrimitiveAttributes(geometry, primitive, parser);\n      });\n    }\n    const pending = [];\n    for (let i = 0, il = primitives.length; i < il; i++) {\n      const primitive = primitives[i];\n      const cacheKey = createPrimitiveKey(primitive);\n\n      // See if we've already created this geometry\n      const cached = cache[cacheKey];\n      if (cached) {\n        // Use the cached geometry if it exists\n        pending.push(cached.promise);\n      } else {\n        let geometryPromise;\n        if (primitive.extensions && primitive.extensions[EXTENSIONS.KHR_DRACO_MESH_COMPRESSION]) {\n          // Use DRACO geometry if available\n          geometryPromise = createDracoPrimitive(primitive);\n        } else {\n          // Otherwise create a new geometry\n          geometryPromise = addPrimitiveAttributes(new BufferGeometry(), primitive, parser);\n        }\n\n        // Cache this geometry\n        cache[cacheKey] = {\n          primitive: primitive,\n          promise: geometryPromise\n        };\n        pending.push(geometryPromise);\n      }\n    }\n    return Promise.all(pending);\n  }\n\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#meshes\n   *\n   * @private\n   * @param {number} meshIndex\n   * @return {Promise<Group|Mesh|SkinnedMesh|Line|Points>}\n   */\n  loadMesh(meshIndex) {\n    const parser = this;\n    const json = this.json;\n    const extensions = this.extensions;\n    const meshDef = json.meshes[meshIndex];\n    const primitives = meshDef.primitives;\n    const pending = [];\n    for (let i = 0, il = primitives.length; i < il; i++) {\n      const material = primitives[i].material === undefined ? createDefaultMaterial(this.cache) : this.getDependency('material', primitives[i].material);\n      pending.push(material);\n    }\n    pending.push(parser.loadGeometries(primitives));\n    return Promise.all(pending).then(function (results) {\n      const materials = results.slice(0, results.length - 1);\n      const geometries = results[results.length - 1];\n      const meshes = [];\n      for (let i = 0, il = geometries.length; i < il; i++) {\n        const geometry = geometries[i];\n        const primitive = primitives[i];\n\n        // 1. create Mesh\n\n        let mesh;\n        const material = materials[i];\n        if (primitive.mode === WEBGL_CONSTANTS.TRIANGLES || primitive.mode === WEBGL_CONSTANTS.TRIANGLE_STRIP || primitive.mode === WEBGL_CONSTANTS.TRIANGLE_FAN || primitive.mode === undefined) {\n          // .isSkinnedMesh isn't in glTF spec. See ._markDefs()\n          mesh = meshDef.isSkinnedMesh === true ? new SkinnedMesh(geometry, material) : new Mesh(geometry, material);\n          if (mesh.isSkinnedMesh === true) {\n            // normalize skin weights to fix malformed assets (see #15319)\n            mesh.normalizeSkinWeights();\n          }\n          if (primitive.mode === WEBGL_CONSTANTS.TRIANGLE_STRIP) {\n            mesh.geometry = toTrianglesDrawMode(mesh.geometry, TriangleStripDrawMode);\n          } else if (primitive.mode === WEBGL_CONSTANTS.TRIANGLE_FAN) {\n            mesh.geometry = toTrianglesDrawMode(mesh.geometry, TriangleFanDrawMode);\n          }\n        } else if (primitive.mode === WEBGL_CONSTANTS.LINES) {\n          mesh = new LineSegments(geometry, material);\n        } else if (primitive.mode === WEBGL_CONSTANTS.LINE_STRIP) {\n          mesh = new Line(geometry, material);\n        } else if (primitive.mode === WEBGL_CONSTANTS.LINE_LOOP) {\n          mesh = new LineLoop(geometry, material);\n        } else if (primitive.mode === WEBGL_CONSTANTS.POINTS) {\n          mesh = new Points(geometry, material);\n        } else {\n          throw new Error('THREE.GLTFLoader: Primitive mode unsupported: ' + primitive.mode);\n        }\n        if (Object.keys(mesh.geometry.morphAttributes).length > 0) {\n          updateMorphTargets(mesh, meshDef);\n        }\n        mesh.name = parser.createUniqueName(meshDef.name || 'mesh_' + meshIndex);\n        assignExtrasToUserData(mesh, meshDef);\n        if (primitive.extensions) addUnknownExtensionsToUserData(extensions, mesh, primitive);\n        parser.assignFinalMaterial(mesh);\n        meshes.push(mesh);\n      }\n      for (let i = 0, il = meshes.length; i < il; i++) {\n        parser.associations.set(meshes[i], {\n          meshes: meshIndex,\n          primitives: i\n        });\n      }\n      if (meshes.length === 1) {\n        if (meshDef.extensions) addUnknownExtensionsToUserData(extensions, meshes[0], meshDef);\n        return meshes[0];\n      }\n      const group = new Group();\n      if (meshDef.extensions) addUnknownExtensionsToUserData(extensions, group, meshDef);\n      parser.associations.set(group, {\n        meshes: meshIndex\n      });\n      for (let i = 0, il = meshes.length; i < il; i++) {\n        group.add(meshes[i]);\n      }\n      return group;\n    });\n  }\n\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#cameras\n   *\n   * @private\n   * @param {number} cameraIndex\n   * @return {Promise<THREE.Camera>}\n   */\n  loadCamera(cameraIndex) {\n    let camera;\n    const cameraDef = this.json.cameras[cameraIndex];\n    const params = cameraDef[cameraDef.type];\n    if (!params) {\n      console.warn('THREE.GLTFLoader: Missing camera parameters.');\n      return;\n    }\n    if (cameraDef.type === 'perspective') {\n      camera = new PerspectiveCamera(MathUtils.radToDeg(params.yfov), params.aspectRatio || 1, params.znear || 1, params.zfar || 2e6);\n    } else if (cameraDef.type === 'orthographic') {\n      camera = new OrthographicCamera(-params.xmag, params.xmag, params.ymag, -params.ymag, params.znear, params.zfar);\n    }\n    if (cameraDef.name) camera.name = this.createUniqueName(cameraDef.name);\n    assignExtrasToUserData(camera, cameraDef);\n    return Promise.resolve(camera);\n  }\n\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#skins\n   *\n   * @private\n   * @param {number} skinIndex\n   * @return {Promise<Skeleton>}\n   */\n  loadSkin(skinIndex) {\n    const skinDef = this.json.skins[skinIndex];\n    const pending = [];\n    for (let i = 0, il = skinDef.joints.length; i < il; i++) {\n      pending.push(this._loadNodeShallow(skinDef.joints[i]));\n    }\n    if (skinDef.inverseBindMatrices !== undefined) {\n      pending.push(this.getDependency('accessor', skinDef.inverseBindMatrices));\n    } else {\n      pending.push(null);\n    }\n    return Promise.all(pending).then(function (results) {\n      const inverseBindMatrices = results.pop();\n      const jointNodes = results;\n\n      // Note that bones (joint nodes) may or may not be in the\n      // scene graph at this time.\n\n      const bones = [];\n      const boneInverses = [];\n      for (let i = 0, il = jointNodes.length; i < il; i++) {\n        const jointNode = jointNodes[i];\n        if (jointNode) {\n          bones.push(jointNode);\n          const mat = new Matrix4();\n          if (inverseBindMatrices !== null) {\n            mat.fromArray(inverseBindMatrices.array, i * 16);\n          }\n          boneInverses.push(mat);\n        } else {\n          console.warn('THREE.GLTFLoader: Joint \"%s\" could not be found.', skinDef.joints[i]);\n        }\n      }\n      return new Skeleton(bones, boneInverses);\n    });\n  }\n\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#animations\n   *\n   * @private\n   * @param {number} animationIndex\n   * @return {Promise<AnimationClip>}\n   */\n  loadAnimation(animationIndex) {\n    const json = this.json;\n    const parser = this;\n    const animationDef = json.animations[animationIndex];\n    const animationName = animationDef.name ? animationDef.name : 'animation_' + animationIndex;\n    const pendingNodes = [];\n    const pendingInputAccessors = [];\n    const pendingOutputAccessors = [];\n    const pendingSamplers = [];\n    const pendingTargets = [];\n    for (let i = 0, il = animationDef.channels.length; i < il; i++) {\n      const channel = animationDef.channels[i];\n      const sampler = animationDef.samplers[channel.sampler];\n      const target = channel.target;\n      const name = target.node;\n      const input = animationDef.parameters !== undefined ? animationDef.parameters[sampler.input] : sampler.input;\n      const output = animationDef.parameters !== undefined ? animationDef.parameters[sampler.output] : sampler.output;\n      if (target.node === undefined) continue;\n      pendingNodes.push(this.getDependency('node', name));\n      pendingInputAccessors.push(this.getDependency('accessor', input));\n      pendingOutputAccessors.push(this.getDependency('accessor', output));\n      pendingSamplers.push(sampler);\n      pendingTargets.push(target);\n    }\n    return Promise.all([Promise.all(pendingNodes), Promise.all(pendingInputAccessors), Promise.all(pendingOutputAccessors), Promise.all(pendingSamplers), Promise.all(pendingTargets)]).then(function (dependencies) {\n      const nodes = dependencies[0];\n      const inputAccessors = dependencies[1];\n      const outputAccessors = dependencies[2];\n      const samplers = dependencies[3];\n      const targets = dependencies[4];\n      const tracks = [];\n      for (let i = 0, il = nodes.length; i < il; i++) {\n        const node = nodes[i];\n        const inputAccessor = inputAccessors[i];\n        const outputAccessor = outputAccessors[i];\n        const sampler = samplers[i];\n        const target = targets[i];\n        if (node === undefined) continue;\n        if (node.updateMatrix) {\n          node.updateMatrix();\n        }\n        const createdTracks = parser._createAnimationTracks(node, inputAccessor, outputAccessor, sampler, target);\n        if (createdTracks) {\n          for (let k = 0; k < createdTracks.length; k++) {\n            tracks.push(createdTracks[k]);\n          }\n        }\n      }\n      return new AnimationClip(animationName, undefined, tracks);\n    });\n  }\n  createNodeMesh(nodeIndex) {\n    const json = this.json;\n    const parser = this;\n    const nodeDef = json.nodes[nodeIndex];\n    if (nodeDef.mesh === undefined) return null;\n    return parser.getDependency('mesh', nodeDef.mesh).then(function (mesh) {\n      const node = parser._getNodeRef(parser.meshCache, nodeDef.mesh, mesh);\n\n      // if weights are provided on the node, override weights on the mesh.\n      if (nodeDef.weights !== undefined) {\n        node.traverse(function (o) {\n          if (!o.isMesh) return;\n          for (let i = 0, il = nodeDef.weights.length; i < il; i++) {\n            o.morphTargetInfluences[i] = nodeDef.weights[i];\n          }\n        });\n      }\n      return node;\n    });\n  }\n\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#nodes-and-hierarchy\n   *\n   * @private\n   * @param {number} nodeIndex\n   * @return {Promise<Object3D>}\n   */\n  loadNode(nodeIndex) {\n    const json = this.json;\n    const parser = this;\n    const nodeDef = json.nodes[nodeIndex];\n    const nodePending = parser._loadNodeShallow(nodeIndex);\n    const childPending = [];\n    const childrenDef = nodeDef.children || [];\n    for (let i = 0, il = childrenDef.length; i < il; i++) {\n      childPending.push(parser.getDependency('node', childrenDef[i]));\n    }\n    const skeletonPending = nodeDef.skin === undefined ? Promise.resolve(null) : parser.getDependency('skin', nodeDef.skin);\n    return Promise.all([nodePending, Promise.all(childPending), skeletonPending]).then(function (results) {\n      const node = results[0];\n      const children = results[1];\n      const skeleton = results[2];\n      if (skeleton !== null) {\n        // This full traverse should be fine because\n        // child glTF nodes have not been added to this node yet.\n        node.traverse(function (mesh) {\n          if (!mesh.isSkinnedMesh) return;\n          mesh.bind(skeleton, _identityMatrix);\n        });\n      }\n      for (let i = 0, il = children.length; i < il; i++) {\n        node.add(children[i]);\n      }\n      return node;\n    });\n  }\n\n  // ._loadNodeShallow() parses a single node.\n  // skin and child nodes are created and added in .loadNode() (no '_' prefix).\n  _loadNodeShallow(nodeIndex) {\n    const json = this.json;\n    const extensions = this.extensions;\n    const parser = this;\n\n    // This method is called from .loadNode() and .loadSkin().\n    // Cache a node to avoid duplication.\n\n    if (this.nodeCache[nodeIndex] !== undefined) {\n      return this.nodeCache[nodeIndex];\n    }\n    const nodeDef = json.nodes[nodeIndex];\n\n    // reserve node's name before its dependencies, so the root has the intended name.\n    const nodeName = nodeDef.name ? parser.createUniqueName(nodeDef.name) : '';\n    const pending = [];\n    const meshPromise = parser._invokeOne(function (ext) {\n      return ext.createNodeMesh && ext.createNodeMesh(nodeIndex);\n    });\n    if (meshPromise) {\n      pending.push(meshPromise);\n    }\n    if (nodeDef.camera !== undefined) {\n      pending.push(parser.getDependency('camera', nodeDef.camera).then(function (camera) {\n        return parser._getNodeRef(parser.cameraCache, nodeDef.camera, camera);\n      }));\n    }\n    parser._invokeAll(function (ext) {\n      return ext.createNodeAttachment && ext.createNodeAttachment(nodeIndex);\n    }).forEach(function (promise) {\n      pending.push(promise);\n    });\n    this.nodeCache[nodeIndex] = Promise.all(pending).then(function (objects) {\n      let node;\n\n      // .isBone isn't in glTF spec. See ._markDefs\n      if (nodeDef.isBone === true) {\n        node = new Bone();\n      } else if (objects.length > 1) {\n        node = new Group();\n      } else if (objects.length === 1) {\n        node = objects[0];\n      } else {\n        node = new Object3D();\n      }\n      if (node !== objects[0]) {\n        for (let i = 0, il = objects.length; i < il; i++) {\n          node.add(objects[i]);\n        }\n      }\n      if (nodeDef.name) {\n        node.userData.name = nodeDef.name;\n        node.name = nodeName;\n      }\n      assignExtrasToUserData(node, nodeDef);\n      if (nodeDef.extensions) addUnknownExtensionsToUserData(extensions, node, nodeDef);\n      if (nodeDef.matrix !== undefined) {\n        const matrix = new Matrix4();\n        matrix.fromArray(nodeDef.matrix);\n        node.applyMatrix4(matrix);\n      } else {\n        if (nodeDef.translation !== undefined) {\n          node.position.fromArray(nodeDef.translation);\n        }\n        if (nodeDef.rotation !== undefined) {\n          node.quaternion.fromArray(nodeDef.rotation);\n        }\n        if (nodeDef.scale !== undefined) {\n          node.scale.fromArray(nodeDef.scale);\n        }\n      }\n      if (!parser.associations.has(node)) {\n        parser.associations.set(node, {});\n      }\n      parser.associations.get(node).nodes = nodeIndex;\n      return node;\n    });\n    return this.nodeCache[nodeIndex];\n  }\n\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#scenes\n   *\n   * @private\n   * @param {number} sceneIndex\n   * @return {Promise<Group>}\n   */\n  loadScene(sceneIndex) {\n    const extensions = this.extensions;\n    const sceneDef = this.json.scenes[sceneIndex];\n    const parser = this;\n\n    // Loader returns Group, not Scene.\n    // See: https://github.com/mrdoob/three.js/issues/18342#issuecomment-578981172\n    const scene = new Group();\n    if (sceneDef.name) scene.name = parser.createUniqueName(sceneDef.name);\n    assignExtrasToUserData(scene, sceneDef);\n    if (sceneDef.extensions) addUnknownExtensionsToUserData(extensions, scene, sceneDef);\n    const nodeIds = sceneDef.nodes || [];\n    const pending = [];\n    for (let i = 0, il = nodeIds.length; i < il; i++) {\n      pending.push(parser.getDependency('node', nodeIds[i]));\n    }\n    return Promise.all(pending).then(function (nodes) {\n      for (let i = 0, il = nodes.length; i < il; i++) {\n        scene.add(nodes[i]);\n      }\n\n      // Removes dangling associations, associations that reference a node that\n      // didn't make it into the scene.\n      const reduceAssociations = node => {\n        const reducedAssociations = new Map();\n        for (const [key, value] of parser.associations) {\n          if (key instanceof Material || key instanceof Texture) {\n            reducedAssociations.set(key, value);\n          }\n        }\n        node.traverse(node => {\n          const mappings = parser.associations.get(node);\n          if (mappings != null) {\n            reducedAssociations.set(node, mappings);\n          }\n        });\n        return reducedAssociations;\n      };\n      parser.associations = reduceAssociations(scene);\n      return scene;\n    });\n  }\n  _createAnimationTracks(node, inputAccessor, outputAccessor, sampler, target) {\n    const tracks = [];\n    const targetName = node.name ? node.name : node.uuid;\n    const targetNames = [];\n    if (PATH_PROPERTIES[target.path] === PATH_PROPERTIES.weights) {\n      node.traverse(function (object) {\n        if (object.morphTargetInfluences) {\n          targetNames.push(object.name ? object.name : object.uuid);\n        }\n      });\n    } else {\n      targetNames.push(targetName);\n    }\n    let TypedKeyframeTrack;\n    switch (PATH_PROPERTIES[target.path]) {\n      case PATH_PROPERTIES.weights:\n        TypedKeyframeTrack = NumberKeyframeTrack;\n        break;\n      case PATH_PROPERTIES.rotation:\n        TypedKeyframeTrack = QuaternionKeyframeTrack;\n        break;\n      case PATH_PROPERTIES.translation:\n      case PATH_PROPERTIES.scale:\n        TypedKeyframeTrack = VectorKeyframeTrack;\n        break;\n      default:\n        switch (outputAccessor.itemSize) {\n          case 1:\n            TypedKeyframeTrack = NumberKeyframeTrack;\n            break;\n          case 2:\n          case 3:\n          default:\n            TypedKeyframeTrack = VectorKeyframeTrack;\n            break;\n        }\n        break;\n    }\n    const interpolation = sampler.interpolation !== undefined ? INTERPOLATION[sampler.interpolation] : InterpolateLinear;\n    const outputArray = this._getArrayFromAccessor(outputAccessor);\n    for (let j = 0, jl = targetNames.length; j < jl; j++) {\n      const track = new TypedKeyframeTrack(targetNames[j] + '.' + PATH_PROPERTIES[target.path], inputAccessor.array, outputArray, interpolation);\n\n      // Override interpolation with custom factory method.\n      if (sampler.interpolation === 'CUBICSPLINE') {\n        this._createCubicSplineTrackInterpolant(track);\n      }\n      tracks.push(track);\n    }\n    return tracks;\n  }\n  _getArrayFromAccessor(accessor) {\n    let outputArray = accessor.array;\n    if (accessor.normalized) {\n      const scale = getNormalizedComponentScale(outputArray.constructor);\n      const scaled = new Float32Array(outputArray.length);\n      for (let j = 0, jl = outputArray.length; j < jl; j++) {\n        scaled[j] = outputArray[j] * scale;\n      }\n      outputArray = scaled;\n    }\n    return outputArray;\n  }\n  _createCubicSplineTrackInterpolant(track) {\n    track.createInterpolant = function InterpolantFactoryMethodGLTFCubicSpline(result) {\n      // A CUBICSPLINE keyframe in glTF has three output values for each input value,\n      // representing inTangent, splineVertex, and outTangent. As a result, track.getValueSize()\n      // must be divided by three to get the interpolant's sampleSize argument.\n\n      const interpolantType = this instanceof QuaternionKeyframeTrack ? GLTFCubicSplineQuaternionInterpolant : GLTFCubicSplineInterpolant;\n      return new interpolantType(this.times, this.values, this.getValueSize() / 3, result);\n    };\n\n    // Mark as CUBICSPLINE. `track.getInterpolation()` doesn't support custom interpolants.\n    track.createInterpolant.isInterpolantFactoryMethodGLTFCubicSpline = true;\n  }\n}\n\n/**\n *\n * @private\n * @param {BufferGeometry} geometry\n * @param {GLTF.Primitive} primitiveDef\n * @param {GLTFParser} parser\n */\nfunction computeBounds(geometry, primitiveDef, parser) {\n  const attributes = primitiveDef.attributes;\n  const box = new Box3();\n  if (attributes.POSITION !== undefined) {\n    const accessor = parser.json.accessors[attributes.POSITION];\n    const min = accessor.min;\n    const max = accessor.max;\n\n    // glTF requires 'min' and 'max', but VRM (which extends glTF) currently ignores that requirement.\n\n    if (min !== undefined && max !== undefined) {\n      box.set(new Vector3(min[0], min[1], min[2]), new Vector3(max[0], max[1], max[2]));\n      if (accessor.normalized) {\n        const boxScale = getNormalizedComponentScale(WEBGL_COMPONENT_TYPES[accessor.componentType]);\n        box.min.multiplyScalar(boxScale);\n        box.max.multiplyScalar(boxScale);\n      }\n    } else {\n      console.warn('THREE.GLTFLoader: Missing min/max properties for accessor POSITION.');\n      return;\n    }\n  } else {\n    return;\n  }\n  const targets = primitiveDef.targets;\n  if (targets !== undefined) {\n    const maxDisplacement = new Vector3();\n    const vector = new Vector3();\n    for (let i = 0, il = targets.length; i < il; i++) {\n      const target = targets[i];\n      if (target.POSITION !== undefined) {\n        const accessor = parser.json.accessors[target.POSITION];\n        const min = accessor.min;\n        const max = accessor.max;\n\n        // glTF requires 'min' and 'max', but VRM (which extends glTF) currently ignores that requirement.\n\n        if (min !== undefined && max !== undefined) {\n          // we need to get max of absolute components because target weight is [-1,1]\n          vector.setX(Math.max(Math.abs(min[0]), Math.abs(max[0])));\n          vector.setY(Math.max(Math.abs(min[1]), Math.abs(max[1])));\n          vector.setZ(Math.max(Math.abs(min[2]), Math.abs(max[2])));\n          if (accessor.normalized) {\n            const boxScale = getNormalizedComponentScale(WEBGL_COMPONENT_TYPES[accessor.componentType]);\n            vector.multiplyScalar(boxScale);\n          }\n\n          // Note: this assumes that the sum of all weights is at most 1. This isn't quite correct - it's more conservative\n          // to assume that each target can have a max weight of 1. However, for some use cases - notably, when morph targets\n          // are used to implement key-frame animations and as such only two are active at a time - this results in very large\n          // boxes. So for now we make a box that's sometimes a touch too small but is hopefully mostly of reasonable size.\n          maxDisplacement.max(vector);\n        } else {\n          console.warn('THREE.GLTFLoader: Missing min/max properties for accessor POSITION.');\n        }\n      }\n    }\n\n    // As per comment above this box isn't conservative, but has a reasonable size for a very large number of morph targets.\n    box.expandByVector(maxDisplacement);\n  }\n  geometry.boundingBox = box;\n  const sphere = new Sphere();\n  box.getCenter(sphere.center);\n  sphere.radius = box.min.distanceTo(box.max) / 2;\n  geometry.boundingSphere = sphere;\n}\n\n/**\n *\n * @private\n * @param {BufferGeometry} geometry\n * @param {GLTF.Primitive} primitiveDef\n * @param {GLTFParser} parser\n * @return {Promise<BufferGeometry>}\n */\nfunction addPrimitiveAttributes(geometry, primitiveDef, parser) {\n  const attributes = primitiveDef.attributes;\n  const pending = [];\n  function assignAttributeAccessor(accessorIndex, attributeName) {\n    return parser.getDependency('accessor', accessorIndex).then(function (accessor) {\n      geometry.setAttribute(attributeName, accessor);\n    });\n  }\n  for (const gltfAttributeName in attributes) {\n    const threeAttributeName = ATTRIBUTES[gltfAttributeName] || gltfAttributeName.toLowerCase();\n\n    // Skip attributes already provided by e.g. Draco extension.\n    if (threeAttributeName in geometry.attributes) continue;\n    pending.push(assignAttributeAccessor(attributes[gltfAttributeName], threeAttributeName));\n  }\n  if (primitiveDef.indices !== undefined && !geometry.index) {\n    const accessor = parser.getDependency('accessor', primitiveDef.indices).then(function (accessor) {\n      geometry.setIndex(accessor);\n    });\n    pending.push(accessor);\n  }\n  if (ColorManagement.workingColorSpace !== LinearSRGBColorSpace && 'COLOR_0' in attributes) {\n    console.warn(`THREE.GLTFLoader: Converting vertex colors from \"srgb-linear\" to \"${ColorManagement.workingColorSpace}\" not supported.`);\n  }\n  assignExtrasToUserData(geometry, primitiveDef);\n  computeBounds(geometry, primitiveDef, parser);\n  return Promise.all(pending).then(function () {\n    return primitiveDef.targets !== undefined ? addMorphTargets(geometry, primitiveDef.targets, parser) : geometry;\n  });\n}\n\n/**\n * Loader result of `GLTFLoader`.\n *\n * @typedef {Object} GLTFLoader~LoadObject\n * @property {Array<AnimationClip>} animations - An array of animation clips.\n * @property {Object} asset - Meta data about the loaded asset.\n * @property {Array<Camera>} cameras - An array of cameras.\n * @property {GLTFParser} parser - A reference to the internal parser.\n * @property {Group} scene - The default scene.\n * @property {Array<Group>} scenes - glTF assets might define multiple scenes.\n * @property {Object} userData - Additional data.\n **/\n\nexport { GLTFLoader };"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAghBA,SAAS,oBAAoB,UAAU,UAAU;AAC/C,MAAI,aAAa,mBAAmB;AAClC,YAAQ,KAAK,yFAAyF;AACtG,WAAO;AAAA,EACT;AACA,MAAI,aAAa,uBAAuB,aAAa,uBAAuB;AAC1E,QAAI,QAAQ,SAAS,SAAS;AAI9B,QAAI,UAAU,MAAM;AAClB,YAAM,UAAU,CAAC;AACjB,YAAM,WAAW,SAAS,aAAa,UAAU;AACjD,UAAI,aAAa,QAAW;AAC1B,iBAAS,IAAI,GAAG,IAAI,SAAS,OAAO,KAAK;AACvC,kBAAQ,KAAK,CAAC;AAAA,QAChB;AACA,iBAAS,SAAS,OAAO;AACzB,gBAAQ,SAAS,SAAS;AAAA,MAC5B,OAAO;AACL,gBAAQ,MAAM,yGAAyG;AACvH,eAAO;AAAA,MACT;AAAA,IACF;AAIA,UAAM,oBAAoB,MAAM,QAAQ;AACxC,UAAM,aAAa,CAAC;AACpB,QAAI,aAAa,qBAAqB;AAGpC,eAAS,IAAI,GAAG,KAAK,mBAAmB,KAAK;AAC3C,mBAAW,KAAK,MAAM,KAAK,CAAC,CAAC;AAC7B,mBAAW,KAAK,MAAM,KAAK,CAAC,CAAC;AAC7B,mBAAW,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;AAAA,MACnC;AAAA,IACF,OAAO;AAGL,eAAS,IAAI,GAAG,IAAI,mBAAmB,KAAK;AAC1C,YAAI,IAAI,MAAM,GAAG;AACf,qBAAW,KAAK,MAAM,KAAK,CAAC,CAAC;AAC7B,qBAAW,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;AACjC,qBAAW,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;AAAA,QACnC,OAAO;AACL,qBAAW,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;AACjC,qBAAW,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;AACjC,qBAAW,KAAK,MAAM,KAAK,CAAC,CAAC;AAAA,QAC/B;AAAA,MACF;AAAA,IACF;AACA,QAAI,WAAW,SAAS,MAAM,mBAAmB;AAC/C,cAAQ,MAAM,kGAAkG;AAAA,IAClH;AAIA,UAAM,cAAc,SAAS,MAAM;AACnC,gBAAY,SAAS,UAAU;AAC/B,gBAAY,YAAY;AACxB,WAAO;AAAA,EACT,OAAO;AACL,YAAQ,MAAM,uEAAuE,QAAQ;AAC7F,WAAO;AAAA,EACT;AACF;;;AC9hBA,IAAM,aAAN,cAAyB,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM9B,YAAY,SAAS;AACnB,UAAM,OAAO;AACb,SAAK,cAAc;AACnB,SAAK,aAAa;AAClB,SAAK,iBAAiB;AACtB,SAAK,kBAAkB,CAAC;AACxB,SAAK,SAAS,SAAU,QAAQ;AAC9B,aAAO,IAAI,gCAAgC,MAAM;AAAA,IACnD,CAAC;AACD,SAAK,SAAS,SAAU,QAAQ;AAC9B,aAAO,IAAI,iCAAiC,MAAM;AAAA,IACpD,CAAC;AACD,SAAK,SAAS,SAAU,QAAQ;AAC9B,aAAO,IAAI,2BAA2B,MAAM;AAAA,IAC9C,CAAC;AACD,SAAK,SAAS,SAAU,QAAQ;AAC9B,aAAO,IAAI,yBAAyB,MAAM;AAAA,IAC5C,CAAC;AACD,SAAK,SAAS,SAAU,QAAQ;AAC9B,aAAO,IAAI,yBAAyB,MAAM;AAAA,IAC5C,CAAC;AACD,SAAK,SAAS,SAAU,QAAQ;AAC9B,aAAO,IAAI,4BAA4B,MAAM;AAAA,IAC/C,CAAC;AACD,SAAK,SAAS,SAAU,QAAQ;AAC9B,aAAO,IAAI,mCAAmC,MAAM;AAAA,IACtD,CAAC;AACD,SAAK,SAAS,SAAU,QAAQ;AAC9B,aAAO,IAAI,6BAA6B,MAAM;AAAA,IAChD,CAAC;AACD,SAAK,SAAS,SAAU,QAAQ;AAC9B,aAAO,IAAI,0BAA0B,MAAM;AAAA,IAC7C,CAAC;AACD,SAAK,SAAS,SAAU,QAAQ;AAC9B,aAAO,IAAI,uCAAuC,MAAM;AAAA,IAC1D,CAAC;AACD,SAAK,SAAS,SAAU,QAAQ;AAC9B,aAAO,IAAI,+BAA+B,MAAM;AAAA,IAClD,CAAC;AACD,SAAK,SAAS,SAAU,QAAQ;AAC9B,aAAO,IAAI,kCAAkC,MAAM;AAAA,IACrD,CAAC;AACD,SAAK,SAAS,SAAU,QAAQ;AAC9B,aAAO,IAAI,iCAAiC,MAAM;AAAA,IACpD,CAAC;AACD,SAAK,SAAS,SAAU,QAAQ;AAC9B,aAAO,IAAI,2BAA2B,MAAM;AAAA,IAC9C,CAAC;AACD,SAAK,SAAS,SAAU,QAAQ;AAC9B,aAAO,IAAI,oBAAoB,MAAM;AAAA,IACvC,CAAC;AACD,SAAK,SAAS,SAAU,QAAQ;AAC9B,aAAO,IAAI,uBAAuB,MAAM;AAAA,IAC1C,CAAC;AACD,SAAK,SAAS,SAAU,QAAQ;AAC9B,aAAO,IAAI,sBAAsB,MAAM;AAAA,IACzC,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,KAAK,KAAK,QAAQ,YAAY,SAAS;AACrC,UAAM,QAAQ;AACd,QAAI;AACJ,QAAI,KAAK,iBAAiB,IAAI;AAC5B,qBAAe,KAAK;AAAA,IACtB,WAAW,KAAK,SAAS,IAAI;AAM3B,YAAM,cAAc,YAAY,eAAe,GAAG;AAClD,qBAAe,YAAY,WAAW,aAAa,KAAK,IAAI;AAAA,IAC9D,OAAO;AACL,qBAAe,YAAY,eAAe,GAAG;AAAA,IAC/C;AAKA,SAAK,QAAQ,UAAU,GAAG;AAC1B,UAAM,WAAW,SAAU,GAAG;AAC5B,UAAI,SAAS;AACX,gBAAQ,CAAC;AAAA,MACX,OAAO;AACL,gBAAQ,MAAM,CAAC;AAAA,MACjB;AACA,YAAM,QAAQ,UAAU,GAAG;AAC3B,YAAM,QAAQ,QAAQ,GAAG;AAAA,IAC3B;AACA,UAAM,SAAS,IAAI,WAAW,KAAK,OAAO;AAC1C,WAAO,QAAQ,KAAK,IAAI;AACxB,WAAO,gBAAgB,aAAa;AACpC,WAAO,iBAAiB,KAAK,aAAa;AAC1C,WAAO,mBAAmB,KAAK,eAAe;AAC9C,WAAO,KAAK,KAAK,SAAU,MAAM;AAC/B,UAAI;AACF,cAAM,MAAM,MAAM,cAAc,SAAU,MAAM;AAC9C,iBAAO,IAAI;AACX,gBAAM,QAAQ,QAAQ,GAAG;AAAA,QAC3B,GAAG,QAAQ;AAAA,MACb,SAAS,GAAG;AACV,iBAAS,CAAC;AAAA,MACZ;AAAA,IACF,GAAG,YAAY,QAAQ;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,eAAe,aAAa;AAC1B,SAAK,cAAc;AACnB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,cAAc,YAAY;AACxB,SAAK,aAAa;AAClB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,kBAAkB,gBAAgB;AAChC,SAAK,iBAAiB;AACtB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,SAAS,UAAU;AACjB,QAAI,KAAK,gBAAgB,QAAQ,QAAQ,MAAM,IAAI;AACjD,WAAK,gBAAgB,KAAK,QAAQ;AAAA,IACpC;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,WAAW,UAAU;AACnB,QAAI,KAAK,gBAAgB,QAAQ,QAAQ,MAAM,IAAI;AACjD,WAAK,gBAAgB,OAAO,KAAK,gBAAgB,QAAQ,QAAQ,GAAG,CAAC;AAAA,IACvE;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAM,MAAM,MAAM,QAAQ,SAAS;AACjC,QAAI;AACJ,UAAM,aAAa,CAAC;AACpB,UAAM,UAAU,CAAC;AACjB,UAAM,cAAc,IAAI,YAAY;AACpC,QAAI,OAAO,SAAS,UAAU;AAC5B,aAAO,KAAK,MAAM,IAAI;AAAA,IACxB,WAAW,gBAAgB,aAAa;AACtC,YAAM,QAAQ,YAAY,OAAO,IAAI,WAAW,MAAM,GAAG,CAAC,CAAC;AAC3D,UAAI,UAAU,+BAA+B;AAC3C,YAAI;AACF,qBAAW,WAAW,eAAe,IAAI,IAAI,oBAAoB,IAAI;AAAA,QACvE,SAAS,OAAO;AACd,cAAI,QAAS,SAAQ,KAAK;AAC1B;AAAA,QACF;AACA,eAAO,KAAK,MAAM,WAAW,WAAW,eAAe,EAAE,OAAO;AAAA,MAClE,OAAO;AACL,eAAO,KAAK,MAAM,YAAY,OAAO,IAAI,CAAC;AAAA,MAC5C;AAAA,IACF,OAAO;AACL,aAAO;AAAA,IACT;AACA,QAAI,KAAK,UAAU,UAAa,KAAK,MAAM,QAAQ,CAAC,IAAI,GAAG;AACzD,UAAI,QAAS,SAAQ,IAAI,MAAM,yEAAyE,CAAC;AACzG;AAAA,IACF;AACA,UAAM,SAAS,IAAI,WAAW,MAAM;AAAA,MAClC,MAAM,QAAQ,KAAK,gBAAgB;AAAA,MACnC,aAAa,KAAK;AAAA,MAClB,eAAe,KAAK;AAAA,MACpB,SAAS,KAAK;AAAA,MACd,YAAY,KAAK;AAAA,MACjB,gBAAgB,KAAK;AAAA,IACvB,CAAC;AACD,WAAO,WAAW,iBAAiB,KAAK,aAAa;AACrD,aAAS,IAAI,GAAG,IAAI,KAAK,gBAAgB,QAAQ,KAAK;AACpD,YAAM,SAAS,KAAK,gBAAgB,CAAC,EAAE,MAAM;AAC7C,UAAI,CAAC,OAAO,KAAM,SAAQ,MAAM,sDAAsD;AACtF,cAAQ,OAAO,IAAI,IAAI;AAMvB,iBAAW,OAAO,IAAI,IAAI;AAAA,IAC5B;AACA,QAAI,KAAK,gBAAgB;AACvB,eAAS,IAAI,GAAG,IAAI,KAAK,eAAe,QAAQ,EAAE,GAAG;AACnD,cAAM,gBAAgB,KAAK,eAAe,CAAC;AAC3C,cAAM,qBAAqB,KAAK,sBAAsB,CAAC;AACvD,gBAAQ,eAAe;AAAA,UACrB,KAAK,WAAW;AACd,uBAAW,aAAa,IAAI,IAAI,4BAA4B;AAC5D;AAAA,UACF,KAAK,WAAW;AACd,uBAAW,aAAa,IAAI,IAAI,kCAAkC,MAAM,KAAK,WAAW;AACxF;AAAA,UACF,KAAK,WAAW;AACd,uBAAW,aAAa,IAAI,IAAI,8BAA8B;AAC9D;AAAA,UACF,KAAK,WAAW;AACd,uBAAW,aAAa,IAAI,IAAI,8BAA8B;AAC9D;AAAA,UACF;AACE,gBAAI,mBAAmB,QAAQ,aAAa,KAAK,KAAK,QAAQ,aAAa,MAAM,QAAW;AAC1F,sBAAQ,KAAK,0CAA0C,gBAAgB,IAAI;AAAA,YAC7E;AAAA,QACJ;AAAA,MACF;AAAA,IACF;AACA,WAAO,cAAc,UAAU;AAC/B,WAAO,WAAW,OAAO;AACzB,WAAO,MAAM,QAAQ,OAAO;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,WAAW,MAAM,MAAM;AACrB,UAAM,QAAQ;AACd,WAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC5C,YAAM,MAAM,MAAM,MAAM,SAAS,MAAM;AAAA,IACzC,CAAC;AAAA,EACH;AACF;AAIA,SAAS,eAAe;AACtB,MAAI,UAAU,CAAC;AACf,SAAO;AAAA,IACL,KAAK,SAAU,KAAK;AAClB,aAAO,QAAQ,GAAG;AAAA,IACpB;AAAA,IACA,KAAK,SAAU,KAAK,QAAQ;AAC1B,cAAQ,GAAG,IAAI;AAAA,IACjB;AAAA,IACA,QAAQ,SAAU,KAAK;AACrB,aAAO,QAAQ,GAAG;AAAA,IACpB;AAAA,IACA,WAAW,WAAY;AACrB,gBAAU,CAAC;AAAA,IACb;AAAA,EACF;AACF;AAMA,IAAM,aAAa;AAAA,EACjB,iBAAiB;AAAA,EACjB,4BAA4B;AAAA,EAC5B,qBAAqB;AAAA,EACrB,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,wBAAwB;AAAA,EACxB,4BAA4B;AAAA,EAC5B,2BAA2B;AAAA,EAC3B,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,sBAAsB;AAAA,EACtB,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,iCAAiC;AAAA,EACjC,oBAAoB;AAAA,EACpB,kBAAkB;AAAA,EAClB,kBAAkB;AAAA,EAClB,yBAAyB;AAAA,EACzB,yBAAyB;AAC3B;AASA,IAAM,sBAAN,MAA0B;AAAA,EACxB,YAAY,QAAQ;AAClB,SAAK,SAAS;AACd,SAAK,OAAO,WAAW;AAGvB,SAAK,QAAQ;AAAA,MACX,MAAM,CAAC;AAAA,MACP,MAAM,CAAC;AAAA,IACT;AAAA,EACF;AAAA,EACA,YAAY;AACV,UAAM,SAAS,KAAK;AACpB,UAAM,WAAW,KAAK,OAAO,KAAK,SAAS,CAAC;AAC5C,aAAS,YAAY,GAAG,aAAa,SAAS,QAAQ,YAAY,YAAY,aAAa;AACzF,YAAM,UAAU,SAAS,SAAS;AAClC,UAAI,QAAQ,cAAc,QAAQ,WAAW,KAAK,IAAI,KAAK,QAAQ,WAAW,KAAK,IAAI,EAAE,UAAU,QAAW;AAC5G,eAAO,YAAY,KAAK,OAAO,QAAQ,WAAW,KAAK,IAAI,EAAE,KAAK;AAAA,MACpE;AAAA,IACF;AAAA,EACF;AAAA,EACA,WAAW,YAAY;AACrB,UAAM,SAAS,KAAK;AACpB,UAAM,WAAW,WAAW;AAC5B,QAAI,aAAa,OAAO,MAAM,IAAI,QAAQ;AAC1C,QAAI,WAAY,QAAO;AACvB,UAAM,OAAO,OAAO;AACpB,UAAM,aAAa,KAAK,cAAc,KAAK,WAAW,KAAK,IAAI,KAAK,CAAC;AACrE,UAAM,YAAY,WAAW,UAAU,CAAC;AACxC,UAAM,WAAW,UAAU,UAAU;AACrC,QAAI;AACJ,UAAM,QAAQ,IAAI,MAAM,QAAQ;AAChC,QAAI,SAAS,UAAU,OAAW,OAAM,OAAO,SAAS,MAAM,CAAC,GAAG,SAAS,MAAM,CAAC,GAAG,SAAS,MAAM,CAAC,GAAG,oBAAoB;AAC5H,UAAM,QAAQ,SAAS,UAAU,SAAY,SAAS,QAAQ;AAC9D,YAAQ,SAAS,MAAM;AAAA,MACrB,KAAK;AACH,oBAAY,IAAI,iBAAiB,KAAK;AACtC,kBAAU,OAAO,SAAS,IAAI,GAAG,GAAG,EAAE;AACtC,kBAAU,IAAI,UAAU,MAAM;AAC9B;AAAA,MACF,KAAK;AACH,oBAAY,IAAI,WAAW,KAAK;AAChC,kBAAU,WAAW;AACrB;AAAA,MACF,KAAK;AACH,oBAAY,IAAI,UAAU,KAAK;AAC/B,kBAAU,WAAW;AAErB,iBAAS,OAAO,SAAS,QAAQ,CAAC;AAClC,iBAAS,KAAK,iBAAiB,SAAS,KAAK,mBAAmB,SAAY,SAAS,KAAK,iBAAiB;AAC3G,iBAAS,KAAK,iBAAiB,SAAS,KAAK,mBAAmB,SAAY,SAAS,KAAK,iBAAiB,KAAK,KAAK;AACrH,kBAAU,QAAQ,SAAS,KAAK;AAChC,kBAAU,WAAW,IAAM,SAAS,KAAK,iBAAiB,SAAS,KAAK;AACxE,kBAAU,OAAO,SAAS,IAAI,GAAG,GAAG,EAAE;AACtC,kBAAU,IAAI,UAAU,MAAM;AAC9B;AAAA,MACF;AACE,cAAM,IAAI,MAAM,8CAA8C,SAAS,IAAI;AAAA,IAC/E;AAIA,cAAU,SAAS,IAAI,GAAG,GAAG,CAAC;AAC9B,2BAAuB,WAAW,QAAQ;AAC1C,QAAI,SAAS,cAAc,OAAW,WAAU,YAAY,SAAS;AACrE,cAAU,OAAO,OAAO,iBAAiB,SAAS,QAAQ,WAAW,UAAU;AAC/E,iBAAa,QAAQ,QAAQ,SAAS;AACtC,WAAO,MAAM,IAAI,UAAU,UAAU;AACrC,WAAO;AAAA,EACT;AAAA,EACA,cAAc,MAAM,OAAO;AACzB,QAAI,SAAS,QAAS;AACtB,WAAO,KAAK,WAAW,KAAK;AAAA,EAC9B;AAAA,EACA,qBAAqB,WAAW;AAC9B,UAAMA,QAAO;AACb,UAAM,SAAS,KAAK;AACpB,UAAM,OAAO,OAAO;AACpB,UAAM,UAAU,KAAK,MAAM,SAAS;AACpC,UAAM,WAAW,QAAQ,cAAc,QAAQ,WAAW,KAAK,IAAI,KAAK,CAAC;AACzE,UAAM,aAAa,SAAS;AAC5B,QAAI,eAAe,OAAW,QAAO;AACrC,WAAO,KAAK,WAAW,UAAU,EAAE,KAAK,SAAU,OAAO;AACvD,aAAO,OAAO,YAAYA,MAAK,OAAO,YAAY,KAAK;AAAA,IACzD,CAAC;AAAA,EACH;AACF;AASA,IAAM,8BAAN,MAAkC;AAAA,EAChC,cAAc;AACZ,SAAK,OAAO,WAAW;AAAA,EACzB;AAAA,EACA,kBAAkB;AAChB,WAAO;AAAA,EACT;AAAA,EACA,aAAa,gBAAgB,aAAa,QAAQ;AAChD,UAAM,UAAU,CAAC;AACjB,mBAAe,QAAQ,IAAI,MAAM,GAAK,GAAK,CAAG;AAC9C,mBAAe,UAAU;AACzB,UAAM,oBAAoB,YAAY;AACtC,QAAI,mBAAmB;AACrB,UAAI,MAAM,QAAQ,kBAAkB,eAAe,GAAG;AACpD,cAAM,QAAQ,kBAAkB;AAChC,uBAAe,MAAM,OAAO,MAAM,CAAC,GAAG,MAAM,CAAC,GAAG,MAAM,CAAC,GAAG,oBAAoB;AAC9E,uBAAe,UAAU,MAAM,CAAC;AAAA,MAClC;AACA,UAAI,kBAAkB,qBAAqB,QAAW;AACpD,gBAAQ,KAAK,OAAO,cAAc,gBAAgB,OAAO,kBAAkB,kBAAkB,cAAc,CAAC;AAAA,MAC9G;AAAA,IACF;AACA,WAAO,QAAQ,IAAI,OAAO;AAAA,EAC5B;AACF;AASA,IAAM,yCAAN,MAA6C;AAAA,EAC3C,YAAY,QAAQ;AAClB,SAAK,SAAS;AACd,SAAK,OAAO,WAAW;AAAA,EACzB;AAAA,EACA,qBAAqB,eAAe,gBAAgB;AAClD,UAAM,SAAS,KAAK;AACpB,UAAM,cAAc,OAAO,KAAK,UAAU,aAAa;AACvD,QAAI,CAAC,YAAY,cAAc,CAAC,YAAY,WAAW,KAAK,IAAI,GAAG;AACjE,aAAO,QAAQ,QAAQ;AAAA,IACzB;AACA,UAAM,mBAAmB,YAAY,WAAW,KAAK,IAAI,EAAE;AAC3D,QAAI,qBAAqB,QAAW;AAClC,qBAAe,oBAAoB;AAAA,IACrC;AACA,WAAO,QAAQ,QAAQ;AAAA,EACzB;AACF;AASA,IAAM,kCAAN,MAAsC;AAAA,EACpC,YAAY,QAAQ;AAClB,SAAK,SAAS;AACd,SAAK,OAAO,WAAW;AAAA,EACzB;AAAA,EACA,gBAAgB,eAAe;AAC7B,UAAM,SAAS,KAAK;AACpB,UAAM,cAAc,OAAO,KAAK,UAAU,aAAa;AACvD,QAAI,CAAC,YAAY,cAAc,CAAC,YAAY,WAAW,KAAK,IAAI,EAAG,QAAO;AAC1E,WAAO;AAAA,EACT;AAAA,EACA,qBAAqB,eAAe,gBAAgB;AAClD,UAAM,SAAS,KAAK;AACpB,UAAM,cAAc,OAAO,KAAK,UAAU,aAAa;AACvD,QAAI,CAAC,YAAY,cAAc,CAAC,YAAY,WAAW,KAAK,IAAI,GAAG;AACjE,aAAO,QAAQ,QAAQ;AAAA,IACzB;AACA,UAAM,UAAU,CAAC;AACjB,UAAM,YAAY,YAAY,WAAW,KAAK,IAAI;AAClD,QAAI,UAAU,oBAAoB,QAAW;AAC3C,qBAAe,YAAY,UAAU;AAAA,IACvC;AACA,QAAI,UAAU,qBAAqB,QAAW;AAC5C,cAAQ,KAAK,OAAO,cAAc,gBAAgB,gBAAgB,UAAU,gBAAgB,CAAC;AAAA,IAC/F;AACA,QAAI,UAAU,6BAA6B,QAAW;AACpD,qBAAe,qBAAqB,UAAU;AAAA,IAChD;AACA,QAAI,UAAU,8BAA8B,QAAW;AACrD,cAAQ,KAAK,OAAO,cAAc,gBAAgB,yBAAyB,UAAU,yBAAyB,CAAC;AAAA,IACjH;AACA,QAAI,UAAU,2BAA2B,QAAW;AAClD,cAAQ,KAAK,OAAO,cAAc,gBAAgB,sBAAsB,UAAU,sBAAsB,CAAC;AACzG,UAAI,UAAU,uBAAuB,UAAU,QAAW;AACxD,cAAM,QAAQ,UAAU,uBAAuB;AAC/C,uBAAe,uBAAuB,IAAI,QAAQ,OAAO,KAAK;AAAA,MAChE;AAAA,IACF;AACA,WAAO,QAAQ,IAAI,OAAO;AAAA,EAC5B;AACF;AASA,IAAM,mCAAN,MAAuC;AAAA,EACrC,YAAY,QAAQ;AAClB,SAAK,SAAS;AACd,SAAK,OAAO,WAAW;AAAA,EACzB;AAAA,EACA,gBAAgB,eAAe;AAC7B,UAAM,SAAS,KAAK;AACpB,UAAM,cAAc,OAAO,KAAK,UAAU,aAAa;AACvD,QAAI,CAAC,YAAY,cAAc,CAAC,YAAY,WAAW,KAAK,IAAI,EAAG,QAAO;AAC1E,WAAO;AAAA,EACT;AAAA,EACA,qBAAqB,eAAe,gBAAgB;AAClD,UAAM,SAAS,KAAK;AACpB,UAAM,cAAc,OAAO,KAAK,UAAU,aAAa;AACvD,QAAI,CAAC,YAAY,cAAc,CAAC,YAAY,WAAW,KAAK,IAAI,GAAG;AACjE,aAAO,QAAQ,QAAQ;AAAA,IACzB;AACA,UAAM,YAAY,YAAY,WAAW,KAAK,IAAI;AAClD,mBAAe,aAAa,UAAU,eAAe,SAAY,UAAU,aAAa;AACxF,WAAO,QAAQ,QAAQ;AAAA,EACzB;AACF;AASA,IAAM,oCAAN,MAAwC;AAAA,EACtC,YAAY,QAAQ;AAClB,SAAK,SAAS;AACd,SAAK,OAAO,WAAW;AAAA,EACzB;AAAA,EACA,gBAAgB,eAAe;AAC7B,UAAM,SAAS,KAAK;AACpB,UAAM,cAAc,OAAO,KAAK,UAAU,aAAa;AACvD,QAAI,CAAC,YAAY,cAAc,CAAC,YAAY,WAAW,KAAK,IAAI,EAAG,QAAO;AAC1E,WAAO;AAAA,EACT;AAAA,EACA,qBAAqB,eAAe,gBAAgB;AAClD,UAAM,SAAS,KAAK;AACpB,UAAM,cAAc,OAAO,KAAK,UAAU,aAAa;AACvD,QAAI,CAAC,YAAY,cAAc,CAAC,YAAY,WAAW,KAAK,IAAI,GAAG;AACjE,aAAO,QAAQ,QAAQ;AAAA,IACzB;AACA,UAAM,UAAU,CAAC;AACjB,UAAM,YAAY,YAAY,WAAW,KAAK,IAAI;AAClD,QAAI,UAAU,sBAAsB,QAAW;AAC7C,qBAAe,cAAc,UAAU;AAAA,IACzC;AACA,QAAI,UAAU,uBAAuB,QAAW;AAC9C,cAAQ,KAAK,OAAO,cAAc,gBAAgB,kBAAkB,UAAU,kBAAkB,CAAC;AAAA,IACnG;AACA,QAAI,UAAU,mBAAmB,QAAW;AAC1C,qBAAe,iBAAiB,UAAU;AAAA,IAC5C;AACA,QAAI,eAAe,8BAA8B,QAAW;AAC1D,qBAAe,4BAA4B,CAAC,KAAK,GAAG;AAAA,IACtD;AACA,QAAI,UAAU,gCAAgC,QAAW;AACvD,qBAAe,0BAA0B,CAAC,IAAI,UAAU;AAAA,IAC1D;AACA,QAAI,UAAU,gCAAgC,QAAW;AACvD,qBAAe,0BAA0B,CAAC,IAAI,UAAU;AAAA,IAC1D;AACA,QAAI,UAAU,gCAAgC,QAAW;AACvD,cAAQ,KAAK,OAAO,cAAc,gBAAgB,2BAA2B,UAAU,2BAA2B,CAAC;AAAA,IACrH;AACA,WAAO,QAAQ,IAAI,OAAO;AAAA,EAC5B;AACF;AASA,IAAM,8BAAN,MAAkC;AAAA,EAChC,YAAY,QAAQ;AAClB,SAAK,SAAS;AACd,SAAK,OAAO,WAAW;AAAA,EACzB;AAAA,EACA,gBAAgB,eAAe;AAC7B,UAAM,SAAS,KAAK;AACpB,UAAM,cAAc,OAAO,KAAK,UAAU,aAAa;AACvD,QAAI,CAAC,YAAY,cAAc,CAAC,YAAY,WAAW,KAAK,IAAI,EAAG,QAAO;AAC1E,WAAO;AAAA,EACT;AAAA,EACA,qBAAqB,eAAe,gBAAgB;AAClD,UAAM,SAAS,KAAK;AACpB,UAAM,cAAc,OAAO,KAAK,UAAU,aAAa;AACvD,QAAI,CAAC,YAAY,cAAc,CAAC,YAAY,WAAW,KAAK,IAAI,GAAG;AACjE,aAAO,QAAQ,QAAQ;AAAA,IACzB;AACA,UAAM,UAAU,CAAC;AACjB,mBAAe,aAAa,IAAI,MAAM,GAAG,GAAG,CAAC;AAC7C,mBAAe,iBAAiB;AAChC,mBAAe,QAAQ;AACvB,UAAM,YAAY,YAAY,WAAW,KAAK,IAAI;AAClD,QAAI,UAAU,qBAAqB,QAAW;AAC5C,YAAM,cAAc,UAAU;AAC9B,qBAAe,WAAW,OAAO,YAAY,CAAC,GAAG,YAAY,CAAC,GAAG,YAAY,CAAC,GAAG,oBAAoB;AAAA,IACvG;AACA,QAAI,UAAU,yBAAyB,QAAW;AAChD,qBAAe,iBAAiB,UAAU;AAAA,IAC5C;AACA,QAAI,UAAU,sBAAsB,QAAW;AAC7C,cAAQ,KAAK,OAAO,cAAc,gBAAgB,iBAAiB,UAAU,mBAAmB,cAAc,CAAC;AAAA,IACjH;AACA,QAAI,UAAU,0BAA0B,QAAW;AACjD,cAAQ,KAAK,OAAO,cAAc,gBAAgB,qBAAqB,UAAU,qBAAqB,CAAC;AAAA,IACzG;AACA,WAAO,QAAQ,IAAI,OAAO;AAAA,EAC5B;AACF;AAUA,IAAM,qCAAN,MAAyC;AAAA,EACvC,YAAY,QAAQ;AAClB,SAAK,SAAS;AACd,SAAK,OAAO,WAAW;AAAA,EACzB;AAAA,EACA,gBAAgB,eAAe;AAC7B,UAAM,SAAS,KAAK;AACpB,UAAM,cAAc,OAAO,KAAK,UAAU,aAAa;AACvD,QAAI,CAAC,YAAY,cAAc,CAAC,YAAY,WAAW,KAAK,IAAI,EAAG,QAAO;AAC1E,WAAO;AAAA,EACT;AAAA,EACA,qBAAqB,eAAe,gBAAgB;AAClD,UAAM,SAAS,KAAK;AACpB,UAAM,cAAc,OAAO,KAAK,UAAU,aAAa;AACvD,QAAI,CAAC,YAAY,cAAc,CAAC,YAAY,WAAW,KAAK,IAAI,GAAG;AACjE,aAAO,QAAQ,QAAQ;AAAA,IACzB;AACA,UAAM,UAAU,CAAC;AACjB,UAAM,YAAY,YAAY,WAAW,KAAK,IAAI;AAClD,QAAI,UAAU,uBAAuB,QAAW;AAC9C,qBAAe,eAAe,UAAU;AAAA,IAC1C;AACA,QAAI,UAAU,wBAAwB,QAAW;AAC/C,cAAQ,KAAK,OAAO,cAAc,gBAAgB,mBAAmB,UAAU,mBAAmB,CAAC;AAAA,IACrG;AACA,WAAO,QAAQ,IAAI,OAAO;AAAA,EAC5B;AACF;AASA,IAAM,+BAAN,MAAmC;AAAA,EACjC,YAAY,QAAQ;AAClB,SAAK,SAAS;AACd,SAAK,OAAO,WAAW;AAAA,EACzB;AAAA,EACA,gBAAgB,eAAe;AAC7B,UAAM,SAAS,KAAK;AACpB,UAAM,cAAc,OAAO,KAAK,UAAU,aAAa;AACvD,QAAI,CAAC,YAAY,cAAc,CAAC,YAAY,WAAW,KAAK,IAAI,EAAG,QAAO;AAC1E,WAAO;AAAA,EACT;AAAA,EACA,qBAAqB,eAAe,gBAAgB;AAClD,UAAM,SAAS,KAAK;AACpB,UAAM,cAAc,OAAO,KAAK,UAAU,aAAa;AACvD,QAAI,CAAC,YAAY,cAAc,CAAC,YAAY,WAAW,KAAK,IAAI,GAAG;AACjE,aAAO,QAAQ,QAAQ;AAAA,IACzB;AACA,UAAM,UAAU,CAAC;AACjB,UAAM,YAAY,YAAY,WAAW,KAAK,IAAI;AAClD,mBAAe,YAAY,UAAU,oBAAoB,SAAY,UAAU,kBAAkB;AACjG,QAAI,UAAU,qBAAqB,QAAW;AAC5C,cAAQ,KAAK,OAAO,cAAc,gBAAgB,gBAAgB,UAAU,gBAAgB,CAAC;AAAA,IAC/F;AACA,mBAAe,sBAAsB,UAAU,uBAAuB;AACtE,UAAM,aAAa,UAAU,oBAAoB,CAAC,GAAG,GAAG,CAAC;AACzD,mBAAe,mBAAmB,IAAI,MAAM,EAAE,OAAO,WAAW,CAAC,GAAG,WAAW,CAAC,GAAG,WAAW,CAAC,GAAG,oBAAoB;AACtH,WAAO,QAAQ,IAAI,OAAO;AAAA,EAC5B;AACF;AASA,IAAM,4BAAN,MAAgC;AAAA,EAC9B,YAAY,QAAQ;AAClB,SAAK,SAAS;AACd,SAAK,OAAO,WAAW;AAAA,EACzB;AAAA,EACA,gBAAgB,eAAe;AAC7B,UAAM,SAAS,KAAK;AACpB,UAAM,cAAc,OAAO,KAAK,UAAU,aAAa;AACvD,QAAI,CAAC,YAAY,cAAc,CAAC,YAAY,WAAW,KAAK,IAAI,EAAG,QAAO;AAC1E,WAAO;AAAA,EACT;AAAA,EACA,qBAAqB,eAAe,gBAAgB;AAClD,UAAM,SAAS,KAAK;AACpB,UAAM,cAAc,OAAO,KAAK,UAAU,aAAa;AACvD,QAAI,CAAC,YAAY,cAAc,CAAC,YAAY,WAAW,KAAK,IAAI,GAAG;AACjE,aAAO,QAAQ,QAAQ;AAAA,IACzB;AACA,UAAM,YAAY,YAAY,WAAW,KAAK,IAAI;AAClD,mBAAe,MAAM,UAAU,QAAQ,SAAY,UAAU,MAAM;AACnE,WAAO,QAAQ,QAAQ;AAAA,EACzB;AACF;AASA,IAAM,iCAAN,MAAqC;AAAA,EACnC,YAAY,QAAQ;AAClB,SAAK,SAAS;AACd,SAAK,OAAO,WAAW;AAAA,EACzB;AAAA,EACA,gBAAgB,eAAe;AAC7B,UAAM,SAAS,KAAK;AACpB,UAAM,cAAc,OAAO,KAAK,UAAU,aAAa;AACvD,QAAI,CAAC,YAAY,cAAc,CAAC,YAAY,WAAW,KAAK,IAAI,EAAG,QAAO;AAC1E,WAAO;AAAA,EACT;AAAA,EACA,qBAAqB,eAAe,gBAAgB;AAClD,UAAM,SAAS,KAAK;AACpB,UAAM,cAAc,OAAO,KAAK,UAAU,aAAa;AACvD,QAAI,CAAC,YAAY,cAAc,CAAC,YAAY,WAAW,KAAK,IAAI,GAAG;AACjE,aAAO,QAAQ,QAAQ;AAAA,IACzB;AACA,UAAM,UAAU,CAAC;AACjB,UAAM,YAAY,YAAY,WAAW,KAAK,IAAI;AAClD,mBAAe,oBAAoB,UAAU,mBAAmB,SAAY,UAAU,iBAAiB;AACvG,QAAI,UAAU,oBAAoB,QAAW;AAC3C,cAAQ,KAAK,OAAO,cAAc,gBAAgB,wBAAwB,UAAU,eAAe,CAAC;AAAA,IACtG;AACA,UAAM,aAAa,UAAU,uBAAuB,CAAC,GAAG,GAAG,CAAC;AAC5D,mBAAe,gBAAgB,IAAI,MAAM,EAAE,OAAO,WAAW,CAAC,GAAG,WAAW,CAAC,GAAG,WAAW,CAAC,GAAG,oBAAoB;AACnH,QAAI,UAAU,yBAAyB,QAAW;AAChD,cAAQ,KAAK,OAAO,cAAc,gBAAgB,oBAAoB,UAAU,sBAAsB,cAAc,CAAC;AAAA,IACvH;AACA,WAAO,QAAQ,IAAI,OAAO;AAAA,EAC5B;AACF;AASA,IAAM,6BAAN,MAAiC;AAAA,EAC/B,YAAY,QAAQ;AAClB,SAAK,SAAS;AACd,SAAK,OAAO,WAAW;AAAA,EACzB;AAAA,EACA,gBAAgB,eAAe;AAC7B,UAAM,SAAS,KAAK;AACpB,UAAM,cAAc,OAAO,KAAK,UAAU,aAAa;AACvD,QAAI,CAAC,YAAY,cAAc,CAAC,YAAY,WAAW,KAAK,IAAI,EAAG,QAAO;AAC1E,WAAO;AAAA,EACT;AAAA,EACA,qBAAqB,eAAe,gBAAgB;AAClD,UAAM,SAAS,KAAK;AACpB,UAAM,cAAc,OAAO,KAAK,UAAU,aAAa;AACvD,QAAI,CAAC,YAAY,cAAc,CAAC,YAAY,WAAW,KAAK,IAAI,GAAG;AACjE,aAAO,QAAQ,QAAQ;AAAA,IACzB;AACA,UAAM,UAAU,CAAC;AACjB,UAAM,YAAY,YAAY,WAAW,KAAK,IAAI;AAClD,mBAAe,YAAY,UAAU,eAAe,SAAY,UAAU,aAAa;AACvF,QAAI,UAAU,gBAAgB,QAAW;AACvC,cAAQ,KAAK,OAAO,cAAc,gBAAgB,WAAW,UAAU,WAAW,CAAC;AAAA,IACrF;AACA,WAAO,QAAQ,IAAI,OAAO;AAAA,EAC5B;AACF;AASA,IAAM,mCAAN,MAAuC;AAAA,EACrC,YAAY,QAAQ;AAClB,SAAK,SAAS;AACd,SAAK,OAAO,WAAW;AAAA,EACzB;AAAA,EACA,gBAAgB,eAAe;AAC7B,UAAM,SAAS,KAAK;AACpB,UAAM,cAAc,OAAO,KAAK,UAAU,aAAa;AACvD,QAAI,CAAC,YAAY,cAAc,CAAC,YAAY,WAAW,KAAK,IAAI,EAAG,QAAO;AAC1E,WAAO;AAAA,EACT;AAAA,EACA,qBAAqB,eAAe,gBAAgB;AAClD,UAAM,SAAS,KAAK;AACpB,UAAM,cAAc,OAAO,KAAK,UAAU,aAAa;AACvD,QAAI,CAAC,YAAY,cAAc,CAAC,YAAY,WAAW,KAAK,IAAI,GAAG;AACjE,aAAO,QAAQ,QAAQ;AAAA,IACzB;AACA,UAAM,UAAU,CAAC;AACjB,UAAM,YAAY,YAAY,WAAW,KAAK,IAAI;AAClD,QAAI,UAAU,uBAAuB,QAAW;AAC9C,qBAAe,aAAa,UAAU;AAAA,IACxC;AACA,QAAI,UAAU,uBAAuB,QAAW;AAC9C,qBAAe,qBAAqB,UAAU;AAAA,IAChD;AACA,QAAI,UAAU,sBAAsB,QAAW;AAC7C,cAAQ,KAAK,OAAO,cAAc,gBAAgB,iBAAiB,UAAU,iBAAiB,CAAC;AAAA,IACjG;AACA,WAAO,QAAQ,IAAI,OAAO;AAAA,EAC5B;AACF;AASA,IAAM,6BAAN,MAAiC;AAAA,EAC/B,YAAY,QAAQ;AAClB,SAAK,SAAS;AACd,SAAK,OAAO,WAAW;AAAA,EACzB;AAAA,EACA,YAAY,cAAc;AACxB,UAAM,SAAS,KAAK;AACpB,UAAM,OAAO,OAAO;AACpB,UAAM,aAAa,KAAK,SAAS,YAAY;AAC7C,QAAI,CAAC,WAAW,cAAc,CAAC,WAAW,WAAW,KAAK,IAAI,GAAG;AAC/D,aAAO;AAAA,IACT;AACA,UAAM,YAAY,WAAW,WAAW,KAAK,IAAI;AACjD,UAAM,SAAS,OAAO,QAAQ;AAC9B,QAAI,CAAC,QAAQ;AACX,UAAI,KAAK,sBAAsB,KAAK,mBAAmB,QAAQ,KAAK,IAAI,KAAK,GAAG;AAC9E,cAAM,IAAI,MAAM,6EAA6E;AAAA,MAC/F,OAAO;AAEL,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO,OAAO,iBAAiB,cAAc,UAAU,QAAQ,MAAM;AAAA,EACvE;AACF;AASA,IAAM,2BAAN,MAA+B;AAAA,EAC7B,YAAY,QAAQ;AAClB,SAAK,SAAS;AACd,SAAK,OAAO,WAAW;AACvB,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,YAAY,cAAc;AACxB,UAAM,OAAO,KAAK;AAClB,UAAM,SAAS,KAAK;AACpB,UAAM,OAAO,OAAO;AACpB,UAAM,aAAa,KAAK,SAAS,YAAY;AAC7C,QAAI,CAAC,WAAW,cAAc,CAAC,WAAW,WAAW,IAAI,GAAG;AAC1D,aAAO;AAAA,IACT;AACA,UAAM,YAAY,WAAW,WAAW,IAAI;AAC5C,UAAM,SAAS,KAAK,OAAO,UAAU,MAAM;AAC3C,QAAI,SAAS,OAAO;AACpB,QAAI,OAAO,KAAK;AACd,YAAM,UAAU,OAAO,QAAQ,QAAQ,WAAW,OAAO,GAAG;AAC5D,UAAI,YAAY,KAAM,UAAS;AAAA,IACjC;AACA,WAAO,KAAK,cAAc,EAAE,KAAK,SAAU,aAAa;AACtD,UAAI,YAAa,QAAO,OAAO,iBAAiB,cAAc,UAAU,QAAQ,MAAM;AACtF,UAAI,KAAK,sBAAsB,KAAK,mBAAmB,QAAQ,IAAI,KAAK,GAAG;AACzE,cAAM,IAAI,MAAM,2DAA2D;AAAA,MAC7E;AAGA,aAAO,OAAO,YAAY,YAAY;AAAA,IACxC,CAAC;AAAA,EACH;AAAA,EACA,gBAAgB;AACd,QAAI,CAAC,KAAK,aAAa;AACrB,WAAK,cAAc,IAAI,QAAQ,SAAU,SAAS;AAChD,cAAM,QAAQ,IAAI,MAAM;AAIxB,cAAM,MAAM;AACZ,cAAM,SAAS,MAAM,UAAU,WAAY;AACzC,kBAAQ,MAAM,WAAW,CAAC;AAAA,QAC5B;AAAA,MACF,CAAC;AAAA,IACH;AACA,WAAO,KAAK;AAAA,EACd;AACF;AASA,IAAM,2BAAN,MAA+B;AAAA,EAC7B,YAAY,QAAQ;AAClB,SAAK,SAAS;AACd,SAAK,OAAO,WAAW;AACvB,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,YAAY,cAAc;AACxB,UAAM,OAAO,KAAK;AAClB,UAAM,SAAS,KAAK;AACpB,UAAM,OAAO,OAAO;AACpB,UAAM,aAAa,KAAK,SAAS,YAAY;AAC7C,QAAI,CAAC,WAAW,cAAc,CAAC,WAAW,WAAW,IAAI,GAAG;AAC1D,aAAO;AAAA,IACT;AACA,UAAM,YAAY,WAAW,WAAW,IAAI;AAC5C,UAAM,SAAS,KAAK,OAAO,UAAU,MAAM;AAC3C,QAAI,SAAS,OAAO;AACpB,QAAI,OAAO,KAAK;AACd,YAAM,UAAU,OAAO,QAAQ,QAAQ,WAAW,OAAO,GAAG;AAC5D,UAAI,YAAY,KAAM,UAAS;AAAA,IACjC;AACA,WAAO,KAAK,cAAc,EAAE,KAAK,SAAU,aAAa;AACtD,UAAI,YAAa,QAAO,OAAO,iBAAiB,cAAc,UAAU,QAAQ,MAAM;AACtF,UAAI,KAAK,sBAAsB,KAAK,mBAAmB,QAAQ,IAAI,KAAK,GAAG;AACzE,cAAM,IAAI,MAAM,2DAA2D;AAAA,MAC7E;AAGA,aAAO,OAAO,YAAY,YAAY;AAAA,IACxC,CAAC;AAAA,EACH;AAAA,EACA,gBAAgB;AACd,QAAI,CAAC,KAAK,aAAa;AACrB,WAAK,cAAc,IAAI,QAAQ,SAAU,SAAS;AAChD,cAAM,QAAQ,IAAI,MAAM;AAGxB,cAAM,MAAM;AACZ,cAAM,SAAS,MAAM,UAAU,WAAY;AACzC,kBAAQ,MAAM,WAAW,CAAC;AAAA,QAC5B;AAAA,MACF,CAAC;AAAA,IACH;AACA,WAAO,KAAK;AAAA,EACd;AACF;AASA,IAAM,yBAAN,MAA6B;AAAA,EAC3B,YAAY,QAAQ;AAClB,SAAK,OAAO,WAAW;AACvB,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,eAAe,OAAO;AACpB,UAAM,OAAO,KAAK,OAAO;AACzB,UAAM,aAAa,KAAK,YAAY,KAAK;AACzC,QAAI,WAAW,cAAc,WAAW,WAAW,KAAK,IAAI,GAAG;AAC7D,YAAM,eAAe,WAAW,WAAW,KAAK,IAAI;AACpD,YAAM,SAAS,KAAK,OAAO,cAAc,UAAU,aAAa,MAAM;AACtE,YAAM,UAAU,KAAK,OAAO,QAAQ;AACpC,UAAI,CAAC,WAAW,CAAC,QAAQ,WAAW;AAClC,YAAI,KAAK,sBAAsB,KAAK,mBAAmB,QAAQ,KAAK,IAAI,KAAK,GAAG;AAC9E,gBAAM,IAAI,MAAM,oFAAoF;AAAA,QACtG,OAAO;AAEL,iBAAO;AAAA,QACT;AAAA,MACF;AACA,aAAO,OAAO,KAAK,SAAU,KAAK;AAChC,cAAM,aAAa,aAAa,cAAc;AAC9C,cAAM,aAAa,aAAa,cAAc;AAC9C,cAAM,QAAQ,aAAa;AAC3B,cAAM,SAAS,aAAa;AAC5B,cAAM,SAAS,IAAI,WAAW,KAAK,YAAY,UAAU;AACzD,YAAI,QAAQ,uBAAuB;AACjC,iBAAO,QAAQ,sBAAsB,OAAO,QAAQ,QAAQ,aAAa,MAAM,aAAa,MAAM,EAAE,KAAK,SAAUC,MAAK;AACtH,mBAAOA,KAAI;AAAA,UACb,CAAC;AAAA,QACH,OAAO;AAEL,iBAAO,QAAQ,MAAM,KAAK,WAAY;AACpC,kBAAM,SAAS,IAAI,YAAY,QAAQ,MAAM;AAC7C,oBAAQ,iBAAiB,IAAI,WAAW,MAAM,GAAG,OAAO,QAAQ,QAAQ,aAAa,MAAM,aAAa,MAAM;AAC9G,mBAAO;AAAA,UACT,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACH,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AACF;AASA,IAAM,wBAAN,MAA4B;AAAA,EAC1B,YAAY,QAAQ;AAClB,SAAK,OAAO,WAAW;AACvB,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,eAAe,WAAW;AACxB,UAAM,OAAO,KAAK,OAAO;AACzB,UAAM,UAAU,KAAK,MAAM,SAAS;AACpC,QAAI,CAAC,QAAQ,cAAc,CAAC,QAAQ,WAAW,KAAK,IAAI,KAAK,QAAQ,SAAS,QAAW;AACvF,aAAO;AAAA,IACT;AACA,UAAM,UAAU,KAAK,OAAO,QAAQ,IAAI;AAIxC,eAAW,aAAa,QAAQ,YAAY;AAC1C,UAAI,UAAU,SAAS,gBAAgB,aAAa,UAAU,SAAS,gBAAgB,kBAAkB,UAAU,SAAS,gBAAgB,gBAAgB,UAAU,SAAS,QAAW;AACxL,eAAO;AAAA,MACT;AAAA,IACF;AACA,UAAM,eAAe,QAAQ,WAAW,KAAK,IAAI;AACjD,UAAM,gBAAgB,aAAa;AAInC,UAAM,UAAU,CAAC;AACjB,UAAM,aAAa,CAAC;AACpB,eAAW,OAAO,eAAe;AAC/B,cAAQ,KAAK,KAAK,OAAO,cAAc,YAAY,cAAc,GAAG,CAAC,EAAE,KAAK,cAAY;AACtF,mBAAW,GAAG,IAAI;AAClB,eAAO,WAAW,GAAG;AAAA,MACvB,CAAC,CAAC;AAAA,IACJ;AACA,QAAI,QAAQ,SAAS,GAAG;AACtB,aAAO;AAAA,IACT;AACA,YAAQ,KAAK,KAAK,OAAO,eAAe,SAAS,CAAC;AAClD,WAAO,QAAQ,IAAI,OAAO,EAAE,KAAK,aAAW;AAC1C,YAAM,aAAa,QAAQ,IAAI;AAC/B,YAAM,SAAS,WAAW,UAAU,WAAW,WAAW,CAAC,UAAU;AACrE,YAAM,QAAQ,QAAQ,CAAC,EAAE;AACzB,YAAM,kBAAkB,CAAC;AACzB,iBAAW,QAAQ,QAAQ;AAEzB,cAAM,IAAI,IAAI,QAAQ;AACtB,cAAM,IAAI,IAAI,QAAQ;AACtB,cAAM,IAAI,IAAI,WAAW;AACzB,cAAM,IAAI,IAAI,QAAQ,GAAG,GAAG,CAAC;AAC7B,cAAM,gBAAgB,IAAI,cAAc,KAAK,UAAU,KAAK,UAAU,KAAK;AAC3E,iBAAS,IAAI,GAAG,IAAI,OAAO,KAAK;AAC9B,cAAI,WAAW,aAAa;AAC1B,cAAE,oBAAoB,WAAW,aAAa,CAAC;AAAA,UACjD;AACA,cAAI,WAAW,UAAU;AACvB,cAAE,oBAAoB,WAAW,UAAU,CAAC;AAAA,UAC9C;AACA,cAAI,WAAW,OAAO;AACpB,cAAE,oBAAoB,WAAW,OAAO,CAAC;AAAA,UAC3C;AACA,wBAAc,YAAY,GAAG,EAAE,QAAQ,GAAG,GAAG,CAAC,CAAC;AAAA,QACjD;AAGA,mBAAW,iBAAiB,YAAY;AACtC,cAAI,kBAAkB,YAAY;AAChC,kBAAM,OAAO,WAAW,aAAa;AACrC,0BAAc,gBAAgB,IAAI,yBAAyB,KAAK,OAAO,KAAK,UAAU,KAAK,UAAU;AAAA,UACvG,WAAW,kBAAkB,iBAAiB,kBAAkB,cAAc,kBAAkB,SAAS;AACvG,iBAAK,SAAS,aAAa,eAAe,WAAW,aAAa,CAAC;AAAA,UACrE;AAAA,QACF;AAGA,iBAAS,UAAU,KAAK,KAAK,eAAe,IAAI;AAChD,aAAK,OAAO,oBAAoB,aAAa;AAC7C,wBAAgB,KAAK,aAAa;AAAA,MACpC;AACA,UAAI,WAAW,SAAS;AACtB,mBAAW,MAAM;AACjB,mBAAW,IAAI,GAAG,eAAe;AACjC,eAAO;AAAA,MACT;AACA,aAAO,gBAAgB,CAAC;AAAA,IAC1B,CAAC;AAAA,EACH;AACF;AAGA,IAAM,gCAAgC;AACtC,IAAM,iCAAiC;AACvC,IAAM,+BAA+B;AAAA,EACnC,MAAM;AAAA,EACN,KAAK;AACP;AACA,IAAM,sBAAN,MAA0B;AAAA,EACxB,YAAY,MAAM;AAChB,SAAK,OAAO,WAAW;AACvB,SAAK,UAAU;AACf,SAAK,OAAO;AACZ,UAAM,aAAa,IAAI,SAAS,MAAM,GAAG,8BAA8B;AACvE,UAAM,cAAc,IAAI,YAAY;AACpC,SAAK,SAAS;AAAA,MACZ,OAAO,YAAY,OAAO,IAAI,WAAW,KAAK,MAAM,GAAG,CAAC,CAAC,CAAC;AAAA,MAC1D,SAAS,WAAW,UAAU,GAAG,IAAI;AAAA,MACrC,QAAQ,WAAW,UAAU,GAAG,IAAI;AAAA,IACtC;AACA,QAAI,KAAK,OAAO,UAAU,+BAA+B;AACvD,YAAM,IAAI,MAAM,mDAAmD;AAAA,IACrE,WAAW,KAAK,OAAO,UAAU,GAAK;AACpC,YAAM,IAAI,MAAM,gDAAgD;AAAA,IAClE;AACA,UAAM,sBAAsB,KAAK,OAAO,SAAS;AACjD,UAAM,YAAY,IAAI,SAAS,MAAM,8BAA8B;AACnE,QAAI,aAAa;AACjB,WAAO,aAAa,qBAAqB;AACvC,YAAM,cAAc,UAAU,UAAU,YAAY,IAAI;AACxD,oBAAc;AACd,YAAM,YAAY,UAAU,UAAU,YAAY,IAAI;AACtD,oBAAc;AACd,UAAI,cAAc,6BAA6B,MAAM;AACnD,cAAM,eAAe,IAAI,WAAW,MAAM,iCAAiC,YAAY,WAAW;AAClG,aAAK,UAAU,YAAY,OAAO,YAAY;AAAA,MAChD,WAAW,cAAc,6BAA6B,KAAK;AACzD,cAAM,aAAa,iCAAiC;AACpD,aAAK,OAAO,KAAK,MAAM,YAAY,aAAa,WAAW;AAAA,MAC7D;AAIA,oBAAc;AAAA,IAChB;AACA,QAAI,KAAK,YAAY,MAAM;AACzB,YAAM,IAAI,MAAM,2CAA2C;AAAA,IAC7D;AAAA,EACF;AACF;AASA,IAAM,oCAAN,MAAwC;AAAA,EACtC,YAAY,MAAM,aAAa;AAC7B,QAAI,CAAC,aAAa;AAChB,YAAM,IAAI,MAAM,qDAAqD;AAAA,IACvE;AACA,SAAK,OAAO,WAAW;AACvB,SAAK,OAAO;AACZ,SAAK,cAAc;AACnB,SAAK,YAAY,QAAQ;AAAA,EAC3B;AAAA,EACA,gBAAgB,WAAW,QAAQ;AACjC,UAAM,OAAO,KAAK;AAClB,UAAM,cAAc,KAAK;AACzB,UAAM,kBAAkB,UAAU,WAAW,KAAK,IAAI,EAAE;AACxD,UAAM,mBAAmB,UAAU,WAAW,KAAK,IAAI,EAAE;AACzD,UAAM,oBAAoB,CAAC;AAC3B,UAAM,yBAAyB,CAAC;AAChC,UAAM,mBAAmB,CAAC;AAC1B,eAAW,iBAAiB,kBAAkB;AAC5C,YAAM,qBAAqB,WAAW,aAAa,KAAK,cAAc,YAAY;AAClF,wBAAkB,kBAAkB,IAAI,iBAAiB,aAAa;AAAA,IACxE;AACA,eAAW,iBAAiB,UAAU,YAAY;AAChD,YAAM,qBAAqB,WAAW,aAAa,KAAK,cAAc,YAAY;AAClF,UAAI,iBAAiB,aAAa,MAAM,QAAW;AACjD,cAAM,cAAc,KAAK,UAAU,UAAU,WAAW,aAAa,CAAC;AACtE,cAAM,gBAAgB,sBAAsB,YAAY,aAAa;AACrE,yBAAiB,kBAAkB,IAAI,cAAc;AACrD,+BAAuB,kBAAkB,IAAI,YAAY,eAAe;AAAA,MAC1E;AAAA,IACF;AACA,WAAO,OAAO,cAAc,cAAc,eAAe,EAAE,KAAK,SAAU,YAAY;AACpF,aAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC5C,oBAAY,gBAAgB,YAAY,SAAU,UAAU;AAC1D,qBAAW,iBAAiB,SAAS,YAAY;AAC/C,kBAAM,YAAY,SAAS,WAAW,aAAa;AACnD,kBAAM,aAAa,uBAAuB,aAAa;AACvD,gBAAI,eAAe,OAAW,WAAU,aAAa;AAAA,UACvD;AACA,kBAAQ,QAAQ;AAAA,QAClB,GAAG,mBAAmB,kBAAkB,sBAAsB,MAAM;AAAA,MACtE,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACF;AASA,IAAM,gCAAN,MAAoC;AAAA,EAClC,cAAc;AACZ,SAAK,OAAO,WAAW;AAAA,EACzB;AAAA,EACA,cAAc,SAAS,WAAW;AAChC,SAAK,UAAU,aAAa,UAAa,UAAU,aAAa,QAAQ,YAAY,UAAU,WAAW,UAAa,UAAU,aAAa,UAAa,UAAU,UAAU,QAAW;AAEvL,aAAO;AAAA,IACT;AACA,cAAU,QAAQ,MAAM;AACxB,QAAI,UAAU,aAAa,QAAW;AACpC,cAAQ,UAAU,UAAU;AAAA,IAC9B;AACA,QAAI,UAAU,WAAW,QAAW;AAClC,cAAQ,OAAO,UAAU,UAAU,MAAM;AAAA,IAC3C;AACA,QAAI,UAAU,aAAa,QAAW;AACpC,cAAQ,WAAW,UAAU;AAAA,IAC/B;AACA,QAAI,UAAU,UAAU,QAAW;AACjC,cAAQ,OAAO,UAAU,UAAU,KAAK;AAAA,IAC1C;AACA,YAAQ,cAAc;AACtB,WAAO;AAAA,EACT;AACF;AASA,IAAM,gCAAN,MAAoC;AAAA,EAClC,cAAc;AACZ,SAAK,OAAO,WAAW;AAAA,EACzB;AACF;AAQA,IAAM,6BAAN,cAAyC,YAAY;AAAA,EACnD,YAAY,oBAAoB,cAAc,YAAY,cAAc;AACtE,UAAM,oBAAoB,cAAc,YAAY,YAAY;AAAA,EAClE;AAAA,EACA,iBAAiB,OAAO;AAItB,UAAM,SAAS,KAAK,cAClB,SAAS,KAAK,cACd,YAAY,KAAK,WACjB,SAAS,QAAQ,YAAY,IAAI;AACnC,aAAS,IAAI,GAAG,MAAM,WAAW,KAAK;AACpC,aAAO,CAAC,IAAI,OAAO,SAAS,CAAC;AAAA,IAC/B;AACA,WAAO;AAAA,EACT;AAAA,EACA,aAAa,IAAI,IAAI,GAAG,IAAI;AAC1B,UAAM,SAAS,KAAK;AACpB,UAAM,SAAS,KAAK;AACpB,UAAM,SAAS,KAAK;AACpB,UAAM,UAAU,SAAS;AACzB,UAAM,UAAU,SAAS;AACzB,UAAM,KAAK,KAAK;AAChB,UAAM,KAAK,IAAI,MAAM;AACrB,UAAM,KAAK,IAAI;AACf,UAAM,MAAM,KAAK;AACjB,UAAM,UAAU,KAAK;AACrB,UAAM,UAAU,UAAU;AAC1B,UAAM,KAAK,KAAK,MAAM,IAAI;AAC1B,UAAM,KAAK,MAAM;AACjB,UAAM,KAAK,IAAI;AACf,UAAM,KAAK,KAAK,KAAK;AAIrB,aAAS,IAAI,GAAG,MAAM,QAAQ,KAAK;AACjC,YAAM,KAAK,OAAO,UAAU,IAAI,MAAM;AACtC,YAAM,KAAK,OAAO,UAAU,IAAI,OAAO,IAAI;AAC3C,YAAM,KAAK,OAAO,UAAU,IAAI,MAAM;AACtC,YAAM,KAAK,OAAO,UAAU,CAAC,IAAI;AAEjC,aAAO,CAAC,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK;AAAA,IACjD;AACA,WAAO;AAAA,EACT;AACF;AACA,IAAM,KAAK,IAAI,WAAW;AAC1B,IAAM,uCAAN,cAAmD,2BAA2B;AAAA,EAC5E,aAAa,IAAI,IAAI,GAAG,IAAI;AAC1B,UAAM,SAAS,MAAM,aAAa,IAAI,IAAI,GAAG,EAAE;AAC/C,OAAG,UAAU,MAAM,EAAE,UAAU,EAAE,QAAQ,MAAM;AAC/C,WAAO;AAAA,EACT;AACF;AAQA,IAAM,kBAAkB;AAAA,EACtB,OAAO;AAAA;AAAA,EAEP,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,gBAAgB;AAAA,EAChB,cAAc;AAAA,EACd,eAAe;AAAA,EACf,gBAAgB;AAClB;AACA,IAAM,wBAAwB;AAAA,EAC5B,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AACR;AACA,IAAM,gBAAgB;AAAA,EACpB,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AACR;AACA,IAAM,kBAAkB;AAAA,EACtB,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AACT;AACA,IAAM,mBAAmB;AAAA,EACvB,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AACV;AACA,IAAM,aAAa;AAAA,EACjB,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,WAAW;AAAA,EACX,UAAU;AACZ;AACA,IAAM,kBAAkB;AAAA,EACtB,OAAO;AAAA,EACP,aAAa;AAAA,EACb,UAAU;AAAA,EACV,SAAS;AACX;AACA,IAAM,gBAAgB;AAAA,EACpB,aAAa;AAAA;AAAA;AAAA,EAGb,QAAQ;AAAA,EACR,MAAM;AACR;AACA,IAAM,cAAc;AAAA,EAClB,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,OAAO;AACT;AASA,SAAS,sBAAsB,OAAO;AACpC,MAAI,MAAM,iBAAiB,MAAM,QAAW;AAC1C,UAAM,iBAAiB,IAAI,IAAI,qBAAqB;AAAA,MAClD,OAAO;AAAA,MACP,UAAU;AAAA,MACV,WAAW;AAAA,MACX,WAAW;AAAA,MACX,aAAa;AAAA,MACb,WAAW;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH;AACA,SAAO,MAAM,iBAAiB;AAChC;AACA,SAAS,+BAA+B,iBAAiB,QAAQ,WAAW;AAG1E,aAAW,QAAQ,UAAU,YAAY;AACvC,QAAI,gBAAgB,IAAI,MAAM,QAAW;AACvC,aAAO,SAAS,iBAAiB,OAAO,SAAS,kBAAkB,CAAC;AACpE,aAAO,SAAS,eAAe,IAAI,IAAI,UAAU,WAAW,IAAI;AAAA,IAClE;AAAA,EACF;AACF;AAQA,SAAS,uBAAuB,QAAQ,SAAS;AAC/C,MAAI,QAAQ,WAAW,QAAW;AAChC,QAAI,OAAO,QAAQ,WAAW,UAAU;AACtC,aAAO,OAAO,OAAO,UAAU,QAAQ,MAAM;AAAA,IAC/C,OAAO;AACL,cAAQ,KAAK,wDAAwD,QAAQ,MAAM;AAAA,IACrF;AAAA,EACF;AACF;AAWA,SAAS,gBAAgB,UAAU,SAAS,QAAQ;AAClD,MAAI,mBAAmB;AACvB,MAAI,iBAAiB;AACrB,MAAI,gBAAgB;AACpB,WAAS,IAAI,GAAG,KAAK,QAAQ,QAAQ,IAAI,IAAI,KAAK;AAChD,UAAM,SAAS,QAAQ,CAAC;AACxB,QAAI,OAAO,aAAa,OAAW,oBAAmB;AACtD,QAAI,OAAO,WAAW,OAAW,kBAAiB;AAClD,QAAI,OAAO,YAAY,OAAW,iBAAgB;AAClD,QAAI,oBAAoB,kBAAkB,cAAe;AAAA,EAC3D;AACA,MAAI,CAAC,oBAAoB,CAAC,kBAAkB,CAAC,cAAe,QAAO,QAAQ,QAAQ,QAAQ;AAC3F,QAAM,2BAA2B,CAAC;AAClC,QAAM,yBAAyB,CAAC;AAChC,QAAM,wBAAwB,CAAC;AAC/B,WAAS,IAAI,GAAG,KAAK,QAAQ,QAAQ,IAAI,IAAI,KAAK;AAChD,UAAM,SAAS,QAAQ,CAAC;AACxB,QAAI,kBAAkB;AACpB,YAAM,kBAAkB,OAAO,aAAa,SAAY,OAAO,cAAc,YAAY,OAAO,QAAQ,IAAI,SAAS,WAAW;AAChI,+BAAyB,KAAK,eAAe;AAAA,IAC/C;AACA,QAAI,gBAAgB;AAClB,YAAM,kBAAkB,OAAO,WAAW,SAAY,OAAO,cAAc,YAAY,OAAO,MAAM,IAAI,SAAS,WAAW;AAC5H,6BAAuB,KAAK,eAAe;AAAA,IAC7C;AACA,QAAI,eAAe;AACjB,YAAM,kBAAkB,OAAO,YAAY,SAAY,OAAO,cAAc,YAAY,OAAO,OAAO,IAAI,SAAS,WAAW;AAC9H,4BAAsB,KAAK,eAAe;AAAA,IAC5C;AAAA,EACF;AACA,SAAO,QAAQ,IAAI,CAAC,QAAQ,IAAI,wBAAwB,GAAG,QAAQ,IAAI,sBAAsB,GAAG,QAAQ,IAAI,qBAAqB,CAAC,CAAC,EAAE,KAAK,SAAU,WAAW;AAC7J,UAAM,iBAAiB,UAAU,CAAC;AAClC,UAAM,eAAe,UAAU,CAAC;AAChC,UAAM,cAAc,UAAU,CAAC;AAC/B,QAAI,iBAAkB,UAAS,gBAAgB,WAAW;AAC1D,QAAI,eAAgB,UAAS,gBAAgB,SAAS;AACtD,QAAI,cAAe,UAAS,gBAAgB,QAAQ;AACpD,aAAS,uBAAuB;AAChC,WAAO;AAAA,EACT,CAAC;AACH;AAQA,SAAS,mBAAmB,MAAM,SAAS;AACzC,OAAK,mBAAmB;AACxB,MAAI,QAAQ,YAAY,QAAW;AACjC,aAAS,IAAI,GAAG,KAAK,QAAQ,QAAQ,QAAQ,IAAI,IAAI,KAAK;AACxD,WAAK,sBAAsB,CAAC,IAAI,QAAQ,QAAQ,CAAC;AAAA,IACnD;AAAA,EACF;AAGA,MAAI,QAAQ,UAAU,MAAM,QAAQ,QAAQ,OAAO,WAAW,GAAG;AAC/D,UAAM,cAAc,QAAQ,OAAO;AACnC,QAAI,KAAK,sBAAsB,WAAW,YAAY,QAAQ;AAC5D,WAAK,wBAAwB,CAAC;AAC9B,eAAS,IAAI,GAAG,KAAK,YAAY,QAAQ,IAAI,IAAI,KAAK;AACpD,aAAK,sBAAsB,YAAY,CAAC,CAAC,IAAI;AAAA,MAC/C;AAAA,IACF,OAAO;AACL,cAAQ,KAAK,sEAAsE;AAAA,IACrF;AAAA,EACF;AACF;AACA,SAAS,mBAAmB,cAAc;AACxC,MAAI;AACJ,QAAM,iBAAiB,aAAa,cAAc,aAAa,WAAW,WAAW,0BAA0B;AAC/G,MAAI,gBAAgB;AAClB,kBAAc,WAAW,eAAe,aAAa,MAAM,eAAe,UAAU,MAAM,oBAAoB,eAAe,UAAU;AAAA,EACzI,OAAO;AACL,kBAAc,aAAa,UAAU,MAAM,oBAAoB,aAAa,UAAU,IAAI,MAAM,aAAa;AAAA,EAC/G;AACA,MAAI,aAAa,YAAY,QAAW;AACtC,aAAS,IAAI,GAAG,KAAK,aAAa,QAAQ,QAAQ,IAAI,IAAI,KAAK;AAC7D,qBAAe,MAAM,oBAAoB,aAAa,QAAQ,CAAC,CAAC;AAAA,IAClE;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,oBAAoB,YAAY;AACvC,MAAI,gBAAgB;AACpB,QAAM,OAAO,OAAO,KAAK,UAAU,EAAE,KAAK;AAC1C,WAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,IAAI,IAAI,KAAK;AAC7C,qBAAiB,KAAK,CAAC,IAAI,MAAM,WAAW,KAAK,CAAC,CAAC,IAAI;AAAA,EACzD;AACA,SAAO;AACT;AACA,SAAS,4BAA4B,aAAa;AAIhD,UAAQ,aAAa;AAAA,IACnB,KAAK;AACH,aAAO,IAAI;AAAA,IACb,KAAK;AACH,aAAO,IAAI;AAAA,IACb,KAAK;AACH,aAAO,IAAI;AAAA,IACb,KAAK;AACH,aAAO,IAAI;AAAA,IACb;AACE,YAAM,IAAI,MAAM,mEAAmE;AAAA,EACvF;AACF;AACA,SAAS,oBAAoB,KAAK;AAChC,MAAI,IAAI,OAAO,gBAAgB,IAAI,KAAK,IAAI,OAAO,oBAAoB,MAAM,EAAG,QAAO;AACvF,MAAI,IAAI,OAAO,eAAe,IAAI,KAAK,IAAI,OAAO,oBAAoB,MAAM,EAAG,QAAO;AACtF,MAAI,IAAI,OAAO,eAAe,IAAI,KAAK,IAAI,OAAO,oBAAoB,MAAM,EAAG,QAAO;AACtF,SAAO;AACT;AACA,IAAM,kBAAkB,IAAI,QAAQ;AAIpC,IAAM,aAAN,MAAiB;AAAA,EACf,YAAY,OAAO,CAAC,GAAG,UAAU,CAAC,GAAG;AACnC,SAAK,OAAO;AACZ,SAAK,aAAa,CAAC;AACnB,SAAK,UAAU,CAAC;AAChB,SAAK,UAAU;AAGf,SAAK,QAAQ,IAAI,aAAa;AAG9B,SAAK,eAAe,oBAAI,IAAI;AAG5B,SAAK,iBAAiB,CAAC;AAGvB,SAAK,YAAY,CAAC;AAGlB,SAAK,YAAY;AAAA,MACf,MAAM,CAAC;AAAA,MACP,MAAM,CAAC;AAAA,IACT;AACA,SAAK,cAAc;AAAA,MACjB,MAAM,CAAC;AAAA,MACP,MAAM,CAAC;AAAA,IACT;AACA,SAAK,aAAa;AAAA,MAChB,MAAM,CAAC;AAAA,MACP,MAAM,CAAC;AAAA,IACT;AACA,SAAK,cAAc,CAAC;AACpB,SAAK,eAAe,CAAC;AAGrB,SAAK,gBAAgB,CAAC;AAKtB,QAAI,WAAW;AACf,QAAI,gBAAgB;AACpB,QAAI,YAAY;AAChB,QAAI,iBAAiB;AACrB,QAAI,OAAO,cAAc,aAAa;AACpC,YAAM,YAAY,UAAU;AAC5B,iBAAW,iCAAiC,KAAK,SAAS,MAAM;AAChE,YAAM,cAAc,UAAU,MAAM,gBAAgB;AACpD,sBAAgB,YAAY,cAAc,SAAS,YAAY,CAAC,GAAG,EAAE,IAAI;AACzE,kBAAY,UAAU,QAAQ,SAAS,IAAI;AAC3C,uBAAiB,YAAY,UAAU,MAAM,qBAAqB,EAAE,CAAC,IAAI;AAAA,IAC3E;AACA,QAAI,OAAO,sBAAsB,eAAe,YAAY,gBAAgB,MAAM,aAAa,iBAAiB,IAAI;AAClH,WAAK,gBAAgB,IAAI,cAAc,KAAK,QAAQ,OAAO;AAAA,IAC7D,OAAO;AACL,WAAK,gBAAgB,IAAI,kBAAkB,KAAK,QAAQ,OAAO;AAAA,IACjE;AACA,SAAK,cAAc,eAAe,KAAK,QAAQ,WAAW;AAC1D,SAAK,cAAc,iBAAiB,KAAK,QAAQ,aAAa;AAC9D,SAAK,aAAa,IAAI,WAAW,KAAK,QAAQ,OAAO;AACrD,SAAK,WAAW,gBAAgB,aAAa;AAC7C,QAAI,KAAK,QAAQ,gBAAgB,mBAAmB;AAClD,WAAK,WAAW,mBAAmB,IAAI;AAAA,IACzC;AAAA,EACF;AAAA,EACA,cAAc,YAAY;AACxB,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,WAAW,SAAS;AAClB,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,MAAM,QAAQ,SAAS;AACrB,UAAM,SAAS;AACf,UAAM,OAAO,KAAK;AAClB,UAAM,aAAa,KAAK;AAGxB,SAAK,MAAM,UAAU;AACrB,SAAK,YAAY,CAAC;AAGlB,SAAK,WAAW,SAAU,KAAK;AAC7B,aAAO,IAAI,aAAa,IAAI,UAAU;AAAA,IACxC,CAAC;AACD,YAAQ,IAAI,KAAK,WAAW,SAAU,KAAK;AACzC,aAAO,IAAI,cAAc,IAAI,WAAW;AAAA,IAC1C,CAAC,CAAC,EAAE,KAAK,WAAY;AACnB,aAAO,QAAQ,IAAI,CAAC,OAAO,gBAAgB,OAAO,GAAG,OAAO,gBAAgB,WAAW,GAAG,OAAO,gBAAgB,QAAQ,CAAC,CAAC;AAAA,IAC7H,CAAC,EAAE,KAAK,SAAU,cAAc;AAC9B,YAAM,SAAS;AAAA,QACb,OAAO,aAAa,CAAC,EAAE,KAAK,SAAS,CAAC;AAAA,QACtC,QAAQ,aAAa,CAAC;AAAA,QACtB,YAAY,aAAa,CAAC;AAAA,QAC1B,SAAS,aAAa,CAAC;AAAA,QACvB,OAAO,KAAK;AAAA,QACZ;AAAA,QACA,UAAU,CAAC;AAAA,MACb;AACA,qCAA+B,YAAY,QAAQ,IAAI;AACvD,6BAAuB,QAAQ,IAAI;AACnC,aAAO,QAAQ,IAAI,OAAO,WAAW,SAAU,KAAK;AAClD,eAAO,IAAI,aAAa,IAAI,UAAU,MAAM;AAAA,MAC9C,CAAC,CAAC,EAAE,KAAK,WAAY;AACnB,mBAAW,SAAS,OAAO,QAAQ;AACjC,gBAAM,kBAAkB;AAAA,QAC1B;AACA,eAAO,MAAM;AAAA,MACf,CAAC;AAAA,IACH,CAAC,EAAE,MAAM,OAAO;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY;AACV,UAAM,WAAW,KAAK,KAAK,SAAS,CAAC;AACrC,UAAM,WAAW,KAAK,KAAK,SAAS,CAAC;AACrC,UAAM,WAAW,KAAK,KAAK,UAAU,CAAC;AAItC,aAAS,YAAY,GAAG,aAAa,SAAS,QAAQ,YAAY,YAAY,aAAa;AACzF,YAAM,SAAS,SAAS,SAAS,EAAE;AACnC,eAAS,IAAI,GAAG,KAAK,OAAO,QAAQ,IAAI,IAAI,KAAK;AAC/C,iBAAS,OAAO,CAAC,CAAC,EAAE,SAAS;AAAA,MAC/B;AAAA,IACF;AAIA,aAAS,YAAY,GAAG,aAAa,SAAS,QAAQ,YAAY,YAAY,aAAa;AACzF,YAAM,UAAU,SAAS,SAAS;AAClC,UAAI,QAAQ,SAAS,QAAW;AAC9B,aAAK,YAAY,KAAK,WAAW,QAAQ,IAAI;AAK7C,YAAI,QAAQ,SAAS,QAAW;AAC9B,mBAAS,QAAQ,IAAI,EAAE,gBAAgB;AAAA,QACzC;AAAA,MACF;AACA,UAAI,QAAQ,WAAW,QAAW;AAChC,aAAK,YAAY,KAAK,aAAa,QAAQ,MAAM;AAAA,MACnD;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeA,YAAY,OAAO,OAAO;AACxB,QAAI,UAAU,OAAW;AACzB,QAAI,MAAM,KAAK,KAAK,MAAM,QAAW;AACnC,YAAM,KAAK,KAAK,IAAI,MAAM,KAAK,KAAK,IAAI;AAAA,IAC1C;AACA,UAAM,KAAK,KAAK;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,YAAY,OAAO,OAAO,QAAQ;AAChC,QAAI,MAAM,KAAK,KAAK,KAAK,EAAG,QAAO;AACnC,UAAM,MAAM,OAAO,MAAM;AAIzB,UAAM,iBAAiB,CAAC,UAAU,UAAU;AAC1C,YAAM,WAAW,KAAK,aAAa,IAAI,QAAQ;AAC/C,UAAI,YAAY,MAAM;AACpB,aAAK,aAAa,IAAI,OAAO,QAAQ;AAAA,MACvC;AACA,iBAAW,CAAC,GAAG,KAAK,KAAK,SAAS,SAAS,QAAQ,GAAG;AACpD,uBAAe,OAAO,MAAM,SAAS,CAAC,CAAC;AAAA,MACzC;AAAA,IACF;AACA,mBAAe,QAAQ,GAAG;AAC1B,QAAI,QAAQ,eAAe,MAAM,KAAK,KAAK;AAC3C,WAAO;AAAA,EACT;AAAA,EACA,WAAW,MAAM;AACf,UAAM,aAAa,OAAO,OAAO,KAAK,OAAO;AAC7C,eAAW,KAAK,IAAI;AACpB,aAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAC1C,YAAM,SAAS,KAAK,WAAW,CAAC,CAAC;AACjC,UAAI,OAAQ,QAAO;AAAA,IACrB;AACA,WAAO;AAAA,EACT;AAAA,EACA,WAAW,MAAM;AACf,UAAM,aAAa,OAAO,OAAO,KAAK,OAAO;AAC7C,eAAW,QAAQ,IAAI;AACvB,UAAM,UAAU,CAAC;AACjB,aAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAC1C,YAAM,SAAS,KAAK,WAAW,CAAC,CAAC;AACjC,UAAI,OAAQ,SAAQ,KAAK,MAAM;AAAA,IACjC;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,cAAc,MAAM,OAAO;AACzB,UAAM,WAAW,OAAO,MAAM;AAC9B,QAAI,aAAa,KAAK,MAAM,IAAI,QAAQ;AACxC,QAAI,CAAC,YAAY;AACf,cAAQ,MAAM;AAAA,QACZ,KAAK;AACH,uBAAa,KAAK,UAAU,KAAK;AACjC;AAAA,QACF,KAAK;AACH,uBAAa,KAAK,WAAW,SAAU,KAAK;AAC1C,mBAAO,IAAI,YAAY,IAAI,SAAS,KAAK;AAAA,UAC3C,CAAC;AACD;AAAA,QACF,KAAK;AACH,uBAAa,KAAK,WAAW,SAAU,KAAK;AAC1C,mBAAO,IAAI,YAAY,IAAI,SAAS,KAAK;AAAA,UAC3C,CAAC;AACD;AAAA,QACF,KAAK;AACH,uBAAa,KAAK,aAAa,KAAK;AACpC;AAAA,QACF,KAAK;AACH,uBAAa,KAAK,WAAW,SAAU,KAAK;AAC1C,mBAAO,IAAI,kBAAkB,IAAI,eAAe,KAAK;AAAA,UACvD,CAAC;AACD;AAAA,QACF,KAAK;AACH,uBAAa,KAAK,WAAW,KAAK;AAClC;AAAA,QACF,KAAK;AACH,uBAAa,KAAK,WAAW,SAAU,KAAK;AAC1C,mBAAO,IAAI,gBAAgB,IAAI,aAAa,KAAK;AAAA,UACnD,CAAC;AACD;AAAA,QACF,KAAK;AACH,uBAAa,KAAK,WAAW,SAAU,KAAK;AAC1C,mBAAO,IAAI,eAAe,IAAI,YAAY,KAAK;AAAA,UACjD,CAAC;AACD;AAAA,QACF,KAAK;AACH,uBAAa,KAAK,SAAS,KAAK;AAChC;AAAA,QACF,KAAK;AACH,uBAAa,KAAK,WAAW,SAAU,KAAK;AAC1C,mBAAO,IAAI,iBAAiB,IAAI,cAAc,KAAK;AAAA,UACrD,CAAC;AACD;AAAA,QACF,KAAK;AACH,uBAAa,KAAK,WAAW,KAAK;AAClC;AAAA,QACF;AACE,uBAAa,KAAK,WAAW,SAAU,KAAK;AAC1C,mBAAO,OAAO,QAAQ,IAAI,iBAAiB,IAAI,cAAc,MAAM,KAAK;AAAA,UAC1E,CAAC;AACD,cAAI,CAAC,YAAY;AACf,kBAAM,IAAI,MAAM,mBAAmB,IAAI;AAAA,UACzC;AACA;AAAA,MACJ;AACA,WAAK,MAAM,IAAI,UAAU,UAAU;AAAA,IACrC;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,gBAAgB,MAAM;AACpB,QAAI,eAAe,KAAK,MAAM,IAAI,IAAI;AACtC,QAAI,CAAC,cAAc;AACjB,YAAM,SAAS;AACf,YAAM,OAAO,KAAK,KAAK,QAAQ,SAAS,SAAS,OAAO,IAAI,KAAK,CAAC;AAClE,qBAAe,QAAQ,IAAI,KAAK,IAAI,SAAU,KAAK,OAAO;AACxD,eAAO,OAAO,cAAc,MAAM,KAAK;AAAA,MACzC,CAAC,CAAC;AACF,WAAK,MAAM,IAAI,MAAM,YAAY;AAAA,IACnC;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,WAAW,aAAa;AACtB,UAAM,YAAY,KAAK,KAAK,QAAQ,WAAW;AAC/C,UAAM,SAAS,KAAK;AACpB,QAAI,UAAU,QAAQ,UAAU,SAAS,eAAe;AACtD,YAAM,IAAI,MAAM,uBAAuB,UAAU,OAAO,gCAAgC;AAAA,IAC1F;AAGA,QAAI,UAAU,QAAQ,UAAa,gBAAgB,GAAG;AACpD,aAAO,QAAQ,QAAQ,KAAK,WAAW,WAAW,eAAe,EAAE,IAAI;AAAA,IACzE;AACA,UAAM,UAAU,KAAK;AACrB,WAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC5C,aAAO,KAAK,YAAY,WAAW,UAAU,KAAK,QAAQ,IAAI,GAAG,SAAS,QAAW,WAAY;AAC/F,eAAO,IAAI,MAAM,8CAA8C,UAAU,MAAM,IAAI,CAAC;AAAA,MACtF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,eAAe,iBAAiB;AAC9B,UAAM,gBAAgB,KAAK,KAAK,YAAY,eAAe;AAC3D,WAAO,KAAK,cAAc,UAAU,cAAc,MAAM,EAAE,KAAK,SAAU,QAAQ;AAC/E,YAAM,aAAa,cAAc,cAAc;AAC/C,YAAM,aAAa,cAAc,cAAc;AAC/C,aAAO,OAAO,MAAM,YAAY,aAAa,UAAU;AAAA,IACzD,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,aAAa,eAAe;AAC1B,UAAM,SAAS;AACf,UAAM,OAAO,KAAK;AAClB,UAAM,cAAc,KAAK,KAAK,UAAU,aAAa;AACrD,QAAI,YAAY,eAAe,UAAa,YAAY,WAAW,QAAW;AAC5E,YAAM,WAAW,iBAAiB,YAAY,IAAI;AAClD,YAAM,aAAa,sBAAsB,YAAY,aAAa;AAClE,YAAM,aAAa,YAAY,eAAe;AAC9C,YAAM,QAAQ,IAAI,WAAW,YAAY,QAAQ,QAAQ;AACzD,aAAO,QAAQ,QAAQ,IAAI,gBAAgB,OAAO,UAAU,UAAU,CAAC;AAAA,IACzE;AACA,UAAM,qBAAqB,CAAC;AAC5B,QAAI,YAAY,eAAe,QAAW;AACxC,yBAAmB,KAAK,KAAK,cAAc,cAAc,YAAY,UAAU,CAAC;AAAA,IAClF,OAAO;AACL,yBAAmB,KAAK,IAAI;AAAA,IAC9B;AACA,QAAI,YAAY,WAAW,QAAW;AACpC,yBAAmB,KAAK,KAAK,cAAc,cAAc,YAAY,OAAO,QAAQ,UAAU,CAAC;AAC/F,yBAAmB,KAAK,KAAK,cAAc,cAAc,YAAY,OAAO,OAAO,UAAU,CAAC;AAAA,IAChG;AACA,WAAO,QAAQ,IAAI,kBAAkB,EAAE,KAAK,SAAU,aAAa;AACjE,YAAM,aAAa,YAAY,CAAC;AAChC,YAAM,WAAW,iBAAiB,YAAY,IAAI;AAClD,YAAM,aAAa,sBAAsB,YAAY,aAAa;AAGlE,YAAM,eAAe,WAAW;AAChC,YAAM,YAAY,eAAe;AACjC,YAAM,aAAa,YAAY,cAAc;AAC7C,YAAM,aAAa,YAAY,eAAe,SAAY,KAAK,YAAY,YAAY,UAAU,EAAE,aAAa;AAChH,YAAM,aAAa,YAAY,eAAe;AAC9C,UAAI,OAAO;AAGX,UAAI,cAAc,eAAe,WAAW;AAG1C,cAAM,UAAU,KAAK,MAAM,aAAa,UAAU;AAClD,cAAM,aAAa,uBAAuB,YAAY,aAAa,MAAM,YAAY,gBAAgB,MAAM,UAAU,MAAM,YAAY;AACvI,YAAI,KAAK,OAAO,MAAM,IAAI,UAAU;AACpC,YAAI,CAAC,IAAI;AACP,kBAAQ,IAAI,WAAW,YAAY,UAAU,YAAY,YAAY,QAAQ,aAAa,YAAY;AAGtG,eAAK,IAAI,kBAAkB,OAAO,aAAa,YAAY;AAC3D,iBAAO,MAAM,IAAI,YAAY,EAAE;AAAA,QACjC;AACA,0BAAkB,IAAI,2BAA2B,IAAI,UAAU,aAAa,aAAa,cAAc,UAAU;AAAA,MACnH,OAAO;AACL,YAAI,eAAe,MAAM;AACvB,kBAAQ,IAAI,WAAW,YAAY,QAAQ,QAAQ;AAAA,QACrD,OAAO;AACL,kBAAQ,IAAI,WAAW,YAAY,YAAY,YAAY,QAAQ,QAAQ;AAAA,QAC7E;AACA,0BAAkB,IAAI,gBAAgB,OAAO,UAAU,UAAU;AAAA,MACnE;AAGA,UAAI,YAAY,WAAW,QAAW;AACpC,cAAM,kBAAkB,iBAAiB;AACzC,cAAM,oBAAoB,sBAAsB,YAAY,OAAO,QAAQ,aAAa;AACxF,cAAM,oBAAoB,YAAY,OAAO,QAAQ,cAAc;AACnE,cAAM,mBAAmB,YAAY,OAAO,OAAO,cAAc;AACjE,cAAM,gBAAgB,IAAI,kBAAkB,YAAY,CAAC,GAAG,mBAAmB,YAAY,OAAO,QAAQ,eAAe;AACzH,cAAM,eAAe,IAAI,WAAW,YAAY,CAAC,GAAG,kBAAkB,YAAY,OAAO,QAAQ,QAAQ;AACzG,YAAI,eAAe,MAAM;AAEvB,4BAAkB,IAAI,gBAAgB,gBAAgB,MAAM,MAAM,GAAG,gBAAgB,UAAU,gBAAgB,UAAU;AAAA,QAC3H;AAGA,wBAAgB,aAAa;AAC7B,iBAAS,IAAI,GAAG,KAAK,cAAc,QAAQ,IAAI,IAAI,KAAK;AACtD,gBAAM,QAAQ,cAAc,CAAC;AAC7B,0BAAgB,KAAK,OAAO,aAAa,IAAI,QAAQ,CAAC;AACtD,cAAI,YAAY,EAAG,iBAAgB,KAAK,OAAO,aAAa,IAAI,WAAW,CAAC,CAAC;AAC7E,cAAI,YAAY,EAAG,iBAAgB,KAAK,OAAO,aAAa,IAAI,WAAW,CAAC,CAAC;AAC7E,cAAI,YAAY,EAAG,iBAAgB,KAAK,OAAO,aAAa,IAAI,WAAW,CAAC,CAAC;AAC7E,cAAI,YAAY,EAAG,OAAM,IAAI,MAAM,mEAAmE;AAAA,QACxG;AACA,wBAAgB,aAAa;AAAA,MAC/B;AACA,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,YAAY,cAAc;AACxB,UAAM,OAAO,KAAK;AAClB,UAAM,UAAU,KAAK;AACrB,UAAM,aAAa,KAAK,SAAS,YAAY;AAC7C,UAAM,cAAc,WAAW;AAC/B,UAAM,YAAY,KAAK,OAAO,WAAW;AACzC,QAAI,SAAS,KAAK;AAClB,QAAI,UAAU,KAAK;AACjB,YAAM,UAAU,QAAQ,QAAQ,WAAW,UAAU,GAAG;AACxD,UAAI,YAAY,KAAM,UAAS;AAAA,IACjC;AACA,WAAO,KAAK,iBAAiB,cAAc,aAAa,MAAM;AAAA,EAChE;AAAA,EACA,iBAAiB,cAAc,aAAa,QAAQ;AAClD,UAAM,SAAS;AACf,UAAM,OAAO,KAAK;AAClB,UAAM,aAAa,KAAK,SAAS,YAAY;AAC7C,UAAM,YAAY,KAAK,OAAO,WAAW;AACzC,UAAM,YAAY,UAAU,OAAO,UAAU,cAAc,MAAM,WAAW;AAC5E,QAAI,KAAK,aAAa,QAAQ,GAAG;AAE/B,aAAO,KAAK,aAAa,QAAQ;AAAA,IACnC;AACA,UAAM,UAAU,KAAK,gBAAgB,aAAa,MAAM,EAAE,KAAK,SAAU,SAAS;AAChF,cAAQ,QAAQ;AAChB,cAAQ,OAAO,WAAW,QAAQ,UAAU,QAAQ;AACpD,UAAI,QAAQ,SAAS,MAAM,OAAO,UAAU,QAAQ,YAAY,UAAU,IAAI,WAAW,aAAa,MAAM,OAAO;AACjH,gBAAQ,OAAO,UAAU;AAAA,MAC3B;AACA,YAAM,WAAW,KAAK,YAAY,CAAC;AACnC,YAAM,UAAU,SAAS,WAAW,OAAO,KAAK,CAAC;AACjD,cAAQ,YAAY,cAAc,QAAQ,SAAS,KAAK;AACxD,cAAQ,YAAY,cAAc,QAAQ,SAAS,KAAK;AACxD,cAAQ,QAAQ,gBAAgB,QAAQ,KAAK,KAAK;AAClD,cAAQ,QAAQ,gBAAgB,QAAQ,KAAK,KAAK;AAClD,cAAQ,kBAAkB,CAAC,QAAQ,uBAAuB,QAAQ,cAAc,iBAAiB,QAAQ,cAAc;AACvH,aAAO,aAAa,IAAI,SAAS;AAAA,QAC/B,UAAU;AAAA,MACZ,CAAC;AACD,aAAO;AAAA,IACT,CAAC,EAAE,MAAM,WAAY;AACnB,aAAO;AAAA,IACT,CAAC;AACD,SAAK,aAAa,QAAQ,IAAI;AAC9B,WAAO;AAAA,EACT;AAAA,EACA,gBAAgB,aAAa,QAAQ;AACnC,UAAM,SAAS;AACf,UAAM,OAAO,KAAK;AAClB,UAAM,UAAU,KAAK;AACrB,QAAI,KAAK,YAAY,WAAW,MAAM,QAAW;AAC/C,aAAO,KAAK,YAAY,WAAW,EAAE,KAAK,aAAW,QAAQ,MAAM,CAAC;AAAA,IACtE;AACA,UAAM,YAAY,KAAK,OAAO,WAAW;AACzC,UAAM,MAAM,KAAK,OAAO,KAAK;AAC7B,QAAI,YAAY,UAAU,OAAO;AACjC,QAAI,cAAc;AAClB,QAAI,UAAU,eAAe,QAAW;AAGtC,kBAAY,OAAO,cAAc,cAAc,UAAU,UAAU,EAAE,KAAK,SAAU,YAAY;AAC9F,sBAAc;AACd,cAAM,OAAO,IAAI,KAAK,CAAC,UAAU,GAAG;AAAA,UAClC,MAAM,UAAU;AAAA,QAClB,CAAC;AACD,oBAAY,IAAI,gBAAgB,IAAI;AACpC,eAAO;AAAA,MACT,CAAC;AAAA,IACH,WAAW,UAAU,QAAQ,QAAW;AACtC,YAAM,IAAI,MAAM,6BAA6B,cAAc,gCAAgC;AAAA,IAC7F;AACA,UAAM,UAAU,QAAQ,QAAQ,SAAS,EAAE,KAAK,SAAUC,YAAW;AACnE,aAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC5C,YAAI,SAAS;AACb,YAAI,OAAO,wBAAwB,MAAM;AACvC,mBAAS,SAAU,aAAa;AAC9B,kBAAM,UAAU,IAAI,QAAQ,WAAW;AACvC,oBAAQ,cAAc;AACtB,oBAAQ,OAAO;AAAA,UACjB;AAAA,QACF;AACA,eAAO,KAAK,YAAY,WAAWA,YAAW,QAAQ,IAAI,GAAG,QAAQ,QAAW,MAAM;AAAA,MACxF,CAAC;AAAA,IACH,CAAC,EAAE,KAAK,SAAU,SAAS;AAGzB,UAAI,gBAAgB,MAAM;AACxB,YAAI,gBAAgB,SAAS;AAAA,MAC/B;AACA,6BAAuB,SAAS,SAAS;AACzC,cAAQ,SAAS,WAAW,UAAU,YAAY,oBAAoB,UAAU,GAAG;AACnF,aAAO;AAAA,IACT,CAAC,EAAE,MAAM,SAAU,OAAO;AACxB,cAAQ,MAAM,2CAA4C,SAAS;AACnE,YAAM;AAAA,IACR,CAAC;AACD,SAAK,YAAY,WAAW,IAAI;AAChC,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,cAAc,gBAAgB,SAAS,QAAQ,YAAY;AACzD,UAAM,SAAS;AACf,WAAO,KAAK,cAAc,WAAW,OAAO,KAAK,EAAE,KAAK,SAAU,SAAS;AACzE,UAAI,CAAC,QAAS,QAAO;AACrB,UAAI,OAAO,aAAa,UAAa,OAAO,WAAW,GAAG;AACxD,kBAAU,QAAQ,MAAM;AACxB,gBAAQ,UAAU,OAAO;AAAA,MAC3B;AACA,UAAI,OAAO,WAAW,WAAW,qBAAqB,GAAG;AACvD,cAAM,YAAY,OAAO,eAAe,SAAY,OAAO,WAAW,WAAW,qBAAqB,IAAI;AAC1G,YAAI,WAAW;AACb,gBAAM,gBAAgB,OAAO,aAAa,IAAI,OAAO;AACrD,oBAAU,OAAO,WAAW,WAAW,qBAAqB,EAAE,cAAc,SAAS,SAAS;AAC9F,iBAAO,aAAa,IAAI,SAAS,aAAa;AAAA,QAChD;AAAA,MACF;AACA,UAAI,eAAe,QAAW;AAC5B,gBAAQ,aAAa;AAAA,MACvB;AACA,qBAAe,OAAO,IAAI;AAC1B,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,oBAAoB,MAAM;AACxB,UAAM,WAAW,KAAK;AACtB,QAAI,WAAW,KAAK;AACpB,UAAM,wBAAwB,SAAS,WAAW,YAAY;AAC9D,UAAM,kBAAkB,SAAS,WAAW,UAAU;AACtD,UAAM,iBAAiB,SAAS,WAAW,WAAW;AACtD,QAAI,KAAK,UAAU;AACjB,YAAM,WAAW,oBAAoB,SAAS;AAC9C,UAAI,iBAAiB,KAAK,MAAM,IAAI,QAAQ;AAC5C,UAAI,CAAC,gBAAgB;AACnB,yBAAiB,IAAI,eAAe;AACpC,iBAAS,UAAU,KAAK,KAAK,gBAAgB,QAAQ;AACrD,uBAAe,MAAM,KAAK,SAAS,KAAK;AACxC,uBAAe,MAAM,SAAS;AAC9B,uBAAe,kBAAkB;AAEjC,aAAK,MAAM,IAAI,UAAU,cAAc;AAAA,MACzC;AACA,iBAAW;AAAA,IACb,WAAW,KAAK,QAAQ;AACtB,YAAM,WAAW,uBAAuB,SAAS;AACjD,UAAI,eAAe,KAAK,MAAM,IAAI,QAAQ;AAC1C,UAAI,CAAC,cAAc;AACjB,uBAAe,IAAI,kBAAkB;AACrC,iBAAS,UAAU,KAAK,KAAK,cAAc,QAAQ;AACnD,qBAAa,MAAM,KAAK,SAAS,KAAK;AACtC,qBAAa,MAAM,SAAS;AAC5B,aAAK,MAAM,IAAI,UAAU,YAAY;AAAA,MACvC;AACA,iBAAW;AAAA,IACb;AAGA,QAAI,yBAAyB,mBAAmB,gBAAgB;AAC9D,UAAI,WAAW,oBAAoB,SAAS,OAAO;AACnD,UAAI,sBAAuB,aAAY;AACvC,UAAI,gBAAiB,aAAY;AACjC,UAAI,eAAgB,aAAY;AAChC,UAAI,iBAAiB,KAAK,MAAM,IAAI,QAAQ;AAC5C,UAAI,CAAC,gBAAgB;AACnB,yBAAiB,SAAS,MAAM;AAChC,YAAI,gBAAiB,gBAAe,eAAe;AACnD,YAAI,eAAgB,gBAAe,cAAc;AACjD,YAAI,uBAAuB;AAEzB,cAAI,eAAe,YAAa,gBAAe,YAAY,KAAK;AAChE,cAAI,eAAe,qBAAsB,gBAAe,qBAAqB,KAAK;AAAA,QACpF;AACA,aAAK,MAAM,IAAI,UAAU,cAAc;AACvC,aAAK,aAAa,IAAI,gBAAgB,KAAK,aAAa,IAAI,QAAQ,CAAC;AAAA,MACvE;AACA,iBAAW;AAAA,IACb;AACA,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,kBACE;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,aAAa,eAAe;AAC1B,UAAM,SAAS;AACf,UAAM,OAAO,KAAK;AAClB,UAAM,aAAa,KAAK;AACxB,UAAM,cAAc,KAAK,UAAU,aAAa;AAChD,QAAI;AACJ,UAAM,iBAAiB,CAAC;AACxB,UAAM,qBAAqB,YAAY,cAAc,CAAC;AACtD,UAAM,UAAU,CAAC;AACjB,QAAI,mBAAmB,WAAW,mBAAmB,GAAG;AACtD,YAAM,eAAe,WAAW,WAAW,mBAAmB;AAC9D,qBAAe,aAAa,gBAAgB;AAC5C,cAAQ,KAAK,aAAa,aAAa,gBAAgB,aAAa,MAAM,CAAC;AAAA,IAC7E,OAAO;AAIL,YAAM,oBAAoB,YAAY,wBAAwB,CAAC;AAC/D,qBAAe,QAAQ,IAAI,MAAM,GAAK,GAAK,CAAG;AAC9C,qBAAe,UAAU;AACzB,UAAI,MAAM,QAAQ,kBAAkB,eAAe,GAAG;AACpD,cAAM,QAAQ,kBAAkB;AAChC,uBAAe,MAAM,OAAO,MAAM,CAAC,GAAG,MAAM,CAAC,GAAG,MAAM,CAAC,GAAG,oBAAoB;AAC9E,uBAAe,UAAU,MAAM,CAAC;AAAA,MAClC;AACA,UAAI,kBAAkB,qBAAqB,QAAW;AACpD,gBAAQ,KAAK,OAAO,cAAc,gBAAgB,OAAO,kBAAkB,kBAAkB,cAAc,CAAC;AAAA,MAC9G;AACA,qBAAe,YAAY,kBAAkB,mBAAmB,SAAY,kBAAkB,iBAAiB;AAC/G,qBAAe,YAAY,kBAAkB,oBAAoB,SAAY,kBAAkB,kBAAkB;AACjH,UAAI,kBAAkB,6BAA6B,QAAW;AAC5D,gBAAQ,KAAK,OAAO,cAAc,gBAAgB,gBAAgB,kBAAkB,wBAAwB,CAAC;AAC7G,gBAAQ,KAAK,OAAO,cAAc,gBAAgB,gBAAgB,kBAAkB,wBAAwB,CAAC;AAAA,MAC/G;AACA,qBAAe,KAAK,WAAW,SAAU,KAAK;AAC5C,eAAO,IAAI,mBAAmB,IAAI,gBAAgB,aAAa;AAAA,MACjE,CAAC;AACD,cAAQ,KAAK,QAAQ,IAAI,KAAK,WAAW,SAAU,KAAK;AACtD,eAAO,IAAI,wBAAwB,IAAI,qBAAqB,eAAe,cAAc;AAAA,MAC3F,CAAC,CAAC,CAAC;AAAA,IACL;AACA,QAAI,YAAY,gBAAgB,MAAM;AACpC,qBAAe,OAAO;AAAA,IACxB;AACA,UAAM,YAAY,YAAY,aAAa,YAAY;AACvD,QAAI,cAAc,YAAY,OAAO;AACnC,qBAAe,cAAc;AAG7B,qBAAe,aAAa;AAAA,IAC9B,OAAO;AACL,qBAAe,cAAc;AAC7B,UAAI,cAAc,YAAY,MAAM;AAClC,uBAAe,YAAY,YAAY,gBAAgB,SAAY,YAAY,cAAc;AAAA,MAC/F;AAAA,IACF;AACA,QAAI,YAAY,kBAAkB,UAAa,iBAAiB,mBAAmB;AACjF,cAAQ,KAAK,OAAO,cAAc,gBAAgB,aAAa,YAAY,aAAa,CAAC;AACzF,qBAAe,cAAc,IAAI,QAAQ,GAAG,CAAC;AAC7C,UAAI,YAAY,cAAc,UAAU,QAAW;AACjD,cAAM,QAAQ,YAAY,cAAc;AACxC,uBAAe,YAAY,IAAI,OAAO,KAAK;AAAA,MAC7C;AAAA,IACF;AACA,QAAI,YAAY,qBAAqB,UAAa,iBAAiB,mBAAmB;AACpF,cAAQ,KAAK,OAAO,cAAc,gBAAgB,SAAS,YAAY,gBAAgB,CAAC;AACxF,UAAI,YAAY,iBAAiB,aAAa,QAAW;AACvD,uBAAe,iBAAiB,YAAY,iBAAiB;AAAA,MAC/D;AAAA,IACF;AACA,QAAI,YAAY,mBAAmB,UAAa,iBAAiB,mBAAmB;AAClF,YAAM,iBAAiB,YAAY;AACnC,qBAAe,WAAW,IAAI,MAAM,EAAE,OAAO,eAAe,CAAC,GAAG,eAAe,CAAC,GAAG,eAAe,CAAC,GAAG,oBAAoB;AAAA,IAC5H;AACA,QAAI,YAAY,oBAAoB,UAAa,iBAAiB,mBAAmB;AACnF,cAAQ,KAAK,OAAO,cAAc,gBAAgB,eAAe,YAAY,iBAAiB,cAAc,CAAC;AAAA,IAC/G;AACA,WAAO,QAAQ,IAAI,OAAO,EAAE,KAAK,WAAY;AAC3C,YAAM,WAAW,IAAI,aAAa,cAAc;AAChD,UAAI,YAAY,KAAM,UAAS,OAAO,YAAY;AAClD,6BAAuB,UAAU,WAAW;AAC5C,aAAO,aAAa,IAAI,UAAU;AAAA,QAChC,WAAW;AAAA,MACb,CAAC;AACD,UAAI,YAAY,WAAY,gCAA+B,YAAY,UAAU,WAAW;AAC5F,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,iBAAiB,cAAc;AAC7B,UAAM,gBAAgB,gBAAgB,iBAAiB,gBAAgB,EAAE;AACzE,QAAI,iBAAiB,KAAK,eAAe;AACvC,aAAO,gBAAgB,MAAM,EAAE,KAAK,cAAc,aAAa;AAAA,IACjE,OAAO;AACL,WAAK,cAAc,aAAa,IAAI;AACpC,aAAO;AAAA,IACT;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,eAAe,YAAY;AACzB,UAAM,SAAS;AACf,UAAM,aAAa,KAAK;AACxB,UAAM,QAAQ,KAAK;AACnB,aAAS,qBAAqB,WAAW;AACvC,aAAO,WAAW,WAAW,0BAA0B,EAAE,gBAAgB,WAAW,MAAM,EAAE,KAAK,SAAU,UAAU;AACnH,eAAO,uBAAuB,UAAU,WAAW,MAAM;AAAA,MAC3D,CAAC;AAAA,IACH;AACA,UAAM,UAAU,CAAC;AACjB,aAAS,IAAI,GAAG,KAAK,WAAW,QAAQ,IAAI,IAAI,KAAK;AACnD,YAAM,YAAY,WAAW,CAAC;AAC9B,YAAM,WAAW,mBAAmB,SAAS;AAG7C,YAAM,SAAS,MAAM,QAAQ;AAC7B,UAAI,QAAQ;AAEV,gBAAQ,KAAK,OAAO,OAAO;AAAA,MAC7B,OAAO;AACL,YAAI;AACJ,YAAI,UAAU,cAAc,UAAU,WAAW,WAAW,0BAA0B,GAAG;AAEvF,4BAAkB,qBAAqB,SAAS;AAAA,QAClD,OAAO;AAEL,4BAAkB,uBAAuB,IAAI,eAAe,GAAG,WAAW,MAAM;AAAA,QAClF;AAGA,cAAM,QAAQ,IAAI;AAAA,UAChB;AAAA,UACA,SAAS;AAAA,QACX;AACA,gBAAQ,KAAK,eAAe;AAAA,MAC9B;AAAA,IACF;AACA,WAAO,QAAQ,IAAI,OAAO;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,SAAS,WAAW;AAClB,UAAM,SAAS;AACf,UAAM,OAAO,KAAK;AAClB,UAAM,aAAa,KAAK;AACxB,UAAM,UAAU,KAAK,OAAO,SAAS;AACrC,UAAM,aAAa,QAAQ;AAC3B,UAAM,UAAU,CAAC;AACjB,aAAS,IAAI,GAAG,KAAK,WAAW,QAAQ,IAAI,IAAI,KAAK;AACnD,YAAM,WAAW,WAAW,CAAC,EAAE,aAAa,SAAY,sBAAsB,KAAK,KAAK,IAAI,KAAK,cAAc,YAAY,WAAW,CAAC,EAAE,QAAQ;AACjJ,cAAQ,KAAK,QAAQ;AAAA,IACvB;AACA,YAAQ,KAAK,OAAO,eAAe,UAAU,CAAC;AAC9C,WAAO,QAAQ,IAAI,OAAO,EAAE,KAAK,SAAU,SAAS;AAClD,YAAM,YAAY,QAAQ,MAAM,GAAG,QAAQ,SAAS,CAAC;AACrD,YAAM,aAAa,QAAQ,QAAQ,SAAS,CAAC;AAC7C,YAAM,SAAS,CAAC;AAChB,eAAS,IAAI,GAAG,KAAK,WAAW,QAAQ,IAAI,IAAI,KAAK;AACnD,cAAM,WAAW,WAAW,CAAC;AAC7B,cAAM,YAAY,WAAW,CAAC;AAI9B,YAAI;AACJ,cAAM,WAAW,UAAU,CAAC;AAC5B,YAAI,UAAU,SAAS,gBAAgB,aAAa,UAAU,SAAS,gBAAgB,kBAAkB,UAAU,SAAS,gBAAgB,gBAAgB,UAAU,SAAS,QAAW;AAExL,iBAAO,QAAQ,kBAAkB,OAAO,IAAI,YAAY,UAAU,QAAQ,IAAI,IAAI,KAAK,UAAU,QAAQ;AACzG,cAAI,KAAK,kBAAkB,MAAM;AAE/B,iBAAK,qBAAqB;AAAA,UAC5B;AACA,cAAI,UAAU,SAAS,gBAAgB,gBAAgB;AACrD,iBAAK,WAAW,oBAAoB,KAAK,UAAU,qBAAqB;AAAA,UAC1E,WAAW,UAAU,SAAS,gBAAgB,cAAc;AAC1D,iBAAK,WAAW,oBAAoB,KAAK,UAAU,mBAAmB;AAAA,UACxE;AAAA,QACF,WAAW,UAAU,SAAS,gBAAgB,OAAO;AACnD,iBAAO,IAAI,aAAa,UAAU,QAAQ;AAAA,QAC5C,WAAW,UAAU,SAAS,gBAAgB,YAAY;AACxD,iBAAO,IAAI,KAAK,UAAU,QAAQ;AAAA,QACpC,WAAW,UAAU,SAAS,gBAAgB,WAAW;AACvD,iBAAO,IAAI,SAAS,UAAU,QAAQ;AAAA,QACxC,WAAW,UAAU,SAAS,gBAAgB,QAAQ;AACpD,iBAAO,IAAI,OAAO,UAAU,QAAQ;AAAA,QACtC,OAAO;AACL,gBAAM,IAAI,MAAM,mDAAmD,UAAU,IAAI;AAAA,QACnF;AACA,YAAI,OAAO,KAAK,KAAK,SAAS,eAAe,EAAE,SAAS,GAAG;AACzD,6BAAmB,MAAM,OAAO;AAAA,QAClC;AACA,aAAK,OAAO,OAAO,iBAAiB,QAAQ,QAAQ,UAAU,SAAS;AACvE,+BAAuB,MAAM,OAAO;AACpC,YAAI,UAAU,WAAY,gCAA+B,YAAY,MAAM,SAAS;AACpF,eAAO,oBAAoB,IAAI;AAC/B,eAAO,KAAK,IAAI;AAAA,MAClB;AACA,eAAS,IAAI,GAAG,KAAK,OAAO,QAAQ,IAAI,IAAI,KAAK;AAC/C,eAAO,aAAa,IAAI,OAAO,CAAC,GAAG;AAAA,UACjC,QAAQ;AAAA,UACR,YAAY;AAAA,QACd,CAAC;AAAA,MACH;AACA,UAAI,OAAO,WAAW,GAAG;AACvB,YAAI,QAAQ,WAAY,gCAA+B,YAAY,OAAO,CAAC,GAAG,OAAO;AACrF,eAAO,OAAO,CAAC;AAAA,MACjB;AACA,YAAM,QAAQ,IAAI,MAAM;AACxB,UAAI,QAAQ,WAAY,gCAA+B,YAAY,OAAO,OAAO;AACjF,aAAO,aAAa,IAAI,OAAO;AAAA,QAC7B,QAAQ;AAAA,MACV,CAAC;AACD,eAAS,IAAI,GAAG,KAAK,OAAO,QAAQ,IAAI,IAAI,KAAK;AAC/C,cAAM,IAAI,OAAO,CAAC,CAAC;AAAA,MACrB;AACA,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,WAAW,aAAa;AACtB,QAAI;AACJ,UAAM,YAAY,KAAK,KAAK,QAAQ,WAAW;AAC/C,UAAM,SAAS,UAAU,UAAU,IAAI;AACvC,QAAI,CAAC,QAAQ;AACX,cAAQ,KAAK,8CAA8C;AAC3D;AAAA,IACF;AACA,QAAI,UAAU,SAAS,eAAe;AACpC,eAAS,IAAI,kBAAkB,UAAU,SAAS,OAAO,IAAI,GAAG,OAAO,eAAe,GAAG,OAAO,SAAS,GAAG,OAAO,QAAQ,GAAG;AAAA,IAChI,WAAW,UAAU,SAAS,gBAAgB;AAC5C,eAAS,IAAI,mBAAmB,CAAC,OAAO,MAAM,OAAO,MAAM,OAAO,MAAM,CAAC,OAAO,MAAM,OAAO,OAAO,OAAO,IAAI;AAAA,IACjH;AACA,QAAI,UAAU,KAAM,QAAO,OAAO,KAAK,iBAAiB,UAAU,IAAI;AACtE,2BAAuB,QAAQ,SAAS;AACxC,WAAO,QAAQ,QAAQ,MAAM;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,SAAS,WAAW;AAClB,UAAM,UAAU,KAAK,KAAK,MAAM,SAAS;AACzC,UAAM,UAAU,CAAC;AACjB,aAAS,IAAI,GAAG,KAAK,QAAQ,OAAO,QAAQ,IAAI,IAAI,KAAK;AACvD,cAAQ,KAAK,KAAK,iBAAiB,QAAQ,OAAO,CAAC,CAAC,CAAC;AAAA,IACvD;AACA,QAAI,QAAQ,wBAAwB,QAAW;AAC7C,cAAQ,KAAK,KAAK,cAAc,YAAY,QAAQ,mBAAmB,CAAC;AAAA,IAC1E,OAAO;AACL,cAAQ,KAAK,IAAI;AAAA,IACnB;AACA,WAAO,QAAQ,IAAI,OAAO,EAAE,KAAK,SAAU,SAAS;AAClD,YAAM,sBAAsB,QAAQ,IAAI;AACxC,YAAM,aAAa;AAKnB,YAAM,QAAQ,CAAC;AACf,YAAM,eAAe,CAAC;AACtB,eAAS,IAAI,GAAG,KAAK,WAAW,QAAQ,IAAI,IAAI,KAAK;AACnD,cAAM,YAAY,WAAW,CAAC;AAC9B,YAAI,WAAW;AACb,gBAAM,KAAK,SAAS;AACpB,gBAAM,MAAM,IAAI,QAAQ;AACxB,cAAI,wBAAwB,MAAM;AAChC,gBAAI,UAAU,oBAAoB,OAAO,IAAI,EAAE;AAAA,UACjD;AACA,uBAAa,KAAK,GAAG;AAAA,QACvB,OAAO;AACL,kBAAQ,KAAK,oDAAoD,QAAQ,OAAO,CAAC,CAAC;AAAA,QACpF;AAAA,MACF;AACA,aAAO,IAAI,SAAS,OAAO,YAAY;AAAA,IACzC,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,cAAc,gBAAgB;AAC5B,UAAM,OAAO,KAAK;AAClB,UAAM,SAAS;AACf,UAAM,eAAe,KAAK,WAAW,cAAc;AACnD,UAAM,gBAAgB,aAAa,OAAO,aAAa,OAAO,eAAe;AAC7E,UAAM,eAAe,CAAC;AACtB,UAAM,wBAAwB,CAAC;AAC/B,UAAM,yBAAyB,CAAC;AAChC,UAAM,kBAAkB,CAAC;AACzB,UAAM,iBAAiB,CAAC;AACxB,aAAS,IAAI,GAAG,KAAK,aAAa,SAAS,QAAQ,IAAI,IAAI,KAAK;AAC9D,YAAM,UAAU,aAAa,SAAS,CAAC;AACvC,YAAM,UAAU,aAAa,SAAS,QAAQ,OAAO;AACrD,YAAM,SAAS,QAAQ;AACvB,YAAM,OAAO,OAAO;AACpB,YAAM,QAAQ,aAAa,eAAe,SAAY,aAAa,WAAW,QAAQ,KAAK,IAAI,QAAQ;AACvG,YAAM,SAAS,aAAa,eAAe,SAAY,aAAa,WAAW,QAAQ,MAAM,IAAI,QAAQ;AACzG,UAAI,OAAO,SAAS,OAAW;AAC/B,mBAAa,KAAK,KAAK,cAAc,QAAQ,IAAI,CAAC;AAClD,4BAAsB,KAAK,KAAK,cAAc,YAAY,KAAK,CAAC;AAChE,6BAAuB,KAAK,KAAK,cAAc,YAAY,MAAM,CAAC;AAClE,sBAAgB,KAAK,OAAO;AAC5B,qBAAe,KAAK,MAAM;AAAA,IAC5B;AACA,WAAO,QAAQ,IAAI,CAAC,QAAQ,IAAI,YAAY,GAAG,QAAQ,IAAI,qBAAqB,GAAG,QAAQ,IAAI,sBAAsB,GAAG,QAAQ,IAAI,eAAe,GAAG,QAAQ,IAAI,cAAc,CAAC,CAAC,EAAE,KAAK,SAAU,cAAc;AAC/M,YAAM,QAAQ,aAAa,CAAC;AAC5B,YAAM,iBAAiB,aAAa,CAAC;AACrC,YAAM,kBAAkB,aAAa,CAAC;AACtC,YAAM,WAAW,aAAa,CAAC;AAC/B,YAAM,UAAU,aAAa,CAAC;AAC9B,YAAM,SAAS,CAAC;AAChB,eAAS,IAAI,GAAG,KAAK,MAAM,QAAQ,IAAI,IAAI,KAAK;AAC9C,cAAM,OAAO,MAAM,CAAC;AACpB,cAAM,gBAAgB,eAAe,CAAC;AACtC,cAAM,iBAAiB,gBAAgB,CAAC;AACxC,cAAM,UAAU,SAAS,CAAC;AAC1B,cAAM,SAAS,QAAQ,CAAC;AACxB,YAAI,SAAS,OAAW;AACxB,YAAI,KAAK,cAAc;AACrB,eAAK,aAAa;AAAA,QACpB;AACA,cAAM,gBAAgB,OAAO,uBAAuB,MAAM,eAAe,gBAAgB,SAAS,MAAM;AACxG,YAAI,eAAe;AACjB,mBAAS,IAAI,GAAG,IAAI,cAAc,QAAQ,KAAK;AAC7C,mBAAO,KAAK,cAAc,CAAC,CAAC;AAAA,UAC9B;AAAA,QACF;AAAA,MACF;AACA,aAAO,IAAI,cAAc,eAAe,QAAW,MAAM;AAAA,IAC3D,CAAC;AAAA,EACH;AAAA,EACA,eAAe,WAAW;AACxB,UAAM,OAAO,KAAK;AAClB,UAAM,SAAS;AACf,UAAM,UAAU,KAAK,MAAM,SAAS;AACpC,QAAI,QAAQ,SAAS,OAAW,QAAO;AACvC,WAAO,OAAO,cAAc,QAAQ,QAAQ,IAAI,EAAE,KAAK,SAAU,MAAM;AACrE,YAAM,OAAO,OAAO,YAAY,OAAO,WAAW,QAAQ,MAAM,IAAI;AAGpE,UAAI,QAAQ,YAAY,QAAW;AACjC,aAAK,SAAS,SAAU,GAAG;AACzB,cAAI,CAAC,EAAE,OAAQ;AACf,mBAAS,IAAI,GAAG,KAAK,QAAQ,QAAQ,QAAQ,IAAI,IAAI,KAAK;AACxD,cAAE,sBAAsB,CAAC,IAAI,QAAQ,QAAQ,CAAC;AAAA,UAChD;AAAA,QACF,CAAC;AAAA,MACH;AACA,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,SAAS,WAAW;AAClB,UAAM,OAAO,KAAK;AAClB,UAAM,SAAS;AACf,UAAM,UAAU,KAAK,MAAM,SAAS;AACpC,UAAM,cAAc,OAAO,iBAAiB,SAAS;AACrD,UAAM,eAAe,CAAC;AACtB,UAAM,cAAc,QAAQ,YAAY,CAAC;AACzC,aAAS,IAAI,GAAG,KAAK,YAAY,QAAQ,IAAI,IAAI,KAAK;AACpD,mBAAa,KAAK,OAAO,cAAc,QAAQ,YAAY,CAAC,CAAC,CAAC;AAAA,IAChE;AACA,UAAM,kBAAkB,QAAQ,SAAS,SAAY,QAAQ,QAAQ,IAAI,IAAI,OAAO,cAAc,QAAQ,QAAQ,IAAI;AACtH,WAAO,QAAQ,IAAI,CAAC,aAAa,QAAQ,IAAI,YAAY,GAAG,eAAe,CAAC,EAAE,KAAK,SAAU,SAAS;AACpG,YAAM,OAAO,QAAQ,CAAC;AACtB,YAAM,WAAW,QAAQ,CAAC;AAC1B,YAAM,WAAW,QAAQ,CAAC;AAC1B,UAAI,aAAa,MAAM;AAGrB,aAAK,SAAS,SAAU,MAAM;AAC5B,cAAI,CAAC,KAAK,cAAe;AACzB,eAAK,KAAK,UAAU,eAAe;AAAA,QACrC,CAAC;AAAA,MACH;AACA,eAAS,IAAI,GAAG,KAAK,SAAS,QAAQ,IAAI,IAAI,KAAK;AACjD,aAAK,IAAI,SAAS,CAAC,CAAC;AAAA,MACtB;AACA,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AAAA;AAAA;AAAA,EAIA,iBAAiB,WAAW;AAC1B,UAAM,OAAO,KAAK;AAClB,UAAM,aAAa,KAAK;AACxB,UAAM,SAAS;AAKf,QAAI,KAAK,UAAU,SAAS,MAAM,QAAW;AAC3C,aAAO,KAAK,UAAU,SAAS;AAAA,IACjC;AACA,UAAM,UAAU,KAAK,MAAM,SAAS;AAGpC,UAAM,WAAW,QAAQ,OAAO,OAAO,iBAAiB,QAAQ,IAAI,IAAI;AACxE,UAAM,UAAU,CAAC;AACjB,UAAM,cAAc,OAAO,WAAW,SAAU,KAAK;AACnD,aAAO,IAAI,kBAAkB,IAAI,eAAe,SAAS;AAAA,IAC3D,CAAC;AACD,QAAI,aAAa;AACf,cAAQ,KAAK,WAAW;AAAA,IAC1B;AACA,QAAI,QAAQ,WAAW,QAAW;AAChC,cAAQ,KAAK,OAAO,cAAc,UAAU,QAAQ,MAAM,EAAE,KAAK,SAAU,QAAQ;AACjF,eAAO,OAAO,YAAY,OAAO,aAAa,QAAQ,QAAQ,MAAM;AAAA,MACtE,CAAC,CAAC;AAAA,IACJ;AACA,WAAO,WAAW,SAAU,KAAK;AAC/B,aAAO,IAAI,wBAAwB,IAAI,qBAAqB,SAAS;AAAA,IACvE,CAAC,EAAE,QAAQ,SAAU,SAAS;AAC5B,cAAQ,KAAK,OAAO;AAAA,IACtB,CAAC;AACD,SAAK,UAAU,SAAS,IAAI,QAAQ,IAAI,OAAO,EAAE,KAAK,SAAU,SAAS;AACvE,UAAI;AAGJ,UAAI,QAAQ,WAAW,MAAM;AAC3B,eAAO,IAAI,KAAK;AAAA,MAClB,WAAW,QAAQ,SAAS,GAAG;AAC7B,eAAO,IAAI,MAAM;AAAA,MACnB,WAAW,QAAQ,WAAW,GAAG;AAC/B,eAAO,QAAQ,CAAC;AAAA,MAClB,OAAO;AACL,eAAO,IAAI,SAAS;AAAA,MACtB;AACA,UAAI,SAAS,QAAQ,CAAC,GAAG;AACvB,iBAAS,IAAI,GAAG,KAAK,QAAQ,QAAQ,IAAI,IAAI,KAAK;AAChD,eAAK,IAAI,QAAQ,CAAC,CAAC;AAAA,QACrB;AAAA,MACF;AACA,UAAI,QAAQ,MAAM;AAChB,aAAK,SAAS,OAAO,QAAQ;AAC7B,aAAK,OAAO;AAAA,MACd;AACA,6BAAuB,MAAM,OAAO;AACpC,UAAI,QAAQ,WAAY,gCAA+B,YAAY,MAAM,OAAO;AAChF,UAAI,QAAQ,WAAW,QAAW;AAChC,cAAM,SAAS,IAAI,QAAQ;AAC3B,eAAO,UAAU,QAAQ,MAAM;AAC/B,aAAK,aAAa,MAAM;AAAA,MAC1B,OAAO;AACL,YAAI,QAAQ,gBAAgB,QAAW;AACrC,eAAK,SAAS,UAAU,QAAQ,WAAW;AAAA,QAC7C;AACA,YAAI,QAAQ,aAAa,QAAW;AAClC,eAAK,WAAW,UAAU,QAAQ,QAAQ;AAAA,QAC5C;AACA,YAAI,QAAQ,UAAU,QAAW;AAC/B,eAAK,MAAM,UAAU,QAAQ,KAAK;AAAA,QACpC;AAAA,MACF;AACA,UAAI,CAAC,OAAO,aAAa,IAAI,IAAI,GAAG;AAClC,eAAO,aAAa,IAAI,MAAM,CAAC,CAAC;AAAA,MAClC;AACA,aAAO,aAAa,IAAI,IAAI,EAAE,QAAQ;AACtC,aAAO;AAAA,IACT,CAAC;AACD,WAAO,KAAK,UAAU,SAAS;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,UAAU,YAAY;AACpB,UAAM,aAAa,KAAK;AACxB,UAAM,WAAW,KAAK,KAAK,OAAO,UAAU;AAC5C,UAAM,SAAS;AAIf,UAAM,QAAQ,IAAI,MAAM;AACxB,QAAI,SAAS,KAAM,OAAM,OAAO,OAAO,iBAAiB,SAAS,IAAI;AACrE,2BAAuB,OAAO,QAAQ;AACtC,QAAI,SAAS,WAAY,gCAA+B,YAAY,OAAO,QAAQ;AACnF,UAAM,UAAU,SAAS,SAAS,CAAC;AACnC,UAAM,UAAU,CAAC;AACjB,aAAS,IAAI,GAAG,KAAK,QAAQ,QAAQ,IAAI,IAAI,KAAK;AAChD,cAAQ,KAAK,OAAO,cAAc,QAAQ,QAAQ,CAAC,CAAC,CAAC;AAAA,IACvD;AACA,WAAO,QAAQ,IAAI,OAAO,EAAE,KAAK,SAAU,OAAO;AAChD,eAAS,IAAI,GAAG,KAAK,MAAM,QAAQ,IAAI,IAAI,KAAK;AAC9C,cAAM,IAAI,MAAM,CAAC,CAAC;AAAA,MACpB;AAIA,YAAM,qBAAqB,UAAQ;AACjC,cAAM,sBAAsB,oBAAI,IAAI;AACpC,mBAAW,CAAC,KAAK,KAAK,KAAK,OAAO,cAAc;AAC9C,cAAI,eAAe,YAAY,eAAe,SAAS;AACrD,gCAAoB,IAAI,KAAK,KAAK;AAAA,UACpC;AAAA,QACF;AACA,aAAK,SAAS,CAAAC,UAAQ;AACpB,gBAAM,WAAW,OAAO,aAAa,IAAIA,KAAI;AAC7C,cAAI,YAAY,MAAM;AACpB,gCAAoB,IAAIA,OAAM,QAAQ;AAAA,UACxC;AAAA,QACF,CAAC;AACD,eAAO;AAAA,MACT;AACA,aAAO,eAAe,mBAAmB,KAAK;AAC9C,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AAAA,EACA,uBAAuB,MAAM,eAAe,gBAAgB,SAAS,QAAQ;AAC3E,UAAM,SAAS,CAAC;AAChB,UAAM,aAAa,KAAK,OAAO,KAAK,OAAO,KAAK;AAChD,UAAM,cAAc,CAAC;AACrB,QAAI,gBAAgB,OAAO,IAAI,MAAM,gBAAgB,SAAS;AAC5D,WAAK,SAAS,SAAU,QAAQ;AAC9B,YAAI,OAAO,uBAAuB;AAChC,sBAAY,KAAK,OAAO,OAAO,OAAO,OAAO,OAAO,IAAI;AAAA,QAC1D;AAAA,MACF,CAAC;AAAA,IACH,OAAO;AACL,kBAAY,KAAK,UAAU;AAAA,IAC7B;AACA,QAAI;AACJ,YAAQ,gBAAgB,OAAO,IAAI,GAAG;AAAA,MACpC,KAAK,gBAAgB;AACnB,6BAAqB;AACrB;AAAA,MACF,KAAK,gBAAgB;AACnB,6BAAqB;AACrB;AAAA,MACF,KAAK,gBAAgB;AAAA,MACrB,KAAK,gBAAgB;AACnB,6BAAqB;AACrB;AAAA,MACF;AACE,gBAAQ,eAAe,UAAU;AAAA,UAC/B,KAAK;AACH,iCAAqB;AACrB;AAAA,UACF,KAAK;AAAA,UACL,KAAK;AAAA,UACL;AACE,iCAAqB;AACrB;AAAA,QACJ;AACA;AAAA,IACJ;AACA,UAAM,gBAAgB,QAAQ,kBAAkB,SAAY,cAAc,QAAQ,aAAa,IAAI;AACnG,UAAM,cAAc,KAAK,sBAAsB,cAAc;AAC7D,aAAS,IAAI,GAAG,KAAK,YAAY,QAAQ,IAAI,IAAI,KAAK;AACpD,YAAM,QAAQ,IAAI,mBAAmB,YAAY,CAAC,IAAI,MAAM,gBAAgB,OAAO,IAAI,GAAG,cAAc,OAAO,aAAa,aAAa;AAGzI,UAAI,QAAQ,kBAAkB,eAAe;AAC3C,aAAK,mCAAmC,KAAK;AAAA,MAC/C;AACA,aAAO,KAAK,KAAK;AAAA,IACnB;AACA,WAAO;AAAA,EACT;AAAA,EACA,sBAAsB,UAAU;AAC9B,QAAI,cAAc,SAAS;AAC3B,QAAI,SAAS,YAAY;AACvB,YAAM,QAAQ,4BAA4B,YAAY,WAAW;AACjE,YAAM,SAAS,IAAI,aAAa,YAAY,MAAM;AAClD,eAAS,IAAI,GAAG,KAAK,YAAY,QAAQ,IAAI,IAAI,KAAK;AACpD,eAAO,CAAC,IAAI,YAAY,CAAC,IAAI;AAAA,MAC/B;AACA,oBAAc;AAAA,IAChB;AACA,WAAO;AAAA,EACT;AAAA,EACA,mCAAmC,OAAO;AACxC,UAAM,oBAAoB,SAAS,wCAAwC,QAAQ;AAKjF,YAAM,kBAAkB,gBAAgB,0BAA0B,uCAAuC;AACzG,aAAO,IAAI,gBAAgB,KAAK,OAAO,KAAK,QAAQ,KAAK,aAAa,IAAI,GAAG,MAAM;AAAA,IACrF;AAGA,UAAM,kBAAkB,4CAA4C;AAAA,EACtE;AACF;AASA,SAAS,cAAc,UAAU,cAAc,QAAQ;AACrD,QAAM,aAAa,aAAa;AAChC,QAAM,MAAM,IAAI,KAAK;AACrB,MAAI,WAAW,aAAa,QAAW;AACrC,UAAM,WAAW,OAAO,KAAK,UAAU,WAAW,QAAQ;AAC1D,UAAM,MAAM,SAAS;AACrB,UAAM,MAAM,SAAS;AAIrB,QAAI,QAAQ,UAAa,QAAQ,QAAW;AAC1C,UAAI,IAAI,IAAI,QAAQ,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,QAAQ,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;AAChF,UAAI,SAAS,YAAY;AACvB,cAAM,WAAW,4BAA4B,sBAAsB,SAAS,aAAa,CAAC;AAC1F,YAAI,IAAI,eAAe,QAAQ;AAC/B,YAAI,IAAI,eAAe,QAAQ;AAAA,MACjC;AAAA,IACF,OAAO;AACL,cAAQ,KAAK,qEAAqE;AAClF;AAAA,IACF;AAAA,EACF,OAAO;AACL;AAAA,EACF;AACA,QAAM,UAAU,aAAa;AAC7B,MAAI,YAAY,QAAW;AACzB,UAAM,kBAAkB,IAAI,QAAQ;AACpC,UAAM,SAAS,IAAI,QAAQ;AAC3B,aAAS,IAAI,GAAG,KAAK,QAAQ,QAAQ,IAAI,IAAI,KAAK;AAChD,YAAM,SAAS,QAAQ,CAAC;AACxB,UAAI,OAAO,aAAa,QAAW;AACjC,cAAM,WAAW,OAAO,KAAK,UAAU,OAAO,QAAQ;AACtD,cAAM,MAAM,SAAS;AACrB,cAAM,MAAM,SAAS;AAIrB,YAAI,QAAQ,UAAa,QAAQ,QAAW;AAE1C,iBAAO,KAAK,KAAK,IAAI,KAAK,IAAI,IAAI,CAAC,CAAC,GAAG,KAAK,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC;AACxD,iBAAO,KAAK,KAAK,IAAI,KAAK,IAAI,IAAI,CAAC,CAAC,GAAG,KAAK,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC;AACxD,iBAAO,KAAK,KAAK,IAAI,KAAK,IAAI,IAAI,CAAC,CAAC,GAAG,KAAK,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC;AACxD,cAAI,SAAS,YAAY;AACvB,kBAAM,WAAW,4BAA4B,sBAAsB,SAAS,aAAa,CAAC;AAC1F,mBAAO,eAAe,QAAQ;AAAA,UAChC;AAMA,0BAAgB,IAAI,MAAM;AAAA,QAC5B,OAAO;AACL,kBAAQ,KAAK,qEAAqE;AAAA,QACpF;AAAA,MACF;AAAA,IACF;AAGA,QAAI,eAAe,eAAe;AAAA,EACpC;AACA,WAAS,cAAc;AACvB,QAAM,SAAS,IAAI,OAAO;AAC1B,MAAI,UAAU,OAAO,MAAM;AAC3B,SAAO,SAAS,IAAI,IAAI,WAAW,IAAI,GAAG,IAAI;AAC9C,WAAS,iBAAiB;AAC5B;AAUA,SAAS,uBAAuB,UAAU,cAAc,QAAQ;AAC9D,QAAM,aAAa,aAAa;AAChC,QAAM,UAAU,CAAC;AACjB,WAAS,wBAAwB,eAAe,eAAe;AAC7D,WAAO,OAAO,cAAc,YAAY,aAAa,EAAE,KAAK,SAAU,UAAU;AAC9E,eAAS,aAAa,eAAe,QAAQ;AAAA,IAC/C,CAAC;AAAA,EACH;AACA,aAAW,qBAAqB,YAAY;AAC1C,UAAM,qBAAqB,WAAW,iBAAiB,KAAK,kBAAkB,YAAY;AAG1F,QAAI,sBAAsB,SAAS,WAAY;AAC/C,YAAQ,KAAK,wBAAwB,WAAW,iBAAiB,GAAG,kBAAkB,CAAC;AAAA,EACzF;AACA,MAAI,aAAa,YAAY,UAAa,CAAC,SAAS,OAAO;AACzD,UAAM,WAAW,OAAO,cAAc,YAAY,aAAa,OAAO,EAAE,KAAK,SAAUC,WAAU;AAC/F,eAAS,SAASA,SAAQ;AAAA,IAC5B,CAAC;AACD,YAAQ,KAAK,QAAQ;AAAA,EACvB;AACA,MAAI,gBAAgB,sBAAsB,wBAAwB,aAAa,YAAY;AACzF,YAAQ,KAAK,qEAAqE,gBAAgB,iBAAiB,kBAAkB;AAAA,EACvI;AACA,yBAAuB,UAAU,YAAY;AAC7C,gBAAc,UAAU,cAAc,MAAM;AAC5C,SAAO,QAAQ,IAAI,OAAO,EAAE,KAAK,WAAY;AAC3C,WAAO,aAAa,YAAY,SAAY,gBAAgB,UAAU,aAAa,SAAS,MAAM,IAAI;AAAA,EACxG,CAAC;AACH;", "names": ["self", "res", "sourceURI", "node", "accessor"]}