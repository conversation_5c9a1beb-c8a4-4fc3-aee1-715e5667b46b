package com.fed.platform.sinister.controller;

import java.util.List;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.fed.platform.sinister.domain.CasDeBareme;
import com.fed.platform.sinister.service.CasDeBaremeService;

@RestController
@RequestMapping("/api/cas-de-bareme")
@CrossOrigin(origins = "http://localhost:4200")
public class CasDeBaremeController {

    private final CasDeBaremeService service;

    public CasDeBaremeController(CasDeBaremeService service) {
        this.service = service;
    }

    @GetMapping
    public ResponseEntity<List<CasDeBareme>> getAll() {
        return ResponseEntity.ok(service.findAll());
    }

    @PostMapping
    public ResponseEntity<CasDeBareme> create(@RequestBody CasDeBareme casDeBareme) {
        return ResponseEntity.ok(service.create(casDeBareme));
    }

    @PutMapping("/{id}")
    public ResponseEntity<CasDeBareme> update(@PathVariable Long id, @RequestBody CasDeBareme casDeBareme) {
        return ResponseEntity.ok(service.update(id, casDeBareme));
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> delete(@PathVariable Long id) {
        service.delete(id);
        return ResponseEntity.noContent().build();
    }
}