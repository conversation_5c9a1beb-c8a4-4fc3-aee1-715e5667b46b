<div class="container">
  <!-- Loading overlay -->
  @if (isLoading) {
    <div class="loading-overlay">
      <div class="spinner"></div>
    </div>
  }

  <!-- Success and error messages -->
  @if (successMessage) {
    <div class="alert alert-success">
      {{ successMessage }}
      <button type="button" class="close" (click)="successMessage = null">&times;</button>
    </div>
  }

  @if (errorMessage) {
    <div class="alert alert-danger">
      {{ errorMessage }}
      <button type="button" class="close" (click)="errorMessage = null">&times;</button>
    </div>
  }

  <div class="header">
    <h2>Gestion des Documents</h2>
    <button class="create-btn" (click)="openEventModal()" [disabled]="isLoading">
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
        <path d="M12 5V19M5 12H19" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
      Ajouter un document
    </button>
  </div>

  <div class="table-container">
    <div class="table-responsive">
      <table>
        <thead>
          <tr>
            <th>Libellé Événement</th>
            <th>Documents</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          @for (event of uniqueEvents; track event.id) {
            <tr>
              <td>{{ event.libelle }}</td>
              <td class="documents-cell">
                @if (getDocumentsForEvent(event.libelle).length > 0) {
                  @for (doc of getDocumentsForEvent(event.libelle); track doc.id) {
                    <div class="document-item">
                      <span class="document-name">{{ doc.document }}</span>
                      <button class="delete-doc-btn" (click)="deleteDocument(doc.id)" title="Supprimer document">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                          <path d="M3 6H5H21" stroke="#FF4B55" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                          <path d="M8 6V4C8 3.46957 8.21071 2.96086 8.58579 2.58579C8.96086 2.21071 9.46957 2 10 2H14C14.5304 2 15.0391 2.21071 15.4142 2.58579C15.7893 2.96086 16 3.46957 16 4V6M19 6V20C19 20.5304 18.7893 21.0391 18.4142 21.4142C18.0391 21.7893 17.5304 22 17 22H7C6.46957 22 5.96086 21.7893 5.58579 21.4142C5.21071 21.0391 5 20.5304 5 20V6H19Z" stroke="#FF4B55" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                          <path d="M10 11V17" stroke="#FF4B55" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                          <path d="M14 11V17" stroke="#FF4B55" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                      </button>
                    </div>
                  }
                } @else {
                  <span class="no-documents">Aucun document</span>
                }
               
              </td>
              <td class="actions">
                <button class="icon-btn edit" (click)="openEventModal(event)" title="Modifier">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <path d="M11 4H4C3.46957 4 2.96086 4.21071 2.58579 4.58579C2.21071 4.96086 2 5.46957 2 6V20C2 20.5304 2.21071 21.0391 2.58579 21.4142C2.96086 21.7893 3.46957 22 4 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V13" stroke="#1C3F93" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M18.5 2.50001C18.8978 2.10219 19.4374 1.87869 20 1.87869C20.5626 1.87869 21.1022 2.10219 21.5 2.50001C21.8978 2.89784 22.1213 3.4374 22.1213 4.00001C22.1213 4.56262 21.8978 5.10219 21.5 5.50001L12 15L8 16L9 12L18.5 2.50001Z" stroke="#1C3F93" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                </button>
              </td>
            </tr>
          }
        </tbody>
      </table>
    </div>
  </div>

  <!-- Modal -->
  @if (showEventModal) {
    <div class="modal">
      <div class="modal-backdrop" (click)="closeModal()"></div>
      <div class="modal-content">
        <div class="modal-header">
          <h3>Ajouter un document sinistre</h3>
          <button class="close-btn" (click)="closeModal()" [disabled]="isLoading">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
              <path d="M18 6L6 18" stroke="#6B7280" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M6 6L18 18" stroke="#6B7280" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </button>
        </div>

        @if (errorMessage) {
          <div class="alert alert-danger">
            {{ errorMessage }}
          </div>
        }

        <form [formGroup]="documentForm" (ngSubmit)="onSubmit()">
          <div class="form-group">
            <label>
              Libellé de l'événement
              <span class="required">*</span>
            </label>
            <div class="input-wrapper">
              <input type="text" formControlName="libelleEvenement" placeholder="Entrez le libellé de l'événement">
            </div>
            @if (documentForm.get('libelleEvenement')?.invalid && documentForm.get('libelleEvenement')?.touched) {
              <div class="error-message">
                @if (documentForm.get('libelleEvenement')?.errors?.['required']) {
                  <span>Le libellé est requis</span>
                }
              </div>
            }
          </div>

          <div class="form-group">
            <label>
              Nom du document
              <span class="required">*</span>
            </label>
            <div class="input-wrapper">
              <input type="text" formControlName="document" placeholder="Entrez le nom du document">
            </div>
            @if (documentForm.get('document')?.invalid && documentForm.get('document')?.touched) {
              <div class="error-message">
                @if (documentForm.get('document')?.errors?.['required']) {
                  <span>Le nom du document est requis</span>
                }
              </div>
            }
          </div>



          <div class="modal-footer">
            <button type="button" class="btn-secondary" (click)="closeModal()" [disabled]="isLoading">
              Annuler
            </button>
            <button type="submit" class="btn-primary" [disabled]="documentForm.invalid || isLoading">
              @if (isLoading) {
                <span class="spinner-btn"></span>
              }
              Ajouter
            </button>
          </div>
        </form>
      </div>
    </div>
  }

  <!-- Document Modal -->
  <div class="modal-overlay" *ngIf="showDocumentModal">
    <div class="modal">
      <div class="modal-header">
        <h3>Ajouter un Document</h3>
        <button class="close-btn" (click)="closeModal()">&times;</button>
      </div>
      <div class="modal-body">
        <div class="form-group">
          <label for="selectedEventName">Événement:</label>
          <input type="text" id="selectedEventName" [value]="selectedEvent?.libelle" readonly>
        </div>
        <div class="form-group">
          <label for="newDocumentName">Nom du document:</label>
          <input type="text" id="newDocumentName" [(ngModel)]="newDocumentName"
                 placeholder="Entrez le nom du document">
        </div>
        <div class="form-actions">
          <button type="button" class="cancel-btn" (click)="closeModal()">Annuler</button>
          <button type="button" class="submit-btn" (click)="onSubmitDocument()" [disabled]="!newDocumentName">
            Ajouter
          </button>
        </div>
      </div>
    </div>
  </div>
</div>