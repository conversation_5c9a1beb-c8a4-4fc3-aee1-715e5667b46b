import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { inject } from '@angular/core';

@Component({
  selector: 'app-final-3d',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div style="width: 100vw; height: 100vh; background: #f5f5f5; font-family: Arial, sans-serif;">
      <!-- Header -->
      <div style="background: #2c3e50; color: white; padding: 20px; display: flex; justify-content: space-between; align-items: center;">
        <div>
          <h1 style="margin: 0; font-size: 24px;">🚗 3D Vehicle Model Viewer</h1>
          <p style="margin: 5px 0 0 0; opacity: 0.8;">Session: {{sessionId}}</p>
        </div>
        <button onclick="window.close()" style="padding: 10px 20px; background: #e74c3c; color: white; border: none; border-radius: 5px; cursor: pointer;">
          ❌ Close
        </button>
      </div>

      <!-- Loading Phase -->
      <div *ngIf="isLoading" style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: calc(100vh - 100px);">
        <div style="text-align: center; max-width: 500px;">
          <!-- Progress Circle -->
          <div style="width: 100px; height: 100px; border: 8px solid #f3f3f3; border-top: 8px solid #3498db; border-radius: 50%; animation: spin 1s linear infinite; margin: 0 auto 30px;"></div>
          
          <!-- Status -->
          <h2 style="color: #2c3e50; margin: 0 0 10px 0;">{{statusMessage}}</h2>
          <p style="color: #666; margin: 0 0 20px 0;">{{detailMessage}}</p>
          
          <!-- Progress Bar -->
          <div style="width: 100%; height: 12px; background: #ddd; border-radius: 6px; overflow: hidden; margin: 20px 0;">
            <div style="height: 100%; background: linear-gradient(90deg, #3498db, #2ecc71); transition: width 0.5s ease;" [style.width.%]="progress"></div>
          </div>
          <p style="color: #2c3e50; font-weight: bold;">{{progress}}%</p>
          
          <!-- CSM.ai Status -->
          <div *ngIf="csmStatus" style="background: #e8f5e8; border: 1px solid #27ae60; border-radius: 8px; padding: 15px; margin: 20px 0;">
            <p style="margin: 0; color: #27ae60;"><strong>CSM.ai Status:</strong> {{csmStatus}}</p>
            <p *ngIf="csmProgress > 0" style="margin: 5px 0 0 0; color: #666;">Generation Progress: {{csmProgress}}%</p>
          </div>
          
          <!-- Error -->
          <div *ngIf="errorMessage" style="background: #ffebee; border: 1px solid #f44336; border-radius: 8px; padding: 15px; margin: 20px 0;">
            <p style="margin: 0 0 10px 0; color: #d32f2f;">❌ {{errorMessage}}</p>
            <button (click)="startDemo()" style="padding: 10px 20px; background: #f39c12; color: white; border: none; border-radius: 5px; cursor: pointer;">
              🎭 Use Demo Mode
            </button>
          </div>
        </div>
      </div>

      <!-- 3D Viewer Phase -->
      <div *ngIf="!isLoading" style="display: flex; height: calc(100vh - 100px);">
        <!-- Canvas Area -->
        <div style="flex: 2; padding: 20px;">
          <div style="background: white; border-radius: 10px; padding: 20px; height: 100%; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
              <h3 style="margin: 0; color: #2c3e50;">Vehicle Damage Assessment</h3>
              <span style="background: #27ae60; color: white; padding: 5px 15px; border-radius: 20px; font-size: 12px;">
                {{modelType}}
              </span>
            </div>
            
            <canvas #canvas width="700" height="450" 
                    style="border: 2px solid #3498db; border-radius: 8px; cursor: crosshair; display: block; margin: 0 auto; background: #f8f9fa;"
                    (click)="addDamagePoint($event)"></canvas>
            
            <div style="text-align: center; margin: 15px 0;">
              <p style="color: #666; margin: 0;">Click on the vehicle to mark damage points</p>
              <p *ngIf="glbUrl" style="color: #27ae60; margin: 5px 0 0 0; font-size: 12px;">
                ✅ 3D Model URL: <a [href]="glbUrl" target="_blank" style="color: #3498db;">{{glbUrl.substring(0, 50)}}...</a>
              </p>
            </div>
          </div>
        </div>

        <!-- Claims Panel -->
        <div style="flex: 1; padding: 20px;">
          <div style="background: white; border-radius: 10px; padding: 20px; height: 100%; box-shadow: 0 4px 15px rgba(0,0,0,0.1); overflow-y: auto;">
            <h3 style="margin: 0 0 20px 0; color: #2c3e50;">Damage Claims ({{damagePoints.length}})</h3>
            
            <!-- Damage Points List -->
            <div *ngFor="let damage of damagePoints; let i = index" style="background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px; padding: 15px; margin-bottom: 15px;">
              <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                <span style="background: #3498db; color: white; width: 30px; height: 30px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: bold; font-size: 14px;">
                  {{i + 1}}
                </span>
                <button (click)="removeDamage(i)" style="background: #e74c3c; color: white; border: none; border-radius: 5px; padding: 5px 10px; cursor: pointer; font-size: 12px;">
                  Remove
                </button>
              </div>
              <p style="margin: 5px 0; font-size: 13px; color: #666;">
                <strong>Position:</strong> ({{damage.x}}, {{damage.y}})
              </p>
              <p style="margin: 5px 0; font-size: 13px; color: #666;">
                <strong>Type:</strong> {{damage.type}}
              </p>
              <p style="margin: 5px 0; font-size: 13px; color: #666;">
                <strong>Time:</strong> {{damage.timestamp | date:'short'}}
              </p>
            </div>

            <!-- Actions -->
            <div *ngIf="damagePoints.length > 0" style="margin-top: 20px;">
              <button (click)="clearAllDamages()" style="width: 100%; padding: 12px; background: #e74c3c; color: white; border: none; border-radius: 5px; cursor: pointer; margin-bottom: 10px; font-weight: bold;">
                🗑️ Clear All
              </button>
              <button (click)="exportClaims()" style="width: 100%; padding: 12px; background: #27ae60; color: white; border: none; border-radius: 5px; cursor: pointer; font-weight: bold;">
                💾 Export Claims
              </button>
            </div>

            <!-- Session Info -->
            <div style="margin-top: 30px; padding: 15px; background: #e8f4fd; border-radius: 8px; font-size: 12px;">
              <h4 style="margin: 0 0 10px 0; color: #2c3e50;">Session Information</h4>
              <p style="margin: 3px 0;"><strong>ID:</strong> {{sessionId}}</p>
              <p style="margin: 3px 0;"><strong>Status:</strong> {{finalStatus}}</p>
              <p style="margin: 3px 0;"><strong>Model:</strong> {{modelType}}</p>
              <p *ngIf="glbUrl" style="margin: 3px 0;"><strong>3D File:</strong> Available</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <style>
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    </style>
  `
})
export class Final3DComponent {
  sessionId = '';
  isLoading = true;
  progress = 0;
  statusMessage = 'Initializing 3D model...';
  detailMessage = 'Connecting to CSM.ai API...';
  errorMessage = '';
  csmStatus = '';
  csmProgress = 0;
  finalStatus = 'Loading';
  modelType = 'Demo Vehicle';
  glbUrl = '';
  damagePoints: any[] = [];
  
  private http = inject(HttpClient);
  private pollInterval: any;

  constructor() {
    // Get session from URL
    const params = new URLSearchParams(window.location.search);
    this.sessionId = params.get('sessionId') || 'demo';
    
    console.log('🚗 Final 3D Component started for session:', this.sessionId);
    this.startProcess();
  }

  ngOnDestroy() {
    if (this.pollInterval) {
      clearInterval(this.pollInterval);
    }
  }

  startProcess() {
    if (this.sessionId.startsWith('SESSION_demo_')) {
      this.startDemo();
    } else {
      this.startRealSession();
    }
  }

  startDemo() {
    this.statusMessage = 'Demo Mode';
    this.detailMessage = 'Loading demo vehicle model...';
    this.modelType = 'Demo Vehicle';
    this.finalStatus = 'Demo';
    this.errorMessage = '';
    
    let progress = 0;
    const interval = setInterval(() => {
      progress += 15;
      this.progress = progress;
      
      if (progress >= 100) {
        clearInterval(interval);
        this.finalStatus = 'Ready';
        this.isLoading = false;
        setTimeout(() => this.drawVehicle(), 100);
      }
    }, 200);
  }

  startRealSession() {
    this.statusMessage = 'Processing CSM.ai Session';
    this.detailMessage = 'Checking session status...';
    this.modelType = 'CSM.ai Generated';
    this.finalStatus = 'Processing';
    
    // Start polling CSM.ai API
    this.pollCsmApi();
  }

  pollCsmApi() {
    const apiUrl = `https://api.csm.ai/v3/sessions/${this.sessionId}`;
    
    const poll = () => {
      console.log('🔄 Polling CSM.ai for session:', this.sessionId);
      
      this.http.get<any>(apiUrl, {
        headers: {
          'x-api-key': '1e4a8A8a44a9b792A002990e6E661c25',
          'Content-Type': 'application/json'
        }
      }).subscribe({
        next: (data: any) => {
          console.log('📊 CSM.ai response:', data);
          this.csmStatus = data.status;
          
          // Check for progress in jobs
          if (data.output?.meshes?.[0]?.jobs?.[0]?.last_percent) {
            this.csmProgress = Math.round(data.output.meshes[0].jobs[0].last_percent);
            this.progress = Math.min(this.csmProgress, 95);
          }
          
          switch (data.status) {
            case 'incomplete':
              this.detailMessage = 'Model generation in progress...';
              this.progress = Math.max(this.progress, 20);
              break;
              
            case 'complete':
              this.progress = 100;
              this.detailMessage = 'Model generation complete!';
              this.finalStatus = 'Complete';
              
              // Get GLB URL
              if (data.output?.meshes?.[0]?.data?.glb_url) {
                this.glbUrl = data.output.meshes[0].data.glb_url;
                this.modelType = 'CSM.ai 3D Model';
                console.log('✅ GLB URL found:', this.glbUrl);
              }
              
              setTimeout(() => {
                this.isLoading = false;
                setTimeout(() => this.drawVehicle(), 100);
              }, 1000);
              return; // Stop polling
              
            case 'failed':
              this.errorMessage = 'CSM.ai generation failed';
              this.finalStatus = 'Failed';
              return; // Stop polling
          }
          
          // Continue polling
          setTimeout(poll, 5000);
        },
        error: (error: any) => {
          console.error('❌ CSM.ai API error:', error);
          this.errorMessage = 'Failed to connect to CSM.ai API';
          this.finalStatus = 'Error';
        }
      });
    };
    
    // Start polling
    poll();
  }

  drawVehicle() {
    const canvas = document.querySelector('canvas') as HTMLCanvasElement;
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d')!;
    const width = canvas.width;
    const height = canvas.height;
    
    // Clear canvas
    ctx.clearRect(0, 0, width, height);
    
    // Draw vehicle
    const centerX = width / 2;
    const centerY = height / 2;
    const carWidth = 250;
    const carHeight = 120;
    
    // Car body
    ctx.fillStyle = '#4a90e2';
    ctx.fillRect(centerX - carWidth/2, centerY - carHeight/2, carWidth, carHeight);
    ctx.strokeStyle = '#2c3e50';
    ctx.lineWidth = 3;
    ctx.strokeRect(centerX - carWidth/2, centerY - carHeight/2, carWidth, carHeight);
    
    // Windows
    ctx.fillStyle = '#87ceeb';
    ctx.fillRect(centerX - 100, centerY - 40, 200, 80);
    ctx.strokeRect(centerX - 100, centerY - 40, 200, 80);
    
    // Wheels
    ctx.fillStyle = '#333';
    ctx.beginPath();
    ctx.arc(centerX - 80, centerY + 50, 18, 0, 2 * Math.PI);
    ctx.fill();
    ctx.beginPath();
    ctx.arc(centerX + 80, centerY + 50, 18, 0, 2 * Math.PI);
    ctx.fill();
    
    // Draw damage points
    this.damagePoints.forEach((damage, index) => {
      ctx.fillStyle = '#e74c3c';
      ctx.beginPath();
      ctx.arc(damage.x, damage.y, 12, 0, 2 * Math.PI);
      ctx.fill();
      
      ctx.fillStyle = 'white';
      ctx.font = 'bold 14px Arial';
      ctx.textAlign = 'center';
      ctx.fillText((index + 1).toString(), damage.x, damage.y + 5);
    });
    
    console.log('✅ Vehicle drawn successfully');
  }

  addDamagePoint(event: MouseEvent) {
    const canvas = event.target as HTMLCanvasElement;
    const rect = canvas.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;
    
    const damage = {
      x: Math.round(x),
      y: Math.round(y),
      type: 'Impact Damage',
      timestamp: new Date()
    };
    
    this.damagePoints.push(damage);
    this.drawVehicle();
    
    console.log('📍 Damage point added:', damage);
  }

  removeDamage(index: number) {
    this.damagePoints.splice(index, 1);
    this.drawVehicle();
  }

  clearAllDamages() {
    this.damagePoints = [];
    this.drawVehicle();
  }

  exportClaims() {
    const exportData = {
      sessionId: this.sessionId,
      modelType: this.modelType,
      glbUrl: this.glbUrl,
      damagePoints: this.damagePoints,
      exportTime: new Date(),
      totalClaims: this.damagePoints.length
    };
    
    console.log('💾 Exporting claims:', exportData);
    
    // Create downloadable JSON
    const dataStr = JSON.stringify(exportData, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `damage-claims-${this.sessionId}.json`;
    link.click();
    
    alert(`✅ ${this.damagePoints.length} damage claims exported!\n\nFile: damage-claims-${this.sessionId}.json`);
  }
}
