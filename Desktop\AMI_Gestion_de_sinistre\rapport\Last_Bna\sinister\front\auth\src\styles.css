@import '@angular/material/prebuilt-themes/indigo-pink.css';

body {
  font-family: '<PERSON><PERSON><PERSON><PERSON>', sans-serif;
  margin: 0;
  padding: 0;
  background-color: #fbfafa;
}

/* Material Theme Customization */
.mat-mdc-form-field {
  font-family: '<PERSON><PERSON><PERSON><PERSON>', sans-serif !important;
}

.mat-calendar-body-selected {
  background-color: #1C3F93 !important;
  color: white !important;
}

.mat-calendar-body-today:not(.mat-calendar-body-selected) {
  border-color: #1C3F93 !important;
}

.mat-datepicker-toggle {
  color: #1C3F93 !important;
}

.mat-mdc-form-field-appearance-outline .mat-mdc-form-field-outline-thick {
  color: #1C3F93 !important;
}