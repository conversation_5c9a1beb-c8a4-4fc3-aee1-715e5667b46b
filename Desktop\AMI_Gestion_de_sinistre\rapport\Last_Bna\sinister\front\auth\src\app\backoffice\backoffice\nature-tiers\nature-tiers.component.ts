// nature-tiers.component.ts
import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { NatureTiersService } from './natures-tiers.service';

@Component({
  selector: 'app-nature-tiers',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './nature-tiers.component.html',
  styleUrls: ['./nature-tiers.component.css']
})
export class NatureTiersComponent implements OnInit {
  natures: any[] = [];
  form = { code: '', libelle: '' };
  showModal = false;
  isEdit = false;

  constructor(private natureService: NatureTiersService) {}

  ngOnInit(): void {
    this.loadNatures();
  }

  loadNatures() {
    this.natureService.getAll().subscribe(data => this.natures = data);
  }

  openModal() {
    this.showModal = true;
    this.form = { code: '', libelle: '' };
    this.isEdit = false;
  }

  closeModal() {
    this.showModal = false;
  }

  saveNature() {
    if (this.isEdit) {
      this.natureService.update(this.form.code, this.form).subscribe(() => {
        this.loadNatures();
        this.closeModal();
      });
    } else {
      this.natureService.create(this.form).subscribe(() => {
        this.loadNatures();
        this.closeModal();
      });
    }
  }

  editNature(nature: any) {
    this.form = { ...nature };
    this.isEdit = true;
    this.showModal = true;
  }

  deleteNature(code: string) {
    if (confirm('Supprimer cette nature ?')) {
      this.natureService.delete(code).subscribe(() => this.loadNatures());
    }
  }
}