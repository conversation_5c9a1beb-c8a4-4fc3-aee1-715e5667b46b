package com.fed.platform.sinister.controller;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fed.platform.sinister.domain.OuvertureSinistre;
import com.fed.platform.sinister.dto.OuvertureSinistreDTO;
import com.fed.platform.sinister.service.OuvertureSinistreService;

import jakarta.validation.Valid;

@RestController
@RequestMapping("/api/ouverture-sinistre")
public class OuvertureSinistreController {

    private static final Logger logger = LoggerFactory.getLogger(OuvertureSinistreController.class);

    @Autowired
    private OuvertureSinistreService ouvertureSinistreService;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * Create new ouverture sinistre with form data and files
     */
    @PostMapping(consumes = { "multipart/form-data" })
    public ResponseEntity<?> createOuvertureSinistre(
            @RequestParam("formData") String formDataJson,
            @RequestParam(value = "vehiclePhotos", required = false) MultipartFile[] vehiclePhotos,
            @RequestParam(value = "attachments", required = false) MultipartFile[] attachments) {

        try {
            logger.info("🔍 Creating new ouverture sinistre");
            logger.info("📄 Form data JSON: {}", formDataJson);

            // Parse form data
            OuvertureSinistreDTO dto = objectMapper.readValue(formDataJson, OuvertureSinistreDTO.class);
            logger.info("✅ Parsed DTO: {}", dto);

            // Convert DTO to Entity
            OuvertureSinistre ouvertureSinistre = convertDtoToEntity(dto);

            // Process vehicle photos
            if (vehiclePhotos != null && vehiclePhotos.length > 0) {
                logger.info("📸 Processing {} vehicle photos", vehiclePhotos.length);
                // For now, we'll store the first photo. In production, you might want to store
                // all
                byte[] photoData = ouvertureSinistreService.processPhotoUpload(vehiclePhotos[0]);
                ouvertureSinistre.setPhotosDeVehicule(photoData);
            } else if (dto.getVehiclePhotoNames() != null && !dto.getVehiclePhotoNames().isEmpty()) {
                // Store photo names as comma-separated string
                String photoNames = String.join(",", dto.getVehiclePhotoNames());
                logger.info("📸 Storing vehicle photo names: {}", photoNames);
                // For now, store as string in a text field (you might want to create a separate
                // table)
                ouvertureSinistre.setLinkVehicule(ouvertureSinistre.getLinkVehicule() + " | Photos: " + photoNames);
            }

            // Process attachments
            if (attachments != null && attachments.length > 0) {
                logger.info("📎 Processing {} attachments", attachments.length);
                // For now, we'll store the first attachment. In production, you might want to
                // store all
                byte[] attachmentData = ouvertureSinistreService.processAttachmentUpload(attachments[0]);
                ouvertureSinistre.setAttachement(attachmentData);
            } else if (dto.getDocumentNames() != null && !dto.getDocumentNames().isEmpty()) {
                // Store document names as comma-separated string
                String documentNames = String.join(",", dto.getDocumentNames());
                logger.info("📎 Storing document names: {}", documentNames);
                ouvertureSinistre.setDocumentNames(documentNames);
            }

            // Note: Damage points processing removed as dommage field was removed from
            // database

            // Save to database
            OuvertureSinistre saved = ouvertureSinistreService.save(ouvertureSinistre);

            // Prepare response
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Ouverture sinistre créée avec succès");
            response.put("numeroSinistre", saved.getNumeroSinistre());
            response.put("id", saved.getId());
            response.put("data", saved);

            logger.info("✅ Ouverture sinistre created successfully: {}", saved.getNumeroSinistre());

            return ResponseEntity.ok(response);

        } catch (IOException e) {
            logger.error("❌ Error parsing form data: {}", e.getMessage());
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "Erreur lors du traitement des données: " + e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);

        } catch (IllegalArgumentException e) {
            logger.error("❌ Validation error: {}", e.getMessage());
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);

        } catch (Exception e) {
            logger.error("❌ Unexpected error creating ouverture sinistre: {}", e.getMessage(), e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "Erreur interne du serveur");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * Simple test endpoint
     */
    @PostMapping("/test")
    public ResponseEntity<?> testEndpoint(@RequestBody Map<String, Object> testData) {
        try {
            logger.info("🧪 Test endpoint called with data: {}", testData);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Test endpoint working");
            response.put("receivedData", testData);
            response.put("timestamp", java.time.Instant.now().toString());

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("❌ Error in test endpoint: {}", e.getMessage(), e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "Test endpoint error: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * Create ouverture sinistre with JSON data only (for testing)
     */
    @PostMapping("/json")
    public ResponseEntity<?> createOuvertureSinistreJson(@Valid @RequestBody OuvertureSinistreDTO dto) {
        try {
            logger.info("🔍 Creating ouverture sinistre from JSON: {}", dto);

            OuvertureSinistre ouvertureSinistre = convertDtoToEntity(dto);

            // Set current date if not provided
            if (ouvertureSinistre.getDateCreation() == null) {
                ouvertureSinistre.setDateCreation(java.time.Instant.now());
            }

            // Note: Damage points processing removed as dommage field was removed from
            // database

            OuvertureSinistre saved = ouvertureSinistreService.save(ouvertureSinistre);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Ouverture sinistre créée avec succès");
            response.put("numeroSinistre", saved.getNumeroSinistre());
            response.put("id", saved.getId());
            response.put("data", saved);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("❌ Error creating ouverture sinistre: {}", e.getMessage(), e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "Erreur lors de la création: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * Save 3D session data (photos + session link)
     */
    @PostMapping("/3d-session")
    public ResponseEntity<?> save3DSession(@RequestParam("sessionId") String sessionId,
            @RequestParam(value = "vehiclePhotos", required = false) MultipartFile[] vehiclePhotos) {
        try {
            logger.info("🔍 Saving 3D session: {}", sessionId);

            // Create a minimal record for 3D session
            OuvertureSinistre ouvertureSinistre = new OuvertureSinistre();
            ouvertureSinistre.setLinkVehicule(sessionId);
            ouvertureSinistre.setDateCreation(java.time.Instant.now());

            // Process vehicle photos
            if (vehiclePhotos != null && vehiclePhotos.length > 0) {
                logger.info("📸 Processing {} vehicle photos for 3D session", vehiclePhotos.length);
                byte[] photoData = ouvertureSinistreService.processPhotoUpload(vehiclePhotos[0]);
                ouvertureSinistre.setPhotosDeVehicule(photoData);
            }

            OuvertureSinistre saved = ouvertureSinistreService.save(ouvertureSinistre);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Session 3D sauvegardée avec succès");
            response.put("sessionId", sessionId);
            response.put("id", saved.getId());

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("❌ Error saving 3D session: {}", e.getMessage(), e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "Erreur lors de la sauvegarde: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * Get all ouverture sinistres
     */
    @GetMapping
    public ResponseEntity<List<OuvertureSinistre>> getAllOuvertureSinistres() {
        logger.info("🔍 Getting all ouverture sinistres");
        List<OuvertureSinistre> sinistres = ouvertureSinistreService.findAll();
        logger.info("📄 Found {} ouverture sinistres", sinistres.size());
        return ResponseEntity.ok(sinistres);
    }

    /**
     * Get ouverture sinistre by ID
     */
    @GetMapping("/{id}")
    public ResponseEntity<?> getOuvertureSinistreById(@PathVariable Long id) {
        logger.info("🔍 Getting ouverture sinistre by ID: {}", id);

        Optional<OuvertureSinistre> sinistre = ouvertureSinistreService.findById(id);
        if (sinistre.isPresent()) {
            logger.info("✅ Found ouverture sinistre: {}", sinistre.get().getNumeroSinistre());
            return ResponseEntity.ok(sinistre.get());
        } else {
            logger.info("❌ Ouverture sinistre not found with ID: {}", id);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "Ouverture sinistre non trouvée");
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * Get ouverture sinistre by numero
     */
    @GetMapping("/numero/{numeroSinistre}")
    public ResponseEntity<?> getOuvertureSinistreByNumero(@PathVariable String numeroSinistre) {
        logger.info("🔍 Getting ouverture sinistre by numero: {}", numeroSinistre);

        Optional<OuvertureSinistre> sinistre = ouvertureSinistreService.findByNumeroSinistre(numeroSinistre);
        if (sinistre.isPresent()) {
            return ResponseEntity.ok(sinistre.get());
        } else {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "Ouverture sinistre non trouvée avec le numéro: " + numeroSinistre);
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * Update ouverture sinistre
     */
    @PutMapping("/{id}")
    public ResponseEntity<?> updateOuvertureSinistre(
            @PathVariable Long id,
            @Valid @RequestBody OuvertureSinistreDTO dto) {
        try {
            logger.info("🔍 Updating ouverture sinistre with ID: {}", id);

            OuvertureSinistre ouvertureSinistre = convertDtoToEntity(dto);
            OuvertureSinistre updated = ouvertureSinistreService.update(id, ouvertureSinistre);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Ouverture sinistre mise à jour avec succès");
            response.put("data", updated);

            return ResponseEntity.ok(response);

        } catch (RuntimeException e) {
            logger.error("❌ Error updating ouverture sinistre: {}", e.getMessage());
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", e.getMessage());
            return ResponseEntity.notFound().build();

        } catch (Exception e) {
            logger.error("❌ Unexpected error updating ouverture sinistre: {}", e.getMessage(), e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "Erreur interne du serveur");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * Delete ouverture sinistre
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<?> deleteOuvertureSinistre(@PathVariable Long id) {
        try {
            logger.info("🔍 Deleting ouverture sinistre with ID: {}", id);
            ouvertureSinistreService.deleteById(id);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Ouverture sinistre supprimée avec succès");

            return ResponseEntity.ok(response);

        } catch (RuntimeException e) {
            logger.error("❌ Error deleting ouverture sinistre: {}", e.getMessage());
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", e.getMessage());
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * Convert DTO to Entity
     */
    private OuvertureSinistre convertDtoToEntity(OuvertureSinistreDTO dto) {
        OuvertureSinistre entity = new OuvertureSinistre();

        entity.setEvenement(dto.getEvenement());
        entity.setTypeDeDegat(dto.getTypeDeDegat());
        entity.setDescriptionDeDegat(dto.getDescriptionDeDegat());
        entity.setTier(dto.getTier());
        entity.setTypeDuTier(dto.getTypeDuTier());
        entity.setCompagnieTier(dto.getCompagnieTier());
        entity.setCasDeBareme(dto.getCasDeBareme());
        entity.setNombreDeTier(dto.getNombreDeTier());
        entity.setDegatEstimatif(dto.getDegatEstimatif());
        entity.setResponsabilite(dto.getResponsabilite());
        entity.setLinkVehicule(dto.getLinkVehicule());

        // Handle file names
        if (dto.getVehiclePhotoNames() != null && !dto.getVehiclePhotoNames().isEmpty()) {
            String photoNames = String.join(",", dto.getVehiclePhotoNames());
            String currentLink = entity.getLinkVehicule() != null ? entity.getLinkVehicule() : "";
            entity.setLinkVehicule(currentLink + " | Photos: " + photoNames);
        }

        if (dto.getDocumentNames() != null && !dto.getDocumentNames().isEmpty()) {
            String documentNames = String.join(",", dto.getDocumentNames());
            entity.setDocumentNames(documentNames);
        }

        return entity;
    }
}
