package com.fed.platform.sinister.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.fed.platform.sinister.domain.Garantie;

/**
 * Spring Data JPA repository for the Garantie entity.
 */
@Repository
public interface GarantieRepository extends JpaRepository<Garantie, Long> {

    /**
     * Find guarantees by libelle containing (case insensitive)
     */
    List<Garantie> findByLibelleContainingIgnoreCase(String libelle);

    /**
     * Find guarantees by code
     */
    List<Garantie> findByCode(String code);

    /**
     * Find all active guarantees (with plafond > 0)
     */
    @Query("SELECT g FROM Garantie g WHERE g.plafond > 0")
    List<Garantie> findActiveGaranties();

    /**
     * Find guarantees by plafond range
     */
    @Query("SELECT g FROM Garantie g WHERE g.plafond BETWEEN :minPlafond AND :maxPlafond")
    List<Garantie> findByPlafondRange(@Param("minPlafond") Double minPlafond, @Param("maxPlafond") Double maxPlafond);

    /**
     * Find guarantees by contrat ID
     */
    List<Garantie> findByContratId(Long contratId);
}