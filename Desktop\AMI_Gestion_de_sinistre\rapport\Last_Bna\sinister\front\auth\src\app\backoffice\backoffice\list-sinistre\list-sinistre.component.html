<div class="container">
  <div class="header">
    <h2>Gestion des Sinistres</h2>
    <div class="search-container">
      <div class="search-wrapper">
        <svg class="search-icon" width="20" height="20" viewBox="0 0 24 24" fill="none">
          <path d="M21 21L16.514 16.506L21 21ZM19 10.5C19 15.194 15.194 19 10.5 19C5.806 19 2 15.194 2 10.5C2 5.806 5.806 2 10.5 2C15.194 2 19 5.806 19 10.5Z" stroke="#6B7280" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        <input
          type="text"
          placeholder="Rechercher par numéro de sinistre, événement ou statut..."
          [(ngModel)]="searchTerm"
          (input)="onSearch()"
          class="search-input">
        <button *ngIf="searchTerm" class="clear-search" (click)="clearSearch()">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
            <path d="M18 6L6 18M6 6L18 18" stroke="#6B7280" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </button>
      </div>
    </div>
  </div>

  <div *ngIf="errorMessage" class="error-message">
    {{ errorMessage }}
  </div>

  <div *ngIf="isLoading" class="loading-spinner">
    <div class="spinner"></div>
  </div>

  <div class="table-responsive" *ngIf="!isLoading">
    <table>
      <thead>
        <tr>
          <th>Numéro de sinistre</th>
          <th>Nom événement</th>
          <th>Date création</th>
          <th>Statut</th>
          <th>Actions</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let sinistre of filteredSinistres">
          <td class="numero-sinistre">{{ sinistre.numeroSinistre }}</td>
          <td>{{ sinistre.nomEvenement }}</td>
          <td>{{ sinistre.dateCreation | date:'dd/MM/yyyy' }}</td>
          <td>
            <span class="status-badge" [ngClass]="{
              'status-en-cours': sinistre.statut === 'En cours',
              'status-traite': sinistre.statut === 'Traité',
              'status-attente': sinistre.statut === 'En attente'
            }">
              {{ sinistre.statut }}
            </span>
          </td>
          <td class="actions">
            <button class="icon-btn view" (click)="viewSinistre(sinistre)" title="Voir les détails">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                <path d="M1 12S5 4 12 4S23 12 23 12S19 20 12 20S1 12 1 12Z" stroke="#1C3F93" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <circle cx="12" cy="12" r="3" stroke="#1C3F93" stroke-width="2"/>
              </svg>
            </button>
            <button class="icon-btn edit" (click)="editSinistre(sinistre)" title="Modifier">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                <path d="M11 4H4C3.46957 4 2.96086 4.21071 2.58579 4.58579C2.21071 4.96086 2 5.46957 2 6V20C2 20.5304 2.21071 21.0391 2.58579 21.4142C2.96086 21.7893 3.46957 22 4 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V13" stroke="#1C3F93" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M18.5 2.50001C18.8978 2.10219 19.4374 1.87869 20 1.87869C20.5626 1.87869 21.1022 2.10219 21.5 2.50001C21.8978 2.89784 22.1213 3.4374 22.1213 4.00001C22.1213 4.56262 21.8978 5.10219 21.5 5.50001L12 15L8 16L9 12L18.5 2.50001Z" stroke="#1C3F93" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </button>
            <button class="icon-btn delete" (click)="deleteSinistre(sinistre)" title="Supprimer">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                <path d="M3 6H5H21" stroke="#FF4B55" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M8 6V4C8 3.46957 8.21071 2.96086 8.58579 2.58579C8.96086 2.21071 9.46957 2 10 2H14C14.5304 2 15.0391 2.21071 15.4142 2.58579C15.7893 2.96086 16 3.46957 16 4V6M19 6V20C19 20.5304 18.7893 21.0391 18.4142 21.4142C18.0391 21.7893 17.5304 22 17 22H7C6.46957 22 5.96086 21.7893 5.58579 21.4142C5.21071 21.0391 5 20.5304 5 20V6H19Z" stroke="#FF4B55" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </button>
          </td>
        </tr>
      </tbody>
    </table>

    <div *ngIf="filteredSinistres.length === 0 && !isLoading" class="no-data">
      <svg width="64" height="64" viewBox="0 0 24 24" fill="none">
        <path d="M9 11H15M9 15H15M17 21H7C5.89543 21 5 20.1046 5 19V5C5 3.89543 5.89543 3 7 3H12.5858C12.851 3 13.1054 3.10536 13.2929 3.29289L19.7071 9.70711C19.8946 9.89464 20 10.149 20 10.4142V19C20 20.1046 19.1046 21 18 21H17ZM17 21V10L13 6H7V19H17Z" stroke="#9CA3AF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
      <p>{{ searchTerm ? 'Aucun sinistre trouvé pour cette recherche' : 'Aucun sinistre disponible' }}</p>
    </div>
  </div>
</div>