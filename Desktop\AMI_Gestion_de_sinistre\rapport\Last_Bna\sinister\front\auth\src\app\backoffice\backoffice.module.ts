import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatInputModule } from '@angular/material/input';
import { BackofficeComponent } from './backoffice/backoffice.component';

@NgModule({
  imports: [
    BackofficeComponent
  ],
  exports: [
    BackofficeComponent
  ]
})
export class BackofficeModule { }
