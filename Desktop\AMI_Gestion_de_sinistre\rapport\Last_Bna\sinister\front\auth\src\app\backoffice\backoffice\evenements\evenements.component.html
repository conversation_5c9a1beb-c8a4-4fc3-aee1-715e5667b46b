<div class="container">
  <div class="header">
    <h2>Gestion des Evenements</h2>
    <button class="create-btn" (click)="openModal()" [disabled]="isLoading">
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
        <path d="M12 5V19M5 12H19" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
      Créer un événement
    </button>
  </div>

  <div *ngIf="errorMessage" class="error-message">
    {{ errorMessage }}
  </div>

  <div *ngIf="isLoading" class="loading-spinner">
    <div class="spinner"></div>
  </div>

  <div class="table-container" *ngIf="!isLoading">
    <div class="table-responsive">
      <table>
        <thead>
          <tr>
            <th>Code</th>
            <th>Libellé</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let event of events" (click)="viewEventDetails(event)" class="clickable-row">
            <td>{{event.code}}</td>
            <td>{{event.libelle}}</td>
            <td class="actions" (click)="$event.stopPropagation()">
              <button class="icon-btn delete" (click)="deleteEvent(event)" [disabled]="isLoading">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                  <path d="M3 6H5H21" stroke="#FF4B55" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  <path d="M8 6V4C8 3.46957 8.21071 2.96086 8.58579 2.58579C8.96086 2.21071 9.46957 2 10 2H14C14.5304 2 15.0391 2.21071 15.4142 2.58579C15.7893 2.96086 16 3.46957 16 4V6M19 6V20C19 20.5304 18.7893 21.0391 18.4142 21.4142C18.0391 21.7893 17.5304 22 17 22H7C6.46957 22 5.96086 21.7893 5.58579 21.4142C5.21071 21.0391 5 20.5304 5 20V6H19Z" stroke="#FF4B55" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </button>
            </td>
          </tr>
          <!-- Ajoutons des lignes de test pour vérifier le défilement -->
        
        </tbody>
      </table>
    </div>
  </div>
</div>

<!-- Modal for creating/editing event -->
<div class="modal" *ngIf="showModal">
  <div class="modal-content">
    <div class="modal-header">
      <h3>{{ editingEvent ? 'Modifier l\'événement' : 'Créer un événement' }}</h3>
      <button class="close-btn" (click)="closeModal()" [disabled]="isLoading">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
          <path d="M18 6L6 18M6 6L18 18" stroke="#6B7280" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </button>
    </div>
    <form [formGroup]="eventForm" (ngSubmit)="onSubmit()">
      <div *ngIf="errorMessage" class="error-message">
        {{ errorMessage }}
      </div>
      
      <div class="form-group">
        <label for="eventCode">Code <span class="required">*</span></label>
        <div class="input-wrapper">
          <input type="text" id="eventCode" formControlName="code" 
                 placeholder="Entrer le code (max 10 caractères)" 
                 [maxlength]="10" [disabled]="isLoading">
        </div>
        <div class="error-message" 
             *ngIf="eventForm.get('code')?.invalid && eventForm.get('code')?.touched">
          <span *ngIf="eventForm.get('code')?.errors?.['required']">Ce champ est obligatoire</span>
          <span *ngIf="eventForm.get('code')?.errors?.['maxlength']">Maximum 10 caractères</span>
        </div>
      </div>
      <div class="form-group">
        <label for="eventLibelle">Libellé <span class="required">*</span></label>
        <div class="input-wrapper">
          <input type="text" id="eventLibelle" formControlName="libelle"
                 placeholder="Entrer le libellé" [disabled]="isLoading">
        </div>
        <div class="error-message"
             *ngIf="eventForm.get('libelle')?.invalid && eventForm.get('libelle')?.touched">
          Ce champ est obligatoire
        </div>
      </div>

      <div class="form-group">
        <label for="typeDegat">Type de Dégât</label>
        <div class="input-wrapper">
          <select id="typeDegat" formControlName="type_de_degat" [disabled]="isLoading || isLoadingTypeDegats">
            <option value="">
              {{ isLoadingTypeDegats ? 'Chargement des types...' : 'Sélectionner un type' }}
            </option>
            <option *ngFor="let type of typeDegats" [value]="type.code">
              {{ type.code }} - {{ type.libelle }}
            </option>
          </select>
        </div>
      </div>

      <div class="form-group">
        <label for="tier">Tiers</label>
        <div class="toggle-wrapper">
          <label class="toggle">
            <input type="checkbox" formControlName="tier" [disabled]="isLoading">
            <span class="slider"></span>
          </label>
          <span class="toggle-label">{{ eventForm.get('tier')?.value ? 'Oui' : 'Non' }}</span>
        </div>
      </div>

      <div class="form-group">
        <label for="responsabilite">Responsabilité</label>
        <div class="input-wrapper">
          <input type="number" id="responsabilite" formControlName="responsabilite"
                 placeholder="Niveau de responsabilité (0-4)"
                 min="0" max="4" [disabled]="isLoading">
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn-secondary" (click)="closeModal()" [disabled]="isLoading">Annuler</button>
        <button type="submit" class="btn-primary" [disabled]="eventForm.invalid || isLoading">
          <span *ngIf="isLoading" class="spinner"></span>
          {{ editingEvent ? 'Modifier' : 'Créer' }}
        </button>
      </div>
    </form>
  </div>
</div>

<!-- Modal for viewing/editing event details -->
<div class="modal" *ngIf="showDetailsModal">
  <div class="modal-content details-modal">
    <div class="modal-header">
      <h3>Détails de l'événement</h3>
      <button class="close-btn" (click)="closeDetailsModal()" [disabled]="isLoading">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
          <path d="M18 6L6 18M6 6L18 18" stroke="#6B7280" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </button>
    </div>

    <form [formGroup]="detailsForm" (ngSubmit)="onDetailsSubmit()">
      <div *ngIf="errorMessage" class="error-message">
        {{ errorMessage }}
      </div>

      <div class="details-grid">
        <div class="form-group">
          <label for="detailCode">Code</label>
          <div class="input-wrapper">
            <input type="text" id="detailCode" formControlName="code"
                   [disabled]="isLoading">
          </div>
        </div>

        <div class="form-group">
          <label for="detailLibelle">Libellé</label>
          <div class="input-wrapper">
            <input type="text" id="detailLibelle" formControlName="libelle"
                   [disabled]="isLoading">
          </div>
        </div>

        <div class="form-group">
          <label for="detailTypeDegat">Type de Dégât</label>
          <div class="input-wrapper">
            <select id="detailTypeDegat" formControlName="type_de_degat" [disabled]="isLoading"
                    [class.loading]="isLoadingTypeDegats">
              <option value="">
                {{ isLoadingTypeDegats ? 'Chargement des types...' : 'Sélectionner un type' }}
              </option>
              <option *ngFor="let type of typeDegats" [value]="type.code">
                {{ type.code }} - {{ type.libelle }}
              </option>
            </select>
          </div>
        </div>

        <div class="form-group">
          <label for="detailTier">Tiers</label>
          <div class="toggle-wrapper">
            <label class="toggle">
              <input type="checkbox" formControlName="tier" [disabled]="isLoading">
              <span class="slider"></span>
            </label>
            <span class="toggle-label">{{ detailsForm.get('tier')?.value ? 'Oui' : 'Non' }}</span>
          </div>
        </div>

        <div class="form-group">
          <label for="detailResponsabilite">Responsabilité</label>
          <div class="input-wrapper">
            <input type="number" id="detailResponsabilite" formControlName="responsabilite"
                   placeholder="Niveau (0-4)" min="0" max="4" [disabled]="isLoading">
          </div>
        </div>
      </div>

      <div class="modal-footer">
        <button type="button" class="btn-secondary" (click)="closeDetailsModal()" [disabled]="isLoading">
          Fermer
        </button>
        <button type="submit" class="btn-primary" [disabled]="!hasChanges || isLoading">
          <span *ngIf="isLoading" class="spinner"></span>
          Enregistrer
        </button>
      </div>
    </form>
  </div>
</div>