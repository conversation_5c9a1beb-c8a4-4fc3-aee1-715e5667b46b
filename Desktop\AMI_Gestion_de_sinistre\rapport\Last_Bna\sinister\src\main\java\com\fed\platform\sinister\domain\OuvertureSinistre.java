package com.fed.platform.sinister.domain;

import java.io.Serializable;
import java.time.Instant;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Lob;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;

@Entity
@Table(name = "ouverture_sinistre")
public class OuvertureSinistre implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "numero_sinistre", unique = true, nullable = false, length = 20)
    private String numeroSinistre;

    @Column(name = "evenement")
    private String evenement;

    @Column(name = "type_de_degat")
    private String typeDeDegat;

    @Size(max = 256)
    @Column(name = "description_de_degat", length = 256)
    private String descriptionDeDegat;

    @Column(name = "tier")
    private Boolean tier;

    @Column(name = "type_du_tier")
    private String typeDuTier;

    @Column(name = "compagnie_tier")
    private String compagnieTier;

    @Column(name = "cas_de_barem")
    private String casDeBareme;

    @Column(name = "nombre_de_tier")
    private Integer nombreDeTier;

    @Column(name = "degat_estimatif")
    private String degatEstimatif;

    @Column(name = "responsabilite")
    private Integer responsabilite;

    @Lob
    @Column(name = "photos_de_vehicule", columnDefinition = "oid")
    private byte[] photosDeVehicule;

    @Column(name = "link_vehicule", columnDefinition = "TEXT")
    private String linkVehicule;

    @Lob
    @Column(name = "attachement", columnDefinition = "oid")
    private byte[] attachement;

    @Column(name = "document_names")
    private String documentNames;

    @Column(name = "date_creation")
    private Instant dateCreation;

    // Constructors
    public OuvertureSinistre() {
        this.dateCreation = Instant.now();
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getNumeroSinistre() {
        return numeroSinistre;
    }

    public void setNumeroSinistre(String numeroSinistre) {
        this.numeroSinistre = numeroSinistre;
    }

    public String getEvenement() {
        return evenement;
    }

    public void setEvenement(String evenement) {
        this.evenement = evenement;
    }

    public String getTypeDeDegat() {
        return typeDeDegat;
    }

    public void setTypeDeDegat(String typeDeDegat) {
        this.typeDeDegat = typeDeDegat;
    }

    public String getDescriptionDeDegat() {
        return descriptionDeDegat;
    }

    public void setDescriptionDeDegat(String descriptionDeDegat) {
        this.descriptionDeDegat = descriptionDeDegat;
    }

    public Boolean getTier() {
        return tier;
    }

    public void setTier(Boolean tier) {
        this.tier = tier;
    }

    public String getTypeDuTier() {
        return typeDuTier;
    }

    public void setTypeDuTier(String typeDuTier) {
        this.typeDuTier = typeDuTier;
    }

    public String getCompagnieTier() {
        return compagnieTier;
    }

    public void setCompagnieTier(String compagnieTier) {
        this.compagnieTier = compagnieTier;
    }

    public String getCasDeBareme() {
        return casDeBareme;
    }

    public void setCasDeBareme(String casDeBareme) {
        this.casDeBareme = casDeBareme;
    }

    public Integer getNombreDeTier() {
        return nombreDeTier;
    }

    public void setNombreDeTier(Integer nombreDeTier) {
        this.nombreDeTier = nombreDeTier;
    }

    public String getDegatEstimatif() {
        return degatEstimatif;
    }

    public void setDegatEstimatif(String degatEstimatif) {
        this.degatEstimatif = degatEstimatif;
    }

    public Integer getResponsabilite() {
        return responsabilite;
    }

    public void setResponsabilite(Integer responsabilite) {
        this.responsabilite = responsabilite;
    }

    public byte[] getPhotosDeVehicule() {
        return photosDeVehicule;
    }

    public void setPhotosDeVehicule(byte[] photosDeVehicule) {
        this.photosDeVehicule = photosDeVehicule;
    }

    public String getLinkVehicule() {
        return linkVehicule;
    }

    public void setLinkVehicule(String linkVehicule) {
        this.linkVehicule = linkVehicule;
    }

    public byte[] getAttachement() {
        return attachement;
    }

    public void setAttachement(byte[] attachement) {
        this.attachement = attachement;
    }

    public String getDocumentNames() {
        return documentNames;
    }

    public void setDocumentNames(String documentNames) {
        this.documentNames = documentNames;
    }

    public Instant getDateCreation() {
        return dateCreation;
    }

    public void setDateCreation(Instant dateCreation) {
        this.dateCreation = dateCreation;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof OuvertureSinistre)) {
            return false;
        }
        return id != null && id.equals(((OuvertureSinistre) o).id);
    }

    @Override
    public int hashCode() {
        return getClass().hashCode();
    }

    @Override
    public String toString() {
        return "OuvertureSinistre{" +
                "id=" + getId() +
                ", numeroSinistre='" + getNumeroSinistre() + "'" +
                ", evenement='" + getEvenement() + "'" +
                ", typeDeDegat='" + getTypeDeDegat() + "'" +
                ", tier=" + getTier() +
                ", responsabilite=" + getResponsabilite() +
                ", dateCreation='" + getDateCreation() + "'" +
                "}";
    }
}
