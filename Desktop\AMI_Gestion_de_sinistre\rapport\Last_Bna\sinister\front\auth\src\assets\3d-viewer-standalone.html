<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚗 Visualisation 3D - Véhicule Sinistré</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 20px 30px;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
            z-index: 1000;
        }

        .header h1 {
            color: #2c3e50;
            font-size: 24px;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .actions {
            display: flex;
            gap: 15px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: #013888;
            color: white;
        }

        .btn-primary:hover {
            background: #012a66;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(1, 56, 136, 0.3);
        }

        .btn-secondary {
            background: transparent;
            color: #6c757d;
            border: 2px solid #6c757d;
        }

        .btn-secondary:hover {
            background: #6c757d;
            color: white;
        }

        .viewer-container {
            flex: 1;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .car-scene {
            position: relative;
            width: 400px;
            height: 250px;
            perspective: 1000px;
        }

        .car-container {
            position: relative;
            width: 100%;
            height: 100%;
            transform-style: preserve-3d;
            animation: carFloat 4s ease-in-out infinite;
        }

        .car-body {
            position: absolute;
            width: 280px;
            height: 80px;
            background: linear-gradient(45deg, #0066cc, #004499, #0066cc);
            border-radius: 15px;
            top: 60px;
            left: 60px;
            box-shadow: 
                0 15px 30px rgba(0, 0, 0, 0.3),
                inset 0 2px 10px rgba(255, 255, 255, 0.2);
            transform: rotateX(-10deg);
        }

        .car-roof {
            position: absolute;
            width: 160px;
            height: 50px;
            background: linear-gradient(45deg, #004499, #002266, #004499);
            border-radius: 12px;
            top: 20px;
            left: 120px;
            box-shadow: 
                0 10px 20px rgba(0, 0, 0, 0.2),
                inset 0 2px 8px rgba(255, 255, 255, 0.1);
            transform: rotateX(-10deg);
        }

        .wheel {
            position: absolute;
            width: 50px;
            height: 50px;
            background: radial-gradient(circle at 30% 30%, #555, #222, #111);
            border-radius: 50%;
            border: 4px solid #333;
            box-shadow: 
                0 8px 16px rgba(0, 0, 0, 0.4),
                inset 0 2px 8px rgba(255, 255, 255, 0.1);
            animation: wheelRotate 2s linear infinite;
        }

        .wheel::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 20px;
            height: 20px;
            background: #666;
            border-radius: 50%;
            transform: translate(-50%, -50%);
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .wheel-fl { top: 90px; left: 40px; }
        .wheel-fr { top: 90px; right: 40px; }
        .wheel-bl { top: 90px; left: 100px; }
        .wheel-br { top: 90px; right: 100px; }

        .windshield {
            position: absolute;
            width: 140px;
            height: 35px;
            background: linear-gradient(135deg, rgba(173, 216, 230, 0.8), rgba(135, 206, 235, 0.6));
            border-radius: 8px;
            top: 30px;
            left: 130px;
            transform: rotateX(-10deg);
            box-shadow: inset 0 1px 3px rgba(255, 255, 255, 0.3);
        }

        .headlight {
            position: absolute;
            width: 25px;
            height: 15px;
            background: radial-gradient(circle, #fff, #f0f0f0);
            border-radius: 50%;
            top: 75px;
            box-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
        }

        .headlight-left { left: 45px; }
        .headlight-right { right: 45px; }

        .info-panel {
            position: absolute;
            bottom: 30px;
            right: 30px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            min-width: 280px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .info-panel h3 {
            margin: 0 0 15px 0;
            color: #2c3e50;
            font-size: 18px;
            border-bottom: 2px solid #013888;
            padding-bottom: 8px;
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            font-size: 14px;
            padding: 5px 0;
        }

        .info-row span:first-child {
            color: #6c757d;
            font-weight: 500;
        }

        .info-row span:last-child {
            color: #2c3e50;
            font-weight: 700;
        }

        .status-success { color: #28a745 !important; }
        .status-demo { color: #ff6b35 !important; }
        .status-api { color: #007bff !important; }

        .success-banner {
            position: absolute;
            top: 30px;
            left: 30px;
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 20px 25px;
            border-radius: 12px;
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
            max-width: 350px;
        }

        .success-banner h3 {
            margin: 0 0 8px 0;
            font-size: 18px;
        }

        .success-banner p {
            margin: 0;
            font-size: 14px;
            opacity: 0.9;
        }

        .loading-indicator {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: white;
        }

        .spinner {
            width: 60px;
            height: 60px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes carFloat {
            0%, 100% { 
                transform: translateY(0px) rotateY(-5deg) rotateX(5deg); 
            }
            50% { 
                transform: translateY(-15px) rotateY(5deg) rotateX(-5deg); 
            }
        }

        @keyframes wheelRotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .fade-in {
            animation: fadeIn 0.8s ease-out;
        }

        @media (max-width: 768px) {
            .header {
                padding: 15px 20px;
                flex-direction: column;
                gap: 15px;
            }

            .header h1 {
                font-size: 20px;
            }

            .car-scene {
                width: 300px;
                height: 200px;
            }

            .info-panel {
                bottom: 20px;
                right: 20px;
                left: 20px;
                min-width: auto;
            }

            .success-banner {
                top: 20px;
                left: 20px;
                right: 20px;
                max-width: none;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header fade-in">
        <h1>
            🚗 Visualisation 3D - Véhicule Sinistré
        </h1>
        <div class="actions">
            <button class="btn btn-primary" onclick="downloadModel()">
                ⬇️ Télécharger Modèle
            </button>
            <button class="btn btn-secondary" onclick="goBack()">
                ⬅️ Retour
            </button>
            <button class="btn btn-secondary" onclick="window.close()">
                ❌ Fermer
            </button>
        </div>
    </div>

    <!-- Loading Indicator (initially hidden) -->
    <div class="loading-indicator" id="loadingIndicator" style="display: none;">
        <div class="spinner"></div>
        <h3>Génération du modèle 3D...</h3>
        <p>Traitement de votre photo en cours</p>
    </div>

    <!-- Main Viewer -->
    <div class="viewer-container">
        <!-- Success Banner -->
        <div class="success-banner fade-in">
            <h3>🎉 Modèle 3D Généré avec Succès !</h3>
            <p>Votre véhicule a été converti en modèle 3D interactif</p>
        </div>

        <!-- 3D Car Scene -->
        <div class="car-scene fade-in">
            <div class="car-container">
                <!-- Car Body -->
                <div class="car-body"></div>
                
                <!-- Car Roof -->
                <div class="car-roof"></div>
                
                <!-- Windshield -->
                <div class="windshield"></div>
                
                <!-- Headlights -->
                <div class="headlight headlight-left"></div>
                <div class="headlight headlight-right"></div>
                
                <!-- Wheels -->
                <div class="wheel wheel-fl"></div>
                <div class="wheel wheel-fr"></div>
                <div class="wheel wheel-bl"></div>
                <div class="wheel wheel-br"></div>
            </div>
        </div>

        <!-- Info Panel -->
        <div class="info-panel fade-in">
            <h3>📊 Informations du Modèle</h3>
            <div class="info-row">
                <span>Task ID:</span>
                <span id="taskId">demo-task</span>
            </div>
            <div class="info-row">
                <span>Service:</span>
                <span class="status-api">🎯 CSM.ai API</span>
            </div>
            <div class="info-row">
                <span>Format:</span>
                <span>GLB</span>
            </div>
            <div class="info-row">
                <span>Status:</span>
                <span class="status-success">✅ Généré</span>
            </div>
            <div class="info-row">
                <span>Mode:</span>
                <span class="status-demo">🎭 Démonstration</span>
            </div>
            <div class="info-row">
                <span>Qualité:</span>
                <span class="status-success">🌟 Haute Définition</span>
            </div>
            <div class="info-row">
                <span>Taille:</span>
                <span>2.4 MB</span>
            </div>
        </div>
    </div>

    <script>
        // Get task ID from URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const taskId = urlParams.get('taskId') || 'demo-task-' + Date.now();
        
        // Update task ID in the info panel
        document.getElementById('taskId').textContent = taskId;

        console.log('=== 3D VIEWER LOADED ===');
        console.log('Task ID:', taskId);
        console.log('URL:', window.location.href);
        console.log('Service: CSM.ai API');

        // Simulate loading process
        function simulateLoading() {
            const loadingIndicator = document.getElementById('loadingIndicator');
            const viewerContainer = document.querySelector('.viewer-container');
            
            // Show loading
            loadingIndicator.style.display = 'block';
            viewerContainer.style.display = 'none';
            
            // Simulate processing time
            setTimeout(() => {
                loadingIndicator.style.display = 'none';
                viewerContainer.style.display = 'flex';
                console.log('✅ 3D model loaded successfully');
            }, 2000);
        }

        // Download model function
        function downloadModel() {
            console.log('Downloading 3D model...');
            
            // Create a fake download
            const link = document.createElement('a');
            link.href = 'data:text/plain;charset=utf-8,# CSM.ai Model\n# Session ID: ' + taskId + '\n# Generated: ' + new Date().toISOString() + '\n# Format: GLB\n# Service: CSM.ai API\n\n# This is a demo file. In production, this would be the actual GLB model.';
            link.download = 'vehicle-3d-model-' + taskId + '.txt';
            link.click();

            alert('🎉 Téléchargement démarré !\n\nSession ID: ' + taskId + '\nFormat: GLB\nService: CSM.ai API');
        }

        // Go back function
        function goBack() {
            if (window.history.length > 1) {
                window.history.back();
            } else {
                window.close();
            }
        }

        // Add some interactive effects
        document.addEventListener('DOMContentLoaded', function() {
            // Add click effect to car
            const carContainer = document.querySelector('.car-container');
            carContainer.addEventListener('click', function() {
                this.style.animation = 'none';
                setTimeout(() => {
                    this.style.animation = 'carFloat 4s ease-in-out infinite';
                }, 100);
            });

            // Add hover effects
            const buttons = document.querySelectorAll('.btn');
            buttons.forEach(btn => {
                btn.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-3px) scale(1.05)';
                });
                
                btn.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });

            console.log('✅ 3D Viewer initialized successfully');
            console.log('🎯 Using Tripo3D API service');
            console.log('📊 Task ID:', taskId);
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            switch(e.key) {
                case 'Escape':
                    window.close();
                    break;
                case 'd':
                case 'D':
                    if (e.ctrlKey) {
                        e.preventDefault();
                        downloadModel();
                    }
                    break;
                case 'r':
                case 'R':
                    if (e.ctrlKey) {
                        e.preventDefault();
                        location.reload();
                    }
                    break;
            }
        });
    </script>
</body>
</html>
