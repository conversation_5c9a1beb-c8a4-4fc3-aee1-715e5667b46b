package com.fed.platform.sinister.service;

import com.fed.platform.sinister.domain.DocumentSinistre;
import com.fed.platform.sinister.repository.DocumentSinistreRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.List;
import java.util.Optional;

@Service
@Transactional
public class DocumentSinistreService {

    private static final Logger logger = LoggerFactory.getLogger(DocumentSinistreService.class);

    @Autowired
    private DocumentSinistreRepository documentSinistreRepository;

    /**
     * Save a new document sinistre
     */
    public DocumentSinistre save(DocumentSinistre documentSinistre) {
        logger.info("🔍 Saving new document sinistre for evenement: {}", documentSinistre.getLibelleEvenement());
        
        // Set upload time if not set
        if (documentSinistre.getDateUpload() == null) {
            documentSinistre.setDateUpload(Instant.now());
        }
        
        DocumentSinistre saved = documentSinistreRepository.save(documentSinistre);
        logger.info("✅ Document sinistre saved with ID: {}", saved.getId());
        
        return saved;
    }

    /**
     * Update an existing document sinistre
     */
    public DocumentSinistre update(Long id, DocumentSinistre documentSinistre) {
        logger.info("🔍 Updating document sinistre with ID: {}", id);
        
        Optional<DocumentSinistre> existingOpt = documentSinistreRepository.findById(id);
        if (existingOpt.isPresent()) {
            DocumentSinistre existing = existingOpt.get();
            
            // Update fields
            existing.setLibelleEvenement(documentSinistre.getLibelleEvenement());
            existing.setDocument(documentSinistre.getDocument());
            existing.setPathFichier(documentSinistre.getPathFichier());
            existing.setDateUpload(Instant.now()); // Update timestamp
            
            DocumentSinistre updated = documentSinistreRepository.save(existing);
            logger.info("✅ Document sinistre updated: {}", updated.getId());
            
            return updated;
        } else {
            logger.error("❌ Document sinistre not found with ID: {}", id);
            throw new RuntimeException("Document sinistre not found with ID: " + id);
        }
    }

    /**
     * Find all documents sinistre
     */
    @Transactional(readOnly = true)
    public List<DocumentSinistre> findAll() {
        logger.info("🔍 Finding all documents sinistre");
        List<DocumentSinistre> result = documentSinistreRepository.findAll();
        logger.info("📄 Found {} documents sinistre", result.size());
        return result;
    }

    /**
     * Find by ID
     */
    @Transactional(readOnly = true)
    public Optional<DocumentSinistre> findById(Long id) {
        logger.info("🔍 Finding document sinistre by ID: {}", id);
        Optional<DocumentSinistre> result = documentSinistreRepository.findById(id);
        if (result.isPresent()) {
            logger.info("✅ Found document sinistre: {}", result.get().getLibelleEvenement());
        } else {
            logger.info("❌ Document sinistre not found with ID: {}", id);
        }
        return result;
    }

    /**
     * Find by libelle evenement
     */
    @Transactional(readOnly = true)
    public List<DocumentSinistre> findByLibelleEvenement(String libelleEvenement) {
        logger.info("🔍 Finding documents sinistre by libelle evenement: {}", libelleEvenement);
        List<DocumentSinistre> result = documentSinistreRepository.findByLibelleEvenement(libelleEvenement);
        logger.info("📄 Found {} documents for evenement: {}", result.size(), libelleEvenement);
        return result;
    }

    /**
     * Delete by ID
     */
    public void deleteById(Long id) {
        logger.info("🔍 Deleting document sinistre with ID: {}", id);
        if (documentSinistreRepository.existsById(id)) {
            documentSinistreRepository.deleteById(id);
            logger.info("✅ Document sinistre deleted with ID: {}", id);
        } else {
            logger.error("❌ Cannot delete - document sinistre not found with ID: {}", id);
            throw new RuntimeException("Document sinistre not found with ID: " + id);
        }
    }

    /**
     * Check if document exists by libelle evenement and document name
     */
    @Transactional(readOnly = true)
    public boolean existsByLibelleEvenementAndDocument(String libelleEvenement, String document) {
        return documentSinistreRepository.existsByLibelleEvenementAndDocument(libelleEvenement, document);
    }

    /**
     * Find documents by document name
     */
    @Transactional(readOnly = true)
    public List<DocumentSinistre> findByDocument(String document) {
        logger.info("🔍 Finding documents sinistre by document name: {}", document);
        List<DocumentSinistre> result = documentSinistreRepository.findByDocument(document);
        logger.info("📄 Found {} documents with name: {}", result.size(), document);
        return result;
    }

    /**
     * Get all unique libelle evenements
     */
    @Transactional(readOnly = true)
    public List<String> findAllUniqueLibelleEvenements() {
        logger.info("🔍 Finding all unique libelle evenements");
        List<String> result = documentSinistreRepository.findDistinctLibelleEvenement();
        logger.info("📄 Found {} unique evenements", result.size());
        return result;
    }
}
