package com.fed.platform.sinister.domain;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

@Entity
@Table(name = "nature_tiers")
public class NatureTiersEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false, unique = true)
    private NatureTiers nature;

    public NatureTiersEntity() {
    }

    public NatureTiersEntity(NatureTiers nature) {
        this.nature = nature;
    }

    public Long getId() {
        return id;
    }

    public NatureTiers getNature() {
        return nature;
    }

    public void setNature(NatureTiers nature) {
        this.nature = nature;
    }
}
