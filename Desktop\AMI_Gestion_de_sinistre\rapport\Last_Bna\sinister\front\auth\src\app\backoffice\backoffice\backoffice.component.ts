import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { RouterModule } from '@angular/router';

interface Event {
  id: number;
  name: string;
}

interface DamageType {
  id: number;
  name: string;
  requiresThirdParty: boolean;
}

interface ScaleCase {
  id: number;
  name: string;
}

interface Sinistre {
  id: number;
  numeroSinistre: string;
  nomEvenement: string;
  dateCreation: string;
  statut: string;
}

@Component({
  selector: 'app-backoffice',
  templateUrl: './backoffice.component.html',
  styleUrls: ['./backoffice.component.css'],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatSelectModule,
    MatCheckboxModule,
    MatInputModule,
    RouterModule,
  ],
  standalone: true
})
export class BackofficeComponent implements OnInit {
  configurationForm: FormGroup;
  events: Event[] = [];
  damageTypes: DamageType[] = [];
  scaleCases: ScaleCase[] = [];
  selectedEvent: Event | null = null;
  selectedDamageType: DamageType | null = null;

  // Propriétés pour la liste des sinistres
  sinistres: Sinistre[] = [];
  filteredSinistres: Sinistre[] = [];
  searchTerm: string = '';
  isLoading: boolean = false;
  errorMessage: string = '';
  showSinistresList: boolean = true;

  constructor(private fb: FormBuilder) {
    this.configurationForm = this.fb.group({
      event: ['', Validators.required],
      damageType: ['', Validators.required],
      scaleCase: [''],
      description: ['', Validators.required]
    });
  }

  ngOnInit(): void {
    // TODO: Fetch events from backend service
    this.events = [
      { id: 1, name: 'Événement 1' },
      { id: 2, name: 'Événement 2' }
    ];

    // Charger les sinistres au démarrage
    this.loadSinistres();
  }

  onEventChange(event: Event): void {
    this.selectedEvent = event;
    // TODO: Fetch damage types from backend service based on selected event
    this.damageTypes = [
      { id: 1, name: 'Dégât 1', requiresThirdParty: true },
      { id: 2, name: 'Dégât 2', requiresThirdParty: false }
    ];
    this.configurationForm.get('damageType')?.reset();
    this.configurationForm.get('scaleCase')?.reset();
  }

  onDamageTypeChange(damageType: DamageType): void {
    this.selectedDamageType = damageType;
    if (damageType.requiresThirdParty) {
      // TODO: Fetch scale cases from backend service
      this.scaleCases = [
        { id: 1, name: 'Barème 1' },
        { id: 2, name: 'Barème 2' }
      ];
      this.configurationForm.get('scaleCase')?.setValidators(Validators.required);
    } else {
      this.scaleCases = [];
      this.configurationForm.get('scaleCase')?.clearValidators();
    }
    this.configurationForm.get('scaleCase')?.updateValueAndValidity();
  }

  onSubmit(): void {
    if (this.configurationForm.valid) {
      const formData = this.configurationForm.value;
      console.log('Form submitted:', formData);
      // TODO: Send data to backend service
    }
  }

  // Méthodes pour la gestion des sinistres
  loadSinistres(): void {
    this.isLoading = true;
    this.errorMessage = '';

    // Simulation de données - à remplacer par un service backend
    setTimeout(() => {
      this.sinistres = [
        {
          id: 1,
          numeroSinistre: 'SIN-2024-001',
          nomEvenement: 'Accident de circulation',
          dateCreation: '2024-01-15',
          statut: 'En cours'
        },
        {
          id: 2,
          numeroSinistre: 'SIN-2024-002',
          nomEvenement: 'Incendie véhicule',
          dateCreation: '2024-01-16',
          statut: 'Traité'
        },
        {
          id: 3,
          numeroSinistre: 'SIN-2024-003',
          nomEvenement: 'Vol de véhicule',
          dateCreation: '2024-01-17',
          statut: 'En attente'
        },
        {
          id: 4,
          numeroSinistre: 'SIN-2024-004',
          nomEvenement: 'Dégâts matériels',
          dateCreation: '2024-01-18',
          statut: 'En cours'
        },
        {
          id: 5,
          numeroSinistre: 'SIN-2024-005',
          nomEvenement: 'Accident corporel',
          dateCreation: '2024-01-19',
          statut: 'Traité'
        }
      ];

      this.filteredSinistres = [...this.sinistres];
      this.isLoading = false;
    }, 1000);
  }

  onSearch(): void {
    if (!this.searchTerm.trim()) {
      this.filteredSinistres = [...this.sinistres];
      return;
    }

    const searchTermLower = this.searchTerm.toLowerCase().trim();
    this.filteredSinistres = this.sinistres.filter(sinistre =>
      sinistre.numeroSinistre.toLowerCase().includes(searchTermLower) ||
      sinistre.nomEvenement.toLowerCase().includes(searchTermLower) ||
      sinistre.statut.toLowerCase().includes(searchTermLower)
    );
  }

  clearSearch(): void {
    this.searchTerm = '';
    this.filteredSinistres = [...this.sinistres];
  }

  viewSinistre(sinistre: Sinistre): void {
    console.log('Viewing sinistre:', sinistre);
    // TODO: Navigate to sinistre details
  }

  editSinistre(sinistre: Sinistre): void {
    console.log('Editing sinistre:', sinistre);
    // TODO: Navigate to edit sinistre
  }

  deleteSinistre(sinistre: Sinistre): void {
    if (confirm(`Êtes-vous sûr de vouloir supprimer le sinistre ${sinistre.numeroSinistre} ?`)) {
      console.log('Deleting sinistre:', sinistre);
      // TODO: Call backend service to delete
      this.sinistres = this.sinistres.filter(s => s.id !== sinistre.id);
      this.onSearch(); // Refresh filtered list
    }
  }

  // Méthodes pour gérer l'affichage conditionnel
  showSinistresTable(): void {
    this.showSinistresList = true;
  }

  hideSinistresTable(): void {
    this.showSinistresList = false;
  }
}
