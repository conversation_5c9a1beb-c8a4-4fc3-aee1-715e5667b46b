import { CommonModule } from '@angular/common';
import { HttpClient, HttpClientModule, HttpErrorResponse } from '@angular/common/http';
import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { catchError, finalize, throwError } from 'rxjs';
import { environment } from '../../../../environments/environment';

interface CasBareme {
  id: number;
  libelle: string;
  resX: number;
  resY: number;
}

@Component({
  selector: 'app-cas-de-bareme',
  templateUrl: './cas-de-bareme.component.html',
  styleUrls: ['./cas-de-bareme.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    HttpClientModule
  ]
})
export class CasDeBaremeComponent implements OnInit {
  casBaremes: CasBareme[] = [];
  casBaremeForm: FormGroup;
  showModal = false;
  editingCasBareme: CasBareme | null = null;
  isLoading = false;
  errorMessage: string | null = null;
  successMessage: string | null = null;

  constructor(
    private fb: FormBuilder,
    private http: HttpClient,
    private router: Router
  ) {
    this.casBaremeForm = this.fb.group({
      libelle: ['', [Validators.required, Validators.maxLength(500)]],
      resX: ['', [Validators.required, Validators.min(0), Validators.max(4)]],
      resY: ['', [Validators.required, Validators.min(0), Validators.max(4)]]
    });
  }

  ngOnInit(): void {
    this.loadCasBaremes();
  }

  loadCasBaremes(): void {
    this.isLoading = true;
    this.errorMessage = null;
    
    this.http.get<CasBareme[]>(`${environment.apiUrl}/cas-de-bareme`)
      .pipe(
        catchError((error: HttpErrorResponse) => {
          this.isLoading = false;
          if (error.status === 404) {
            this.errorMessage = 'Endpoint not found. Check backend configuration.';
          } else if (error.status === 401) {
            this.router.navigate(['/login']);
          } else {
            this.errorMessage = 'Failed to load cases. Please try again.';
          }
          return throwError(() => error);
        }),
        finalize(() => this.isLoading = false)
      )
      .subscribe({
        next: (data) => this.casBaremes = data,
        error: (err) => console.error('Error:', err)
      });
  }


  openModal(cas?: CasBareme): void {
    this.showModal = true;
    this.editingCasBareme = cas || null;
    this.errorMessage = null;
    this.successMessage = null;
    
    if (cas) {
      this.casBaremeForm.patchValue({
        libelle: cas.libelle,
        resX: cas.resX,
        resY: cas.resY
      });
    } else {
      this.casBaremeForm.reset();
    }
  }

  closeModal(): void {
    this.showModal = false;
    this.editingCasBareme = null;
    this.casBaremeForm.reset();
    this.errorMessage = null;
    this.successMessage = null;
  }

  onSubmit(): void {
    if (this.casBaremeForm.invalid) {
      this.casBaremeForm.markAllAsTouched();
      return;
    }

    const formData = this.casBaremeForm.value;
    this.isLoading = true;
    this.errorMessage = null;
    this.successMessage = null;

    const request = this.editingCasBareme
      ? this.http.put<CasBareme>(
          `${environment.apiUrl}/cas-de-bareme/${this.editingCasBareme.id}`, 
          formData
        )
      : this.http.post<CasBareme>(
          `${environment.apiUrl}/cas-de-bareme`, 
          formData
        );

    request
      .pipe(
        catchError((error: HttpErrorResponse) => {
          return throwError(() => error.error?.message || error.message);
        }),
        finalize(() => this.isLoading = false)
      )
      .subscribe({
        next: () => {
          this.successMessage = this.editingCasBareme 
            ? 'Cas de barème mis à jour avec succès' 
            : 'Cas de barème créé avec succès';
          this.loadCasBaremes();
          this.closeModal();
        },
        error: (err) => {
          console.error('Error saving case:', err);
          this.errorMessage = this.editingCasBareme
            ? 'Erreur lors de la mise à jour du cas de barème'
            : 'Erreur lors de la création du cas de barème';
        }
      });
  }

  deleteCasBareme(id: number): void {
    if (!confirm('Êtes-vous sûr de vouloir supprimer ce cas de barème ?')) {
      return;
    }

    this.isLoading = true;
    this.errorMessage = null;
    this.http.delete(`${environment.apiUrl}/cas-de-bareme/${id}`)
      .pipe(
        catchError((error: HttpErrorResponse) => {
          return throwError(() => error.error?.message || error.message);
        }),
        finalize(() => this.isLoading = false)
      )
      .subscribe({
        next: () => {
          this.successMessage = 'Cas de barème supprimé avec succès';
          this.loadCasBaremes();
        },
        error: (err) => {
          console.error('Error deleting case:', err);
          this.errorMessage = 'Erreur lors de la suppression du cas de barème';
        }
      });
  }
}