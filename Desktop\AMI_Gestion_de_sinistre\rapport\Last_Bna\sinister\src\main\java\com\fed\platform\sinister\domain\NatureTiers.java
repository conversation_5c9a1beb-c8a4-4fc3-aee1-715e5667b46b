package com.fed.platform.sinister.domain;

import com.fasterxml.jackson.annotation.JsonCreator;

public enum NatureTiers {
    AD, AT, AY, BY, CF, CH, MO, PI, TR, VH;

    @JsonCreator
    public static NatureTiers fromString(String value) {
        for (NatureTiers type : NatureTiers.values()) {
            if (type.name().equalsIgnoreCase(value)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Invalid NatureTiers value: " + value);
    }
}
