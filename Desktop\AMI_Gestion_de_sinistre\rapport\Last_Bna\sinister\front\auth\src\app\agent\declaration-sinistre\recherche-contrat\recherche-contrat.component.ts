import { CommonModule } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { Component, OnInit } from '@angular/core';
import { AbstractControl, AsyncValidatorFn, FormBuilder, FormGroup, ReactiveFormsModule, ValidationErrors, Validators } from '@angular/forms';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { Router } from '@angular/router';
import { Observable, map, of } from 'rxjs';
import { tap } from 'rxjs/operators';
import { OuvertureSinistreData, OuvertureSinistreService } from '../../../services/ouverture-sinistre.service';

export interface Contrat {
  id: number;
  numContrat: string;
  immatriculation: string;
  dateEffet: string;
  dateFin: string;
  produit: string;
  clientName: string;
  clientCode: string;
}

export interface Garantie {
  id: number;
  contratId: number;
  nom: string;
  description: string;
  montant: number;
}

export interface DelaiCheckResponse {
  isDelaiExceeded: boolean;
  delaiJours: number;
  message: string;
}

export interface GarantieCheckResponse {
  guarantees: string[];
  hasMatchingGuarantee: boolean;
  message: string;
}

@Component({
  selector: 'app-recherche-contrat',
  templateUrl: './recherche-contrat.component.html',
  styleUrls: ['./recherche-contrat.component.css'],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatDatepickerModule,
    MatFormFieldModule,
    MatInputModule,
    MatNativeDateModule,
  ],
  standalone: true
})
export class RechercheContratComponent implements OnInit {
  searchForm: FormGroup;
  selectedPersonType: 'physique' | 'morale' = 'morale';
  selectedSearchType: string = 'fiscal';

  // Sinistre data from previous component
  sinistreData: OuvertureSinistreData | null = null;
  numeroSinistre: string | null = null;

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private ouvertureSinistreService: OuvertureSinistreService,
    private http: HttpClient
  ) {
    this.searchForm = this.fb.group({
      dateOfOccurrence: ['', Validators.required],
      searchCriterion: ['identifiant', Validators.required],
      personType: ['morale', Validators.required],
      searchType: ['fiscal', Validators.required],
      fiscalNumber: ['', Validators.required]
    });
  }

  contrats: Contrat[] = [];
  noResults: boolean = false;
  selectedCriterion: string = 'immatriculation';
  selectedContrat: Contrat | null = null;
  showDelaiAlert: boolean = false;
  showNoGuaranteeAlert: boolean = false;
  showNoResultsPopup: boolean = false;
  showGarantieSelection: boolean = false;
  delaiMessage: string = '';
  guaranteeMessage: string = '';
  availableGaranties: Garantie[] = [];
  selectedGaranties: Garantie[] = [];

  ngOnInit(): void {
    // Load sinistre data from session storage
    this.loadSinistreDataFromSession();

    // Subscribe to personType changes to update UI
    this.searchForm.get('personType')?.valueChanges.subscribe(value => {
      this.selectedPersonType = value;
    });

    // Subscribe to searchType changes to update UI
    this.searchForm.get('searchType')?.valueChanges.subscribe(value => {
      this.selectedSearchType = value;
    });
  }

  onPersonTypeChange(type: 'physique' | 'morale'): void {
    this.searchForm.patchValue({ personType: type });
  }

  onSearchTypeChange(type: string): void {
    this.searchForm.patchValue({ searchType: type });
  }

  searchContrat(immatriculation: string): Observable<Contrat[]> {
    return this.http.get<Contrat[]>('/api/search-contrat', { params: { immatriculation } })
      .pipe(
        tap((contrats: Contrat[]) => {
          if (contrats.length === 0) {
            this.noResults = true;
            this.showNoResultsPopup = true;
          } else {
            this.noResults = false;
            this.showNoResultsPopup = false;
          }
        })
      );
  }

  selectContrat(contrat: Contrat): void {
    console.log('Selected contract:', contrat);

    const dateOfOccurrence = this.searchForm.get('dateOfOccurrence')?.value;
    const dateCreation = this.sinistreData?.dateCreation;

    if (!dateOfOccurrence || !dateCreation) {
      console.error('Date of occurrence or date creation is missing.');
      return;
    }

    // 1. Check if the selected contract covers the "date de survenance".
    // Assuming the backend handles this check.

    // 2. Calculate the delay between "date de déclaration" and "date de survenance".
    this.http.post<boolean>('/api/check-delai', { dateDeclaration: dateCreation, dateSurvenance: dateOfOccurrence })
      .subscribe(isDelaiExceeded => {
        if (isDelaiExceeded) {
          // 3. Display an alert message if the delay exceeds the allowed limit.
          alert('Ce sinistre est déclaré hors le délai qui peut être une clause de rejet. Nous vous souhaitons la bonne santé, nous prenons vote sinistre, mais ça peut être un motif de rejet.');
        }

        // 4. Check the guarantee coverage.
        this.http.post<string[]>('/api/check-guarantee', { contratId: contrat.id, sinistreId: this.numeroSinistre })
          .subscribe(guarantees => {
            if (guarantees.length === 0) {
              // 5. Set the "sinistre" state to "Sans suite" if no guarantee covers the "sinistre".
              alert('Aucune garantie dans le contrat ne couvre le sinistre. Le sinistre sera enregistré avec un état « Sans suite ».');
            }
          });
      });
  }

  checkContractCoverage(contrat: Contrat, dateOfOccurrence: string, dateCreation: string): void {
    const coverageData = {
      contratId: contrat.id,
      dateEffet: contrat.dateEffet,
      dateFin: contrat.dateFin,
      dateSurvenance: dateOfOccurrence
    };

    this.http.post<{isValid: boolean, message: string}>('/api/check-contract-coverage', coverageData)
      .subscribe({
        next: (response) => {
          if (response.isValid) {
            // Contract covers the date, proceed with delay check
            this.checkDelai(contrat, dateOfOccurrence, dateCreation);
          } else {
            // Contract doesn't cover the date - save with "Sans suite" status
            this.saveWithSansSuiteStatus(contrat, 'Le contrat ne couvre pas la date de survenance');
          }
        },
        error: (error) => {
          console.error('Error checking contract coverage:', error);
          alert('Erreur lors de la vérification de la couverture du contrat.');
        }
      });
  }

  checkDelai(contrat: Contrat, dateOfOccurrence: string, dateCreation: string): void {
    const delaiData = {
      dateDeclaration: dateCreation,
      dateSurvenance: dateOfOccurrence,
      contratId: contrat.id
    };

    this.http.post<DelaiCheckResponse>('/api/check-delai', delaiData)
      .subscribe({
        next: (response) => {
          if (response.isDelaiExceeded) {
            // Show delay alert popup
            this.delaiMessage = response.message;
            this.showDelaiAlert = true;
          } else {
            // Proceed directly to guarantee check
            this.checkGuarantees(contrat);
          }
        },
        error: (error) => {
          console.error('Error checking delay:', error);
          alert('Erreur lors de la vérification des délais.');
        }
      });
  }

  checkGuarantees(contrat: Contrat): void {
    const guaranteeData = {
      contratId: contrat.id,
      produit: contrat.produit,
      sinistreId: this.numeroSinistre,
      evenement: this.sinistreData?.evenement,
      typeDegat: this.sinistreData?.degatType
    };

    this.http.post<GarantieCheckResponse>('/api/check-guarantee', guaranteeData)
      .subscribe({
        next: (response) => {
          if (response.hasMatchingGuarantee) {
            // Show guarantees selection
            this.showGuaranteeSelection(response.guarantees);
          } else {
            // No matching guarantees - show alert and continue with "Sans suite"
            this.guaranteeMessage = response.message;
            this.showNoGuaranteeAlert = true;
          }
        },
        error: (error) => {
          console.error('Error checking guarantees:', error);
          alert('Erreur lors de la vérification des garanties.');
        }
      });
  }

  showGuaranteeSelection(guarantees: string[]): void {
    // Convert string array to Garantie objects for UI
    this.availableGaranties = guarantees.map((name, index) => ({
      id: index + 1,
      contratId: this.selectedContrat?.id || 0,
      nom: name,
      description: `Garantie ${name}`,
      montant: 0
    }));
    this.showGarantieSelection = true;
  }

  selectGarantie(garantie: Garantie): void {
    const index = this.selectedGaranties.findIndex(g => g.id === garantie.id);
    if (index > -1) {
      // Remove if already selected
      this.selectedGaranties.splice(index, 1);
    } else {
      // Add if not selected
      this.selectedGaranties.push(garantie);
    }
  }

  isGarantieSelected(garantie: Garantie): boolean {
    return this.selectedGaranties.some(g => g.id === garantie.id);
  }

  confirmGarantieSelection(): void {
    if (this.selectedGaranties.length === 0) {
      alert('Veuillez sélectionner au moins une garantie.');
      return;
    }

    console.log('Selected guarantees:', this.selectedGaranties);

    // Save the selection and redirect to conducteur component
    const selectionData = {
      contrat: this.selectedContrat,
      garanties: this.selectedGaranties,
      numeroSinistre: this.numeroSinistre
    };

    // Store in session storage for next component
    sessionStorage.setItem('contratSelection', JSON.stringify(selectionData));

    // Navigate to conducteur component
    this.router.navigate(['/agent/declaration-sinistre/conducteur']);
  }

  closeGarantieSelection(): void {
    this.showGarantieSelection = false;
    this.selectedGaranties = [];
  }

  saveWithSansSuiteStatus(contrat: Contrat, reason: string): void {
    const sinistreData = {
      numeroSinistre: this.numeroSinistre,
      contratId: contrat.id,
      etat: 'Sans suite',
      reason: reason
    };

    this.http.post('/api/save-sinistre-sans-suite', sinistreData)
      .subscribe({
        next: () => {
          alert(`Sinistre enregistré avec l'état "Sans suite".\nRaison: ${reason}`);
        },
        error: (error) => {
          console.error('Error saving sinistre:', error);
          alert('Erreur lors de l\'enregistrement du sinistre.');
        }
      });
  }

  // Popup control methods
  closeDelaiAlert(): void {
    this.showDelaiAlert = false;
  }

  continueWithDelai(): void {
    this.showDelaiAlert = false;
    if (this.selectedContrat) {
      this.checkGuarantees(this.selectedContrat);
    }
  }

  closeNoGuaranteeAlert(): void {
    this.showNoGuaranteeAlert = false;
  }

  continueWithoutGuarantee(): void {
    this.showNoGuaranteeAlert = false;
    if (this.selectedContrat) {
      this.saveWithSansSuiteStatus(this.selectedContrat, 'Aucune garantie ne couvre le sinistre');
    }
  }

  closeNoResultsPopup(): void {
    this.showNoResultsPopup = false;
  }

  dateValidator(): AsyncValidatorFn {
    return (control: AbstractControl): Observable<ValidationErrors | null> => {
      if (!control.value) {
        return of(null);
      }

      const dateOfOccurrence = control.value;
      const dateCreation = this.sinistreData?.dateCreation;

      if (!dateCreation) {
        return of(null);
      }

      return this.http.post<boolean>('/api/validate-date', { dateOfOccurrence, dateCreation })
        .pipe(
          map(isValid => {
            return isValid ? null : { invalidDate: true };
          })
        );
    };
  }

  loadSinistreDataFromSession(): void {
    console.log('🔍 Loading sinistre data from session storage...');

    const sessionData = this.ouvertureSinistreService.getFormDataFromSession();
    if (sessionData) {
      this.sinistreData = sessionData.formData;
      this.numeroSinistre = sessionData.numeroSinistre || null;

      console.log('✅ Loaded sinistre data:', this.sinistreData);
      console.log('📄 Numero sinistre:', this.numeroSinistre);

      // Display success message to user
      if (this.numeroSinistre) {
        console.log(`🎉 Sinistre ${this.numeroSinistre} créé avec succès! Vous pouvez maintenant rechercher le contrat.`);
      }
    } else {
      console.log('❌ No sinistre data found in session storage');
      // Optionally redirect back to form if no data found
      // this.router.navigate(['/sinistre']);
    }
  }

  onSubmit(): void {
    console.log('🚀 Form submitted');

    // First validate date de survenance ≤ date de creation
    const dateOfOccurrence = this.searchForm.get('dateOfOccurrence')?.value;
    const dateCreation = this.sinistreData?.dateCreation;

    if (!dateOfOccurrence) {
      alert('Veuillez saisir la date de survenance.');
      return;
    }

    if (dateOfOccurrence && dateCreation) {
      if (new Date(dateOfOccurrence) > new Date(dateCreation)) {
        alert('Erreur: La date de survenance doit être inférieure ou égale à la date de création.');
        return;
      }
    }

    if (this.searchForm.valid) {
      console.log('📋 Form data:', this.searchForm.value);
      console.log('👤 Person type:', this.selectedPersonType);

      // Get the search value (matricule for both person types)
      const searchValue = this.searchForm.get('fiscalNumber')?.value;

      if (!searchValue) {
        alert('Veuillez saisir le critère de recherche.');
        return;
      }

      console.log('🔍 Searching with value:', searchValue);
      this.searchContratByImmatriculation(searchValue, dateOfOccurrence);

    } else {
      console.log('❌ Form is invalid');
      // Mark all fields as touched to trigger validation display
      Object.keys(this.searchForm.controls).forEach(key => {
        const control = this.searchForm.get(key);
        control?.markAsTouched();
      });

      // Show specific validation errors
      if (!this.searchForm.get('dateOfOccurrence')?.value) {
        alert('Veuillez saisir la date de survenance.');
      } else if (!this.searchForm.get('fiscalNumber')?.value) {
        alert('Veuillez saisir le critère de recherche.');
      }
    }
  }

  searchContratByImmatriculation(immatriculation: string, dateSurvenance: string): void {
    console.log('🔍 Starting contract search...');
    console.log('📋 Search parameters:', { immatriculation, dateSurvenance });

    const searchData = {
      immatriculation: immatriculation,
      dateSurvenance: dateSurvenance
    };

    this.http.post<Contrat[]>('/api/search-contrat-with-validation', searchData)
      .subscribe({
        next: (contrats) => {
          console.log('✅ Search completed. Found contracts:', contrats.length);
          console.log('📄 Contract details:', contrats);

          this.contrats = contrats;
          if (contrats.length === 0) {
            console.log('❌ No contracts found');
            this.noResults = true;
            this.showNoResultsPopup = true;
          } else {
            console.log('✅ Contracts found, displaying results');
            this.noResults = false;
            this.showNoResultsPopup = false;
          }
        },
        error: (error) => {
          console.error('❌ Error searching contracts:', error);
          this.noResults = true;
          this.showNoResultsPopup = true;
          alert('Erreur lors de la recherche des contrats. Veuillez réessayer.');
        }
      });
  }
}
