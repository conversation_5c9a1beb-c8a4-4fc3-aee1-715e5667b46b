import { CommonModule } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { Component, OnInit } from '@angular/core';
import { AbstractControl, AsyncValidatorFn, FormBuilder, FormGroup, ReactiveFormsModule, ValidationErrors, Validators } from '@angular/forms';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { Router } from '@angular/router';
import { Observable, map, of } from 'rxjs';
import { tap } from 'rxjs/operators';
import { OuvertureSinistreData, OuvertureSinistreService } from '../../../services/ouverture-sinistre.service';

export interface Contrat {
  clientName: string;
  clientCode: string;
  contractNumber: string;
  immatriculation: string;
}

@Component({
  selector: 'app-recherche-contrat',
  templateUrl: './recherche-contrat.component.html',
  styleUrls: ['./recherche-contrat.component.css'],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatDatepickerModule,
    MatFormFieldModule,
    MatInputModule,
    MatNativeDateModule,
  ],
  standalone: true
})
export class RechercheContratComponent implements OnInit {
  searchForm: FormGroup;
  selectedPersonType: 'physique' | 'morale' = 'morale';
  selectedSearchType: string = 'fiscal';

  // Sinistre data from previous component
  sinistreData: OuvertureSinistreData | null = null;
  numeroSinistre: string | null = null;

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private ouvertureSinistreService: OuvertureSinistreService,
    private http: HttpClient
  ) {
    this.searchForm = this.fb.group({
      dateOfOccurrence: ['', Validators.required],
      searchCriterion: ['identifiant', Validators.required],
      personType: ['morale', Validators.required],
      searchType: ['fiscal', Validators.required],
      fiscalNumber: ['', Validators.required]
    });
  }

  contrats: Contrat[] = [];
  noResults: boolean = false;
  selectedCriterion: string = 'immatriculation';

  ngOnInit(): void {
    // Load sinistre data from session storage
    this.loadSinistreDataFromSession();

    // Subscribe to personType changes to update UI
    this.searchForm.get('personType')?.valueChanges.subscribe(value => {
      this.selectedPersonType = value;
    });

    // Subscribe to searchType changes to update UI
    this.searchForm.get('searchType')?.valueChanges.subscribe(value => {
      this.selectedSearchType = value;
    });
  }

  onPersonTypeChange(type: 'physique' | 'morale'): void {
    this.searchForm.patchValue({ personType: type });
  }

  onSearchTypeChange(type: string): void {
    this.searchForm.patchValue({ searchType: type });
  }

  searchContrat(immatriculation: string): Observable<Contrat[]> {
    return this.http.get<Contrat[]>('/api/search-contrat', { params: { immatriculation } })
      .pipe(
        tap((contrats: Contrat[]) => {
          if (contrats.length === 0) {
            this.noResults = true;
          } else {
            this.noResults = false;
          }
        })
      );
  }

  selectContrat(contrat: Contrat): void {
    console.log('Selected contract:', contrat);

    const dateOfOccurrence = this.searchForm.get('dateOfOccurrence')?.value;
    const dateCreation = this.sinistreData?.dateCreation;

    if (!dateOfOccurrence || !dateCreation) {
      console.error('Date of occurrence or date creation is missing.');
      return;
    }

    // 1. Check if the selected contract covers the "date de survenance".
    // Assuming the backend handles this check.

    // 2. Calculate the delay between "date de déclaration" and "date de survenance".
    this.http.post<boolean>('/api/check-delai', { dateDeclaration: dateCreation, dateSurvenance: dateOfOccurrence })
      .subscribe(isDelaiExceeded => {
        if (isDelaiExceeded) {
          // 3. Display an alert message if the delay exceeds the allowed limit.
          alert('Ce sinistre est déclaré hors le délai qui peut être une clause de rejet. Nous vous souhaitons la bonne santé, nous prenons vote sinistre, mais ça peut être un motif de rejet.');
        }

        // 4. Check the guarantee coverage.
        this.http.post<string[]>('/api/check-guarantee', { contratId: contrat.contractNumber, sinistreId: this.numeroSinistre })
          .subscribe(guarantees => {
            if (guarantees.length === 0) {
              // 5. Set the "sinistre" state to "Sans suite" if no guarantee covers the "sinistre".
              alert('Aucune garantie dans le contrat ne couvre le sinistre. Le sinistre sera enregistré avec un état « Sans suite ».');
            }
          });
      });
  }

  dateValidator(): AsyncValidatorFn {
    return (control: AbstractControl): Observable<ValidationErrors | null> => {
      if (!control.value) {
        return of(null);
      }

      const dateOfOccurrence = control.value;
      const dateCreation = this.sinistreData?.dateCreation;

      if (!dateCreation) {
        return of(null);
      }

      return this.http.post<boolean>('/api/validate-date', { dateOfOccurrence, dateCreation })
        .pipe(
          map(isValid => {
            return isValid ? null : { invalidDate: true };
          })
        );
    };
  }

  loadSinistreDataFromSession(): void {
    console.log('🔍 Loading sinistre data from session storage...');

    const sessionData = this.ouvertureSinistreService.getFormDataFromSession();
    if (sessionData) {
      this.sinistreData = sessionData.formData;
      this.numeroSinistre = sessionData.numeroSinistre || null;

      console.log('✅ Loaded sinistre data:', this.sinistreData);
      console.log('📄 Numero sinistre:', this.numeroSinistre);

      // Display success message to user
      if (this.numeroSinistre) {
        console.log(`🎉 Sinistre ${this.numeroSinistre} créé avec succès! Vous pouvez maintenant rechercher le contrat.`);
      }
    } else {
      console.log('❌ No sinistre data found in session storage');
      // Optionally redirect back to form if no data found
      // this.router.navigate(['/sinistre']);
    }
  }

  onSubmit(): void {
    this.searchForm.get('dateOfOccurrence')?.setAsyncValidators([this.dateValidator()]);
    this.searchForm.get('dateOfOccurrence')?.updateValueAndValidity();

    if (this.searchForm.valid) {
      console.log('Form data:', this.searchForm.value);
      const searchCriterion = this.searchForm.get('searchCriterion')?.value;
      if (searchCriterion === 'immatriculation') {
        const immatriculation = this.searchForm.get('fiscalNumber')?.value;
        this.searchContrat(immatriculation).subscribe(contrats => {
          this.contrats = contrats;
        });
      } else {
        // Navigate to next step or handle form submission
        this.router.navigate(['/agent/declaration-sinistre/next-step']);
      }
    } else {
      // Mark all fields as touched to trigger validation display
      Object.keys(this.searchForm.controls).forEach(key => {
        const control = this.searchForm.get(key);
        control?.markAsTouched();
      });
    }
  }
}
