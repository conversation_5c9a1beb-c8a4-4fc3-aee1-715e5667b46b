import { CommonModule } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { Component, OnInit } from '@angular/core';
import { AbstractControl, AsyncValidatorFn, FormBuilder, FormGroup, ReactiveFormsModule, ValidationErrors, Validators } from '@angular/forms';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { Router } from '@angular/router';
import { Observable, map, of } from 'rxjs';
import { tap } from 'rxjs/operators';
import { environment } from '../../../../environments/environment';
import { OuvertureSinistreData, OuvertureSinistreService } from '../../../services/ouverture-sinistre.service';

export interface Contrat {
  id: number;
  numContrat: string;
  immatriculation: string;
  dateEffet: string;
  dateFin: string;
  produit: string;
  nom: string;
  clientName: string;
  clientCode: string;
}

export interface Garantie {
  id: number;
  contratId: number;
  nom: string;
  description: string;
  montant: number;
}

export interface DelaiCheckResponse {
  isDelaiExceeded: boolean;
  delaiJours: number;
  message: string;
}

export interface GarantieCheckResponse {
  guarantees: string[];
  hasMatchingGuarantee: boolean;
  message: string;
  evenement?: string;
  typeDegat?: string;
  responsabilite?: string;
}

@Component({
  selector: 'app-recherche-contrat',
  templateUrl: './recherche-contrat.component.html',
  styleUrls: ['./recherche-contrat.component.css'],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatDatepickerModule,
    MatFormFieldModule,
    MatInputModule,
    MatNativeDateModule,
  ],
  standalone: true
})
export class RechercheContratComponent implements OnInit {
  searchForm: FormGroup;
  selectedPersonType: 'physique' | 'morale' = 'morale';
  selectedSearchType: string = 'fiscal';

  // Sinistre data from previous component
  sinistreData: OuvertureSinistreData | null = null;
  numeroSinistre: string | null = null;

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private ouvertureSinistreService: OuvertureSinistreService,
    private http: HttpClient
  ) {
    this.searchForm = this.fb.group({
      dateOfOccurrence: ['', Validators.required],
      searchCriterion: ['identifiant', Validators.required],
      personType: ['morale', Validators.required],
      searchType: ['fiscal', Validators.required],
      fiscalNumber: ['', Validators.required]
    });
  }

  contrats: Contrat[] = [];
  noResults: boolean = false;
  selectedCriterion: string = 'immatriculation';
  selectedContrat: Contrat | null = null;
  showDelaiAlert: boolean = false;
  showNoGuaranteeAlert: boolean = false;
  showNoResultsPopup: boolean = false;
  showGarantieSelection: boolean = false;
  showDateValidationError: boolean = false;
  delaiMessage: string = '';
  guaranteeMessage: string = '';
  dateValidationMessage: string = '';
  availableGaranties: Garantie[] = [];
  selectedGaranties: Garantie[] = [];

  private apiUrl = environment.apiUrl;

  ngOnInit(): void {
    // Test backend connection first
    this.testBackendConnection();

    // Load sinistre data from session storage
    this.loadSinistreDataFromSession();

    // Subscribe to personType changes to update UI
    this.searchForm.get('personType')?.valueChanges.subscribe(value => {
      this.selectedPersonType = value;
    });

    // Subscribe to searchType changes to update UI
    this.searchForm.get('searchType')?.valueChanges.subscribe(value => {
      this.selectedSearchType = value;
    });
  }

  testBackendConnection(): void {
    console.log('🔍 Testing backend connection...');

    // Try direct backend connection first
    this.http.get<any>('http://localhost:8080/api/health')
      .subscribe({
        next: (response) => {
          console.log('✅ Backend is running directly:', response);
          console.log('🔄 Proxy might not be working, but backend is accessible');
        },
        error: (error) => {
          console.error('❌ Direct backend connection failed:', error);

          // Fallback to proxy
          this.http.get<any>('/api/health')
            .subscribe({
              next: (response) => {
                console.log('✅ Backend is running via proxy:', response);
              },
              error: (proxyError) => {
                console.error('❌ Proxy connection also failed:', proxyError);
                console.log('⚠️ Backend server appears to be down. Using fallback mechanisms.');
              }
            });
        }
      });
  }

  onPersonTypeChange(type: 'physique' | 'morale'): void {
    this.searchForm.patchValue({ personType: type });
  }

  onSearchTypeChange(type: string): void {
    this.searchForm.patchValue({ searchType: type });
  }

  searchContrat(immatriculation: string): Observable<Contrat[]> {
    return this.http.get<Contrat[]>('/api/search-contrat', { params: { immatriculation } })
      .pipe(
        tap((contrats: Contrat[]) => {
          if (contrats.length === 0) {
            this.noResults = true;
            this.showNoResultsPopup = true;
          } else {
            this.noResults = false;
            this.showNoResultsPopup = false;
          }
        })
      );
  }

  selectContrat(contrat: Contrat): void {
    console.log('Selected contract:', contrat);

    const dateOfOccurrence = this.searchForm.get('dateOfOccurrence')?.value;
    const dateCreation = this.sinistreData?.dateCreation;

    console.log('📅 Date of occurrence:', dateOfOccurrence);
    console.log('📅 Date creation:', dateCreation);
    console.log('📋 Sinistre data:', this.sinistreData);

    // 1. Validate that date de survenance is provided
    if (!dateOfOccurrence) {
      alert('Veuillez saisir la date de survenance avant de sélectionner un contrat.');
      return;
    }

    // 2. Validate that date de survenance ≤ date de déclaration (date_creation)
    if (!dateCreation) {
      console.error('❌ Date creation missing from sinistre data');
      console.log('🔄 Using current date as fallback for date_creation');

      // Use current date as fallback
      const currentDate = new Date().toISOString();
      if (this.sinistreData) {
        this.sinistreData.dateCreation = currentDate;
      } else {
        console.error('❌ No sinistre data available at all');
        alert('Données du sinistre manquantes. Veuillez recommencer le processus depuis le début.');
        return;
      }

      // Update the dateCreation variable for the rest of the method
      const updatedDateCreation = this.sinistreData.dateCreation;

      // Re-validate with the fallback date
      const survenance = new Date(dateOfOccurrence);
      const creation = new Date(updatedDateCreation);

      if (survenance > creation) {
        this.dateValidationMessage = 'Erreur: La date de survenance doit être inférieure ou égale à la date de déclaration.';
        this.showDateValidationError = true;
        return;
      }

      // Continue with the updated date
      this.proceedWithContractValidation(contrat, dateOfOccurrence, updatedDateCreation);
      return;
    }

    this.proceedWithContractValidation(contrat, dateOfOccurrence, dateCreation);
  }

  proceedWithContractValidation(contrat: Contrat, dateOfOccurrence: string, dateCreation: string): void {
    // Convert dates for comparison
    const survenance = new Date(dateOfOccurrence);
    const creation = new Date(dateCreation);

    if (survenance > creation) {
      this.dateValidationMessage = 'Erreur: La date de survenance doit être inférieure ou égale à la date de déclaration.';
      this.showDateValidationError = true;
      return;
    }

    // Calculate delay between date_creation and date_survenance
    const delaiJours = Math.floor((creation.getTime() - survenance.getTime()) / (1000 * 60 * 60 * 24));

    console.log(`📊 Calculated delay: ${delaiJours} days`);
    console.log(`📅 Date creation: ${dateCreation}`);
    console.log(`📅 Date survenance: ${dateOfOccurrence}`);

    // 4. Check if delay exceeds allowed limit (typically 30 days for insurance)
    if (delaiJours > 30) {
      this.delaiMessage = `Ce sinistre est déclaré hors le délai qui peut être une clause de rejet. (Délai: ${delaiJours} jours)`;
      this.showDelaiAlert = true;
      // Don't return here - let user continue after seeing the alert
    } else {
      console.log('✅ Delay is within acceptable limits');
      // Proceed directly to guarantee check
      this.checkGuarantees(contrat);
    }

    // 1. Check if the selected contract covers the "date de survenance".
    // Assuming the backend handles this check.

    // 2. Calculate the delay between "date de déclaration" and "date de survenance".
    this.http.post<DelaiCheckResponse>('http://localhost:8080/api/check-delai', { dateDeclaration: dateCreation, dateSurvenance: dateOfOccurrence })
      .subscribe({
        next: (response) => {
          console.log('✅ Delay check result:', response);
          if (response.isDelaiExceeded) {
            // 3. Display an alert message if the delay exceeds the allowed limit.
          alert('Ce sinistre est déclaré hors le délai qui peut être une clause de rejet. Nous vous souhaitons la bonne santé, nous prenons vote sinistre, mais ça peut être un motif de rejet.');
        }

        // 4. Check the guarantee coverage.
        this.http.post<string[]>('http://localhost:8080/api/check-guarantee', { contratId: contrat.id, sinistreId: this.numeroSinistre })
          .subscribe(guarantees => {
            if (guarantees.length === 0) {
              // 5. Set the "sinistre" state to "Sans suite" if no guarantee covers the "sinistre".
              alert('Aucune garantie dans le contrat ne couvre le sinistre. Le sinistre sera enregistré avec un état « Sans suite ».');
            }
          });
      });
  }

  checkContractCoverage(contrat: Contrat, dateOfOccurrence: string, dateCreation: string): void {
    console.log('🔍 Checking contract coverage...');

    const coverageData = {
      contratId: contrat.id,
      dateEffet: contrat.dateEffet,
      dateFin: contrat.dateFin,
      dateSurvenance: dateOfOccurrence
    };

    this.http.post<{isValid: boolean, message: string}>('http://localhost:8080/api/check-contract-coverage', coverageData)
      .subscribe({
        next: (response) => {
          console.log('✅ Contract coverage result:', response);
          if (response.isValid) {
            // Contract covers the date, proceed with delay check
            this.checkDelai(contrat, dateOfOccurrence, dateCreation);
          } else {
            // Contract doesn't cover the date - save with "Sans suite" status
            alert(`Le contrat ne couvre pas la date de survenance. ${response.message}`);
            this.saveWithSansSuiteStatus(contrat, 'Le contrat ne couvre pas la date de survenance');
          }
        },
        error: (error) => {
          console.error('❌ Error checking contract coverage:', error);
          // Fallback: proceed with delay check anyway for demo
          console.log('🔄 Proceeding with delay check as fallback...');
          this.checkDelai(contrat, dateOfOccurrence, dateCreation);
        }
      });
  }

  checkDelai(contrat: Contrat, dateOfOccurrence: string, dateCreation: string): void {
    console.log('🔍 Checking delay between dates...');

    // Calculate delay locally first
    const survenance = new Date(dateOfOccurrence);
    const creation = new Date(dateCreation);
    const delaiJours = Math.floor((creation.getTime() - survenance.getTime()) / (1000 * 60 * 60 * 24));

    console.log(`📊 Calculated delay: ${delaiJours} days (creation: ${dateCreation}, survenance: ${dateOfOccurrence})`);

    const delaiData = {
      dateDeclaration: dateCreation,
      dateSurvenance: dateOfOccurrence,
      contratId: contrat.id
    };

    this.http.post<DelaiCheckResponse>('http://localhost:8080/api/check-delai', delaiData)
      .subscribe({
        next: (response) => {
          console.log('✅ Delay check result:', response);
          if (response.isDelaiExceeded) {
            // Show delay alert popup with the specific message
            this.delaiMessage = `${response.message} (Délai: ${response.delaiJours} jours)`;
            this.showDelaiAlert = true;
          } else {
            console.log('✅ Delay is within limits, proceeding to guarantee check');
            // Proceed directly to guarantee check
            this.checkGuarantees(contrat);
          }
        },
        error: (error) => {
          console.error('❌ Error checking delay:', error);
          // Fallback: use local calculation
          if (delaiJours > 30) {
            this.delaiMessage = `Ce sinistre est déclaré hors le délai qui peut être une clause de rejet. (Délai: ${delaiJours} jours)`;
            this.showDelaiAlert = true;
          } else {
            console.log('🔄 Using fallback delay check - proceeding to guarantee check');
            this.checkGuarantees(contrat);
          }
        }
      });
  }

  checkGuarantees(contrat: Contrat): void {
    console.log('🔍 Checking guarantee coverage from ouverture_sinistre table...');

    const guaranteeData = {
      contratId: contrat.id,
      produit: contrat.produit,
      sinistreId: this.numeroSinistre,
      evenement: this.sinistreData?.evenement,
      typeDegat: this.sinistreData?.degatType
    };

    console.log('📋 Guarantee check data:', guaranteeData);
    console.log('📋 Using numeroSinistre:', this.numeroSinistre);

    this.http.post<GarantieCheckResponse>('http://localhost:8080/api/check-guarantee', guaranteeData)
      .subscribe({
        next: (response) => {
          console.log('✅ Guarantee check result from ouverture_sinistre:', response);

          // Log the data retrieved from ouverture_sinistre table
          if (response.evenement) {
            console.log('📋 Data from ouverture_sinistre table:');
            console.log('   - Evenement:', response.evenement);
            console.log('   - Type degat:', response.typeDegat);
            console.log('   - Responsabilite:', response.responsabilite);
            console.log('   - Garanties:', response.guarantees);
          }

          if (response.hasMatchingGuarantee && response.guarantees.length > 0) {
            // Show guarantees selection from ouverture_sinistre garantie column
            console.log('✅ Found guarantees from ouverture_sinistre.garantie column, showing selection');
            this.showGuaranteeSelection(response.guarantees);
          } else {
            // No matching guarantees - show alert and continue with "Sans suite"
            console.log('❌ No guarantees found in ouverture_sinistre.garantie column');
            this.guaranteeMessage = response.message || 'Aucune garantie dans le contrat ne couvre le sinistre.';
            this.showNoGuaranteeAlert = true;
          }
        },
        error: (error) => {
          console.error('❌ Error checking guarantees:', error);
          console.log('🔄 Backend not available, using mock guarantee selection...');

          // Check if it's a login redirect (backend not running)
          if (error.url && error.url.includes('/login')) {
            console.log('⚠️ Backend server appears to be down - API calls redirecting to login');
          }

          // Fallback: show mock guarantees for demo
          this.showMockGuaranteeSelection();
        }
      });
  }

  showGuaranteeSelection(guarantees: string[]): void {
    // Convert string array to Garantie objects for UI
    this.availableGaranties = guarantees.map((name, index) => ({
      id: index + 1,
      contratId: this.selectedContrat?.id || 0,
      nom: name,
      description: `Garantie ${name}`,
      montant: 0
    }));
    this.showGarantieSelection = true;
  }

  selectGarantie(garantie: Garantie): void {
    const index = this.selectedGaranties.findIndex(g => g.id === garantie.id);
    if (index > -1) {
      // Remove if already selected
      this.selectedGaranties.splice(index, 1);
    } else {
      // Add if not selected
      this.selectedGaranties.push(garantie);
    }
  }

  isGarantieSelected(garantie: Garantie): boolean {
    return this.selectedGaranties.some(g => g.id === garantie.id);
  }

  confirmGarantieSelection(): void {
    if (this.selectedGaranties.length === 0) {
      alert('Veuillez sélectionner au moins une garantie.');
      return;
    }

    console.log('✅ Confirming guarantee selection...');
    console.log('📋 Selected guarantees:', this.selectedGaranties);
    console.log('📄 Selected contract:', this.selectedContrat);
    console.log('🔢 Numero sinistre:', this.numeroSinistre);

    // Prepare comprehensive data for next component
    const completeSelectionData = {
      // Contract information
      contrat: this.selectedContrat,

      // Selected guarantees
      garanties: this.selectedGaranties,

      // Sinistre information
      numeroSinistre: this.numeroSinistre,
      sinistreData: this.sinistreData,

      // Form data from recherche-contrat
      dateOfOccurrence: this.searchForm.get('dateOfOccurrence')?.value,
      personType: this.selectedPersonType,
      searchType: this.selectedSearchType,

      // Timestamp for tracking
      selectionTimestamp: new Date().toISOString(),

      // Step tracking
      currentStep: 'conducteur',
      completedSteps: ['ouverture-sinistre', 'recherche-contrat', 'garantie-selection']
    };

    console.log('💾 Storing complete selection data:', completeSelectionData);

    // Store in session storage for next component
    if (typeof window !== 'undefined' && window.sessionStorage) {
      sessionStorage.setItem('contratSelection', JSON.stringify(completeSelectionData));
    }

    // Also update the existing sinistre data with contract info
    if (this.sinistreData) {
      const updatedSinistreData = {
        ...this.sinistreData,
        contratId: this.selectedContrat?.id,
        contratNumber: this.selectedContrat?.numContrat,
        immatriculation: this.selectedContrat?.immatriculation,
        selectedGaranties: this.selectedGaranties.map(g => g.nom)
      };

      // Store the updated sinistre data in the complete selection data
      completeSelectionData.sinistreData = updatedSinistreData;
      console.log('🔄 Updated sinistre data with contract info');
    }

    console.log('🚀 Navigating to conducteur component...');

    // Navigate to conducteur component (correct path)
    this.router.navigate(['/conducteur']);
  }

  closeGarantieSelection(): void {
    this.showGarantieSelection = false;
    this.selectedGaranties = [];
  }

  saveWithSansSuiteStatus(contrat: Contrat, reason: string): void {
    const sinistreData = {
      numeroSinistre: this.numeroSinistre,
      contratId: contrat.id,
      etat: 'Sans suite',
      reason: reason
    };

    this.http.post('/api/save-sinistre-sans-suite', sinistreData)
      .subscribe({
        next: () => {
          alert(`Sinistre enregistré avec l'état "Sans suite".\nRaison: ${reason}`);
        },
        error: (error) => {
          console.error('Error saving sinistre:', error);
          alert('Erreur lors de l\'enregistrement du sinistre.');
        }
      });
  }

  // Popup control methods
  closeDelaiAlert(): void {
    this.showDelaiAlert = false;
  }

  continueWithDelai(): void {
    this.showDelaiAlert = false;
    if (this.selectedContrat) {
      this.checkGuarantees(this.selectedContrat);
    }
  }

  closeNoGuaranteeAlert(): void {
    this.showNoGuaranteeAlert = false;
  }

  continueWithoutGuarantee(): void {
    this.showNoGuaranteeAlert = false;
    if (this.selectedContrat) {
      this.saveWithSansSuiteStatus(this.selectedContrat, 'Aucune garantie ne couvre le sinistre');
    }
  }

  closeNoResultsPopup(): void {
    this.showNoResultsPopup = false;
  }

  closeDateValidationError(): void {
    this.showDateValidationError = false;
    this.dateValidationMessage = '';
  }

  dateValidator(): AsyncValidatorFn {
    return (control: AbstractControl): Observable<ValidationErrors | null> => {
      if (!control.value) {
        return of(null);
      }

      const dateOfOccurrence = control.value;
      const dateCreation = this.sinistreData?.dateCreation;

      if (!dateCreation) {
        return of(null);
      }

      return this.http.post<boolean>('/api/validate-date', { dateOfOccurrence, dateCreation })
        .pipe(
          map(isValid => {
            return isValid ? null : { invalidDate: true };
          })
        );
    };
  }

  loadSinistreDataFromSession(): void {
    console.log('🔍 Loading sinistre data from session storage...');

    const sessionData = this.ouvertureSinistreService.getFormDataFromSession();
    if (sessionData) {
      this.sinistreData = sessionData.formData;
      this.numeroSinistre = sessionData.numeroSinistre || null;

      console.log('✅ Loaded sinistre data:', this.sinistreData);
      console.log('📄 Numero sinistre:', this.numeroSinistre);

      // Ensure we always have a date_creation
      if (!this.sinistreData.dateCreation) {
        console.log('⚠️ No date_creation in session data, using current date');
        this.sinistreData.dateCreation = new Date().toISOString();
      }

      // If we have a numero sinistre, try to fetch the actual date_creation from backend
      if (this.numeroSinistre) {
        console.log(`🎉 Sinistre ${this.numeroSinistre} créé avec succès! Fetching actual date_creation...`);
        this.fetchSinistreDetails(this.numeroSinistre);
      }
    } else {
      console.log('❌ No sinistre data found in session storage');
      // Create minimal sinistre data with current date as fallback
      this.sinistreData = {
        dateCreation: new Date().toISOString(),
        evenement: '',
        degatType: '',
        tiersExiste: false
      } as OuvertureSinistreData;
      console.log('✅ Created fallback sinistre data with current date');
    }
  }

  fetchSinistreDetails(numeroSinistre: string): void {
    console.log('🔍 Fetching sinistre details for:', numeroSinistre);

    this.http.get<any>(`http://localhost:8080/api/ouverture-sinistre/${numeroSinistre}`)
      .subscribe({
        next: (sinistreDetails) => {
          console.log('✅ Fetched sinistre details:', sinistreDetails);
          if (sinistreDetails && sinistreDetails.dateCreation) {
            // Update the sinistre data with the actual date_creation from database
            this.sinistreData = {
              ...this.sinistreData!,
              dateCreation: sinistreDetails.dateCreation
            };
            console.log('✅ Updated sinistre data with actual date_creation:', this.sinistreData.dateCreation);
          }
        },
        error: (error) => {
          console.error('❌ Error fetching sinistre details:', error);
          console.log('🔄 Backend not available, using current date as fallback');

          // Use current date as fallback when backend is not available
          const currentDate = new Date().toISOString();
          if (this.sinistreData) {
            this.sinistreData.dateCreation = currentDate;
            console.log('✅ Set fallback date_creation:', currentDate);
          }
        }
      });
  }

  onSubmit(): void {
    console.log('🚀 Form submitted');

    // First validate date de survenance ≤ date de creation
    const dateOfOccurrence = this.searchForm.get('dateOfOccurrence')?.value;
    const dateCreation = this.sinistreData?.dateCreation;

    if (!dateOfOccurrence) {
      alert('Veuillez saisir la date de survenance.');
      return;
    }

    if (dateOfOccurrence && dateCreation) {
      if (new Date(dateOfOccurrence) > new Date(dateCreation)) {
        this.dateValidationMessage = 'Erreur: La date de survenance doit être inférieure ou égale à la date de création.';
        this.showDateValidationError = true;
        return;
      }
    }

    if (this.searchForm.valid) {
      console.log('📋 Form data:', this.searchForm.value);
      console.log('👤 Person type:', this.selectedPersonType);

      // Get the search value (matricule for both person types)
      const searchValue = this.searchForm.get('fiscalNumber')?.value;

      if (!searchValue) {
        alert('Veuillez saisir le critère de recherche.');
        return;
      }

      console.log('🔍 Searching with value:', searchValue);
      this.searchContratByImmatriculation(searchValue, dateOfOccurrence);

    } else {
      console.log('❌ Form is invalid');
      // Mark all fields as touched to trigger validation display
      Object.keys(this.searchForm.controls).forEach(key => {
        const control = this.searchForm.get(key);
        control?.markAsTouched();
      });

      // Show specific validation errors
      if (!this.searchForm.get('dateOfOccurrence')?.value) {
        alert('Veuillez saisir la date de survenance.');
      } else if (!this.searchForm.get('fiscalNumber')?.value) {
        alert('Veuillez saisir le critère de recherche.');
      }
    }
  }

  searchContratByImmatriculation(immatriculation: string, dateSurvenance: string): void {
    console.log('🔍 Starting contract search...');
    console.log('📋 Search parameters:', { immatriculation, dateSurvenance });

    const searchData = {
      immatriculation: immatriculation,
      dateSurvenance: dateSurvenance
    };

    // Remove mock data - use only real database search

    // Try the real API call - using direct backend URL since proxy might not be working
    this.http.post<Contrat[]>('http://localhost:8080/api/search-contrat-with-validation', searchData)
      .subscribe({
        next: (contrats) => {
          console.log('✅ Search completed. Found contracts:', contrats.length);
          console.log('📄 Contract details:', contrats);

          this.contrats = contrats;
          if (contrats.length === 0) {
            console.log('❌ No contracts found');
            this.noResults = true;
            this.showNoResultsPopup = true;
          } else {
            console.log('✅ Contracts found, displaying results');
            this.noResults = false;
            this.showNoResultsPopup = false;
          }
        },
        error: (error) => {
          console.error('❌ Error searching contracts:', error);
          console.log('❌ No contracts found or API error');

          // Show no results - no more mock data
          this.contrats = [];
          this.noResults = true;
          this.showNoResultsPopup = true;
        }
      });
  }

  showMockGuaranteeSelection(): void {
    console.log('🧪 Showing mock guarantee selection');

    // Mock guarantees based on contract type
    const mockGuarantees = [
      'Responsabilité Civile',
      'Dommages Matériels',
      'Protection Juridique',
      'Assistance Dépannage'
    ];

    this.availableGaranties = mockGuarantees.map((name, index) => ({
      id: index + 1,
      contratId: this.selectedContrat?.id || 0,
      nom: name,
      description: `Garantie ${name}`,
      montant: 0
    }));

    this.showGarantieSelection = true;
  }
}
