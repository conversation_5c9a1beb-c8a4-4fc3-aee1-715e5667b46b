<body>
  
<div class="top-navbar">
    <!-- Logo -->
    <div class="logo-container">
      <svg width="212" height="45" viewBox="0 0 212 45" fill="none" xmlns="http://www.w3.org/2000/svg">
        <g clip-path="url(#clip0_284_2665)">
        <path d="M71.5741 44.8035H68.3639C68.0491 44.8159 67.7384 44.7279 67.4769 44.5521C67.2519 44.3938 67.083 44.1682 66.9943 43.9078L65.9397 40.7987H59.1553L58.1035 43.9193C58.0062 44.1671 57.8397 44.3818 57.6239 44.5377C57.3767 44.7306 57.0706 44.8326 56.7571 44.8266H53.5238L60.4295 26.9844H64.6654L71.5741 44.8035ZM60.1001 37.9815H64.9746L63.3334 33.0955L62.9578 32.0004C62.8162 31.5786 62.6688 31.1336 62.5359 30.6337C62.4069 31.1307 62.272 31.5921 62.1314 32.0178C61.9908 32.4435 61.8608 32.8104 61.7413 33.1186L60.1001 37.9815Z" fill="#1C3F93"/>
        <path d="M82.9613 30.5847C82.8622 30.7549 82.7318 30.9049 82.577 31.0268C82.4238 31.1301 82.2416 31.1817 82.0569 31.1741C81.8429 31.1661 81.6342 31.1046 81.4502 30.995L80.7162 30.5963C80.4143 30.4395 80.1003 30.3071 79.7772 30.2004C79.3899 30.0758 78.9849 30.0153 78.5781 30.0213C77.8075 30.0213 77.2297 30.1869 76.8444 30.5182C76.6538 30.6842 76.503 30.8908 76.403 31.1229C76.3029 31.3549 76.2563 31.6065 76.2665 31.8589C76.2617 32.0231 76.2896 32.1867 76.3486 32.34C76.4076 32.4933 76.4966 32.6333 76.6104 32.7518C76.8693 33.01 77.174 33.2177 77.509 33.3643C77.9217 33.5521 78.3476 33.7095 78.7832 33.8353C79.2629 33.9798 79.7483 34.1387 80.2482 34.315C80.7475 34.4915 81.2357 34.698 81.7102 34.9333C82.1804 35.1651 82.6105 35.4704 82.9844 35.8377C83.3663 36.2236 83.6712 36.6786 83.8831 37.1784C84.1277 37.7826 84.2447 38.4309 84.2269 39.0825C84.2335 39.8733 84.0883 40.658 83.7993 41.3941C83.5217 42.1013 83.0984 42.7422 82.5568 43.2751C81.9819 43.8303 81.2981 44.2603 80.5486 44.5378C79.6803 44.8567 78.7604 45.0124 77.8355 44.9972C77.2658 44.9957 76.6976 44.9377 76.1394 44.8238C75.5688 44.7119 75.009 44.5505 74.4664 44.3413C73.9402 44.1383 73.4326 43.8898 72.9494 43.5987C72.5002 43.3321 72.0849 43.0119 71.7128 42.6452L72.935 40.7093C73.0287 40.5593 73.1602 40.4367 73.3164 40.3539C73.4774 40.2651 73.6585 40.2193 73.8423 40.221C74.1065 40.2273 74.3637 40.3074 74.5849 40.4521C74.8334 40.6081 75.1136 40.7786 75.4286 40.9664C75.7738 41.1696 76.1364 41.3418 76.5121 41.4807C76.9754 41.646 77.465 41.7253 77.9568 41.7148C78.5799 41.7522 79.1973 41.577 79.7078 41.2178C80.1239 40.8884 80.3319 40.3635 80.3319 39.6431C80.3488 39.2743 80.2272 38.9126 79.991 38.6289C79.7409 38.3497 79.4333 38.128 79.0895 37.9788C78.6805 37.7937 78.2573 37.642 77.8239 37.5251C77.35 37.3951 76.8617 37.2506 76.3648 37.0917C75.8696 36.935 75.3867 36.7418 74.92 36.5138C74.448 36.2832 74.019 35.9734 73.6516 35.5979C73.2624 35.1869 72.9572 34.7041 72.753 34.1763C72.5065 33.5041 72.3898 32.7913 72.4091 32.0757C72.4123 31.3894 72.5551 30.7109 72.8288 30.0815C73.1025 29.4522 73.5014 28.8851 74.0012 28.4147C74.5576 27.8952 75.2104 27.4898 75.9227 27.2214C76.7536 26.9099 77.6359 26.758 78.5232 26.7735C79.0583 26.7731 79.5927 26.8156 80.121 26.9007C80.6237 26.983 81.1179 27.1106 81.5975 27.2821C82.0484 27.4433 82.4844 27.6434 82.9006 27.8802C83.2902 28.1028 83.6537 28.3684 83.9842 28.6719L82.9613 30.5847Z" fill="#1C3F93"/>
        <path d="M96.2383 30.5847C96.1382 30.7551 96.0068 30.905 95.8511 31.0268C95.6979 31.1301 95.5156 31.1817 95.331 31.1741C95.1169 31.1661 94.9083 31.1045 94.7242 30.995L93.9903 30.5962C93.6891 30.4399 93.3761 30.3076 93.0541 30.2004C92.6658 30.0758 92.2599 30.0153 91.8521 30.0212C91.0816 30.0212 90.5037 30.1869 90.1185 30.5182C89.9274 30.6838 89.7762 30.8903 89.6762 31.1225C89.5761 31.3546 89.5298 31.6063 89.5406 31.8589C89.5352 32.0229 89.5625 32.1864 89.6211 32.3397C89.6796 32.493 89.7682 32.6331 89.8815 32.7517C90.141 33.0106 90.4468 33.2184 90.783 33.3643C91.1954 33.5525 91.6213 33.7104 92.0573 33.8353C92.535 33.9798 93.0223 34.1396 93.5193 34.3149C94.0187 34.4915 94.5069 34.698 94.9814 34.9333C95.4524 35.1636 95.8828 35.469 96.2556 35.8377C96.6392 36.2227 96.9453 36.6778 97.1571 37.1784C97.3993 37.7832 97.5153 38.4312 97.4981 39.0825C97.5047 39.8733 97.3595 40.6579 97.0704 41.394C96.7929 42.1013 96.3696 42.7422 95.828 43.2751C95.2531 43.8303 94.5693 44.2602 93.8198 44.5377C92.9514 44.8566 92.0316 45.0124 91.1067 44.9972C90.5369 44.9957 89.9688 44.9376 89.4106 44.8238C88.8408 44.7123 88.2819 44.5508 87.7405 44.3413C87.213 44.1388 86.7045 43.8903 86.2206 43.5987C85.7713 43.332 85.3561 43.0118 84.9839 42.6452L86.2091 40.7092C86.3016 40.5594 86.4322 40.4368 86.5876 40.3538C86.7487 40.2654 86.9297 40.2197 87.1135 40.2209C87.3778 40.2268 87.6351 40.3069 87.856 40.4521C88.1045 40.6081 88.3848 40.7786 88.6998 40.9664C89.045 41.1696 89.4076 41.3417 89.7833 41.4807C90.2466 41.6459 90.7362 41.7252 91.228 41.7148C91.852 41.7516 92.47 41.5765 92.9819 41.2178C93.4057 40.8999 93.6176 40.376 93.6176 39.6459C93.6333 39.2768 93.5107 38.9151 93.2737 38.6317C93.0236 38.3526 92.7161 38.1308 92.3722 37.9816C91.9634 37.7961 91.5402 37.6444 91.1067 37.528C90.6328 37.3989 90.1464 37.2544 89.6475 37.0946C89.1523 36.9379 88.6694 36.7447 88.2028 36.5167C87.7308 36.286 87.3017 35.9762 86.9343 35.6007C86.5452 35.1898 86.2399 34.7069 86.0357 34.1791C85.7892 33.507 85.6725 32.7942 85.6919 32.0785C85.6905 31.393 85.8289 30.714 85.98 30.0827C86.217 29.4513 86.61 28.8809 87.104 28.406C87.661 27.8859 88.315 27.4804 89.029 27.2127C90.86 26.9006 92.742 26.7486 94.629 26.7648C95.164 26.7643 95.699 26.8068 96.227 26.892C96.73 26.9743 97.224 27.1019 97.704 27.2734C98.155 27.4346 98.592 27.6347 99.01 27.8715C99.398 28.0956 99.762 28.361 100.093 28.6632L96.2383 30.5847Z" fill="#1C3F93"/>
        <path d="M106.99 41.5328C107.5 41.5364 108.006 41.4382 108.478 41.2439C108.903 41.0682 109.281 40.7973 109.585 40.4522C109.901 40.0874 110.137 39.6601 110.278 39.1982C110.444 38.6524 110.524 38.084 110.515 37.5136V26.9614H114.655V37.5281C114.67 38.5564 114.493 39.5785 114.133 40.5418C113.795 41.4277 113.275 42.2329 112.607 42.9053C111.92 43.5835 111.098 44.1093 110.194 44.4483C108.122 45.1841 105.86 45.1841 103.788 44.4483C102.885 44.1086 102.063 43.5829 101.376 42.9053C100.709 42.2332 100.192 41.4278 99.8587 40.5418C99.4986 39.5784 99.3203 38.5565 99.3328 37.5281V26.9614H103.471V37.5165C103.462 38.0871 103.543 38.6554 103.71 39.2011C103.853 39.6635 104.088 40.0922 104.401 40.4608C104.705 40.808 105.084 41.0809 105.51 41.2583C105.982 41.4526 106.488 41.5509 106.998 41.5473" fill="#1C3F93"/>
        <path d="M121.489 38.2012V44.8035H117.348V26.9613H123.153C124.267 26.9395 125.379 27.0727 126.456 27.3571C127.266 27.5724 128.024 27.9499 128.684 28.4667C129.238 28.9106 129.669 29.4883 129.938 30.1454C130.205 30.8143 130.338 31.5288 130.331 32.2489C130.334 32.8043 130.256 33.3572 130.1 33.8901C129.946 34.3999 129.712 34.8817 129.406 35.3175C129.091 35.7571 128.711 36.1468 128.279 36.4733C127.812 36.8294 127.293 37.1137 126.742 37.317C127.021 37.4571 127.281 37.6297 127.519 37.8313C127.769 38.0455 127.982 38.2982 128.152 38.5797L131.949 44.7891H128.192C127.895 44.812 127.597 44.7503 127.332 44.6113C127.068 44.4723 126.848 44.2615 126.699 44.0031L123.734 38.8253C123.629 38.6226 123.47 38.4527 123.275 38.3341C123.044 38.2241 122.79 38.1734 122.535 38.1867L121.489 38.2012ZM121.489 35.3493H123.153C123.644 35.3611 124.132 35.2888 124.598 35.1355C124.963 35.0116 125.295 34.8083 125.572 34.5403C125.824 34.2926 126.013 33.9885 126.124 33.6532C126.241 33.2991 126.298 32.9281 126.294 32.5552C126.311 32.2119 126.252 31.8691 126.12 31.5517C125.988 31.2343 125.787 30.9503 125.531 30.7204C125.02 30.2783 124.231 30.0588 123.159 30.0588H121.495L121.489 35.3493Z" fill="#1C3F93"/>
        <path d="M150.25 44.8033H147.043C146.728 44.8164 146.417 44.7283 146.156 44.552C145.929 44.3947 145.759 44.1688 145.671 43.9076L144.619 40.7986H137.826L136.771 43.9192C136.664 44.1876 136.478 44.4171 136.238 44.5771C135.997 44.7372 135.714 44.8201 135.425 44.8149H132.191L139.1 26.9727H143.336L150.25 44.8033ZM138.779 37.9814H143.654L142.013 33.0954C141.909 32.7862 141.781 32.4192 141.64 32.0003C141.498 31.5813 141.351 31.1334 141.218 30.6336C141.089 31.1305 140.954 31.5919 140.814 32.0176C140.675 32.4423 140.545 32.8093 140.421 33.1185L138.779 37.9814Z" fill="#1C3F93"/>
        <path d="M154.322 26.9845C154.435 26.9988 154.546 27.03 154.651 27.0769C154.757 27.1236 154.854 27.187 154.94 27.2648C155.049 27.3692 155.151 27.4802 155.246 27.597L163.721 38.3111C163.686 37.9701 163.663 37.6378 163.646 37.3142C163.628 36.9906 163.623 36.6872 163.623 36.4011V26.9614H167.272V44.8036H165.117C164.84 44.8118 164.564 44.7616 164.308 44.6562C164.054 44.5314 163.836 44.3457 163.672 44.1159L155.252 33.4886C155.275 33.7968 155.295 34.1002 155.313 34.3988C155.33 34.6973 155.339 34.9776 155.339 35.2396V44.8036H151.695V26.9614H153.874C154.025 26.9589 154.177 26.9667 154.327 26.9845" fill="#1C3F93"/>
        <path d="M182.193 40.1861C182.292 40.1868 182.39 40.2054 182.482 40.241C182.591 40.2829 182.69 40.3479 182.771 40.4317L184.412 42.1654C183.709 43.0929 182.78 43.825 181.713 44.292C180.497 44.7949 179.189 45.0389 177.873 45.0086C176.624 45.029 175.384 44.793 174.23 44.3151C173.194 43.8805 172.263 43.2293 171.499 42.4052C170.733 41.5685 170.143 40.5857 169.766 39.5157C169.358 38.3486 169.156 37.1198 169.167 35.8837C169.153 34.6284 169.386 33.3826 169.852 32.217C170.286 31.1326 170.938 30.1491 171.768 29.3276C172.614 28.5058 173.62 27.8666 174.724 27.4495C175.931 26.988 177.214 26.7595 178.506 26.7762C179.141 26.7725 179.775 26.8315 180.399 26.9525C180.966 27.064 181.521 27.2285 182.057 27.4437C182.555 27.6395 183.031 27.8854 183.479 28.1776C183.898 28.4551 184.286 28.7778 184.635 29.1398L183.239 31.0122C183.147 31.1296 183.04 31.2345 182.921 31.3242C182.77 31.4276 182.589 31.4774 182.407 31.4658C182.262 31.4669 182.12 31.4332 181.991 31.3675C181.848 31.2958 181.709 31.2167 181.575 31.1306L181.089 30.8243C180.892 30.7017 180.683 30.5991 180.465 30.5181C180.191 30.4142 179.909 30.3339 179.621 30.2782C179.248 30.2091 178.869 30.1762 178.489 30.18C177.783 30.1711 177.083 30.3068 176.432 30.5787C175.822 30.8363 175.276 31.225 174.834 31.7172C174.372 32.2411 174.017 32.8508 173.791 33.5115C173.533 34.2724 173.407 35.0717 173.418 35.8751C173.404 36.7022 173.53 37.5257 173.791 38.3109C174.012 38.9689 174.358 39.5782 174.811 40.1052C175.225 40.5786 175.736 40.9571 176.31 41.2147C176.89 41.4681 177.516 41.597 178.148 41.5932C178.492 41.5955 178.835 41.5772 179.176 41.5383C179.75 41.4757 180.303 41.2925 180.8 41.0009C181.058 40.8443 181.302 40.6654 181.528 40.4664C181.626 40.3867 181.733 40.3188 181.846 40.2641C181.958 40.2133 182.079 40.1867 182.202 40.1861" fill="#1C3F93"/>
        <path d="M197.782 26.9614V30.1456H190.46V34.3064H196.068V37.3692H190.46V41.6195H197.782V44.8037H186.296V26.9614H197.782Z" fill="#1C3F93"/>
        <path d="M210.079 30.5847C209.979 30.7551 209.847 30.905 209.692 31.0268C209.539 31.1301 209.356 31.1817 209.172 31.1741C208.958 31.1661 208.749 31.1045 208.565 30.995L207.831 30.5962C207.53 30.4399 207.217 30.3076 206.895 30.2004C206.507 30.0758 206.101 30.0153 205.693 30.0212C204.922 30.0212 204.344 30.1869 203.959 30.5182C203.768 30.6838 203.617 30.8903 203.517 31.1225C203.417 31.3546 203.37 31.6063 203.381 31.8589C203.376 32.0229 203.403 32.1864 203.462 32.3397C203.52 32.493 203.609 32.6331 203.722 32.7517C203.982 33.0106 204.288 33.2184 204.624 33.3643C205.035 33.5525 205.46 33.71 205.895 33.8353C206.375 33.9798 206.863 34.1396 207.36 34.3149C207.859 34.4915 208.348 34.698 208.822 34.9333C209.293 35.1636 209.723 35.469 210.096 35.8377C210.48 36.2227 210.786 36.6778 210.998 37.1784C211.24 37.7832 211.356 38.4312 211.339 39.0825C211.345 39.8733 211.2 40.6579 210.911 41.394C210.634 42.1013 210.21 42.7422 209.669 43.2751C209.094 43.8303 208.41 44.2602 207.661 44.5377C206.792 44.8566 205.872 45.0124 204.947 44.9972C204.378 44.9957 203.809 44.9376 203.251 44.8238C202.681 44.7119 202.121 44.5504 201.578 44.3413C201.052 44.1382 200.544 43.8898 200.061 43.5987C199.612 43.332 199.197 43.0118 198.825 42.6452L200.05 40.7092C200.142 40.5594 200.273 40.4368 200.428 40.3538C200.589 40.2654 200.77 40.2197 200.954 40.2209C201.218 40.2273 201.476 40.3074 201.697 40.4521C201.945 40.6081 202.226 40.7786 202.54 40.9664C202.886 41.1696 203.248 41.3417 203.624 41.4807C204.087 41.6459 204.577 41.7252 205.069 41.7148C205.692 41.7521 206.309 41.5769 206.82 41.2178C207.247 40.8999 207.461 40.376 207.461 39.6459C207.477 39.2768 207.354 38.9151 207.117 38.6317C206.867 38.3526 206.56 38.1308 206.216 37.9816C205.807 37.7961 205.384 37.6444 204.95 37.528C204.476 37.3989 203.995 37.2544 203.506 37.0946C203.01 36.9379 202.527 36.7447 202.061 36.5167C201.589 36.286 201.16 35.9762 200.792 35.6007C200.403 35.1898 200.098 34.7069 199.894 34.1791C199.647 33.507 199.531 32.7942 199.55 32.0785C199.547 31.393 199.683 30.714 199.95 30.0827C200.217 29.4513 200.61 28.8809 201.104 28.406C201.661 27.8859 202.315 27.4804 203.029 27.2127C203.86 26.9006 204.742 26.7486 205.629 26.7648C206.164 26.7643 206.699 26.8068 207.227 26.892C207.73 26.9743 208.224 27.1019 208.704 27.2734C209.155 27.4346 209.592 27.6347 210.01 27.8715C210.398 28.0956 210.762 28.361 211.093 28.6632L210.079 30.5847Z" fill="#1C3F93"/>
        <path d="M53.8532 18.0356V0.0662459H60.4642C61.534 0.0459235 62.602 0.161461 63.6426 0.410088C64.4116 0.592289 65.1371 0.924146 65.7779 1.38672C66.3084 1.77917 66.7236 2.30712 66.9799 2.91522C67.2397 3.55734 67.3675 4.24524 67.3555 4.93783C67.3554 5.32934 67.2979 5.71872 67.185 6.0936C67.0655 6.47359 66.8818 6.8303 66.6418 7.14824C66.3753 7.49531 66.0543 7.79684 65.6912 8.04108C65.2585 8.33233 64.7905 8.56731 64.2985 8.74032C65.4803 9.02926 66.3587 9.49446 66.925 10.1388C67.5087 10.8319 67.8117 11.7184 67.7745 12.6237C67.7776 13.3585 67.6239 14.0855 67.3237 14.7561C67.024 15.4239 66.5787 16.0162 66.0206 16.4898C65.399 17.0112 64.6801 17.4041 63.9055 17.6455C62.976 17.9342 62.0066 18.0736 61.0334 18.0587L53.8532 18.0356ZM58.0198 7.63367H60.1926C60.6098 7.63761 61.0265 7.60473 61.438 7.53542C61.7714 7.48358 62.0915 7.36676 62.3799 7.19158C62.6392 7.02764 62.845 6.79168 62.9723 6.51257C63.1193 6.1698 63.1893 5.79893 63.1774 5.42614C63.1874 5.06394 63.1326 4.70287 63.0156 4.35994C62.9182 4.08995 62.747 3.8527 62.5215 3.67515C62.2715 3.49028 61.9832 3.36389 61.6778 3.3053C61.2781 3.22704 60.8714 3.19024 60.4642 3.1955H58.0198V7.63367ZM58.0198 10.422V14.9006H60.967C61.4269 14.9148 61.8855 14.8433 62.3192 14.6897C62.6309 14.5757 62.9111 14.3896 63.1369 14.1464C63.3319 13.933 63.4718 13.6751 63.5444 13.3952C63.6192 13.1173 63.6571 12.8307 63.657 12.5428C63.6613 12.2421 63.6174 11.9426 63.527 11.6558C63.4415 11.3919 63.2833 11.1575 63.0705 10.9796C62.8268 10.7812 62.5428 10.6382 62.2383 10.5607C61.8117 10.4526 61.3723 10.403 60.9323 10.4133L58.0198 10.422Z" fill="#009B79"/>
        <path d="M72.6837 0.0896168C72.7991 0.104834 72.9119 0.13595 73.0188 0.182079C73.1248 0.230848 73.2224 0.296198 73.3078 0.37567C73.4182 0.479313 73.5215 0.590329 73.617 0.707955L82.1437 11.5C82.1119 11.1532 82.0859 10.8181 82.0714 10.4945C82.057 10.1708 82.0454 9.86456 82.0454 9.57562V0.0665013H85.7208V18.0359H83.5508C83.272 18.0435 82.9945 17.9933 82.736 17.8885C82.4799 17.7604 82.2595 17.5707 82.0945 17.3366L73.6227 6.64574C73.6458 6.95779 73.6661 7.26407 73.6834 7.56458C73.7007 7.85352 73.7094 8.14246 73.7094 8.40829V18.0359H70.0341V0.0665013H72.2358C72.3873 0.0654468 72.5388 0.0731649 72.6894 0.0896168" fill="#009B79"/>
        <path d="M105.406 18.0358H102.173C101.856 18.0492 101.543 17.9601 101.28 17.7815C101.053 17.6233 100.882 17.3966 100.792 17.1343L99.7316 14.0021H92.8981L91.8377 17.1343C91.7309 17.405 91.5438 17.6365 91.3015 17.7976C91.0593 17.9588 90.7734 18.0419 90.4825 18.0358H87.2261L94.1954 0.0664062H98.4631L105.406 18.0358ZM93.8487 11.1647H98.7607L97.108 6.25268L96.7324 5.14892C96.5879 4.72706 96.4463 4.2686 96.3076 3.77355C96.1747 4.27631 96.0389 4.74151 95.9002 5.16914C95.7586 5.59678 95.6286 5.96662 95.5044 6.27868L93.8487 11.1647Z" fill="#009B79"/>
        <path d="M10.0783 44.6389C7.41323 44.6358 4.85815 43.5757 2.97364 41.6912C1.08912 39.8067 0.0290599 37.2516 0.026001 34.5865V26.9844C2.62159 28.5698 5.60372 29.4096 8.64517 29.4116H24.3377V21.3818H34.4305C37.1126 21.3818 39.6849 22.4473 41.5814 24.3439C43.478 26.2404 44.5435 28.8127 44.5435 31.4948V44.662L10.0783 44.6389Z" fill="#1C3F93"/>
        <path d="M38.048 10.113V15.2851C37.1138 15.1307 36.169 15.0496 35.2222 15.0424H18.0272V23.0837H8.61628C6.35209 23.0786 4.18042 22.1846 2.56888 20.5942C0.957345 19.0037 0.03484 16.844 0 14.5801L0 0H27.9495C30.6286 -3.93668e-06 33.1983 1.06309 35.0943 2.9559C36.9904 4.84871 38.0579 7.41654 38.0625 10.0957" fill="#009B79"/>
        </g>
        <defs>
        <clipPath id="clip0_284_2665">
        <rect width="211.35" height="45" fill="white"/>
        </clipPath>
        </defs>
        </svg>
        
    </div>
    <div class="actions">
      <button class="icon-btn">
        <svg class="w-5 h-5" fill="none" stroke="#1A3E8D" stroke-width="2"
          viewBox="0 0 24 24" stroke-linecap="round" stroke-linejoin="round">
          <circle cx="11" cy="11" r="8" />
          <line x1="21" y1="21" x2="16.65" y2="16.65" />
        </svg>
      </button>
  
      <!-- Notification -->
      <div class="notification-container">
        <div class="notification-icon-wrapper">
          <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M19 17.5C18.6022 17.5 18.2206 17.342 17.9393 17.0607C17.658 16.7794 17.5 16.3978 17.5 16V11.138C17.5577 9.23909 16.9342 7.38218 15.742 5.903C14.5499 4.42382 12.8678 3.41999 11 3.073V1C11 0.734784 10.8946 0.48043 10.7071 0.292893C10.5196 0.105357 10.2652 0 10 0C9.73478 0 9.48043 0.105357 9.29289 0.292893C9.10536 0.48043 9 0.734784 9 1V3.073C7.13217 3.41999 5.45013 4.42382 4.25798 5.903C3.06583 7.38218 2.44226 9.23909 2.5 11.138V16C2.5 16.3978 4.34196 16.7794 4.06066 17.0607C3.77936 17.342 3.39782 17.5 3 17.5C2.73478 17.5 2.48043 17.6054 2.29289 17.7929C2.10536 17.9804 2 18.2348 2 18.5C2 18.7652 2.10536 19.0196 2.29289 19.2071C2.48043 19.3946 2.73478 19.5 3 19.5H19C19.2652 19.5 19.5196 19.3946 19.7071 19.2071C19.8946 19.0196 20 18.7652 20 18.5C20 18.2348 19.8946 17.9804 19.7071 17.7929C19.5196 17.6054 19.2652 17.5 19 17.5Z" fill="#1C3F93"/>
            </svg>
            
          <div class="notification-badge">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <g clip-path="url(#clip0_284_2659)">
              <path d="M21 17.5C20.6022 17.5 20.2206 17.342 19.9393 17.0607C19.658 16.7794 19.5 16.3978 19.5 16V11.138C19.5577 9.23909 18.9342 7.38218 17.742 5.903C16.5499 4.42382 14.8678 3.41999 13 3.073V1C13 0.734784 12.8946 0.48043 12.7071 0.292893C12.5196 0.105357 12.2652 0 12 0C11.7348 0 11.4804 0.105357 11.2929 0.292893C11.1054 0.48043 11 0.734784 11 1V3.073C9.13217 3.41999 7.45013 4.42382 6.25798 5.903C5.06583 7.38218 4.44226 9.23909 4.5 11.138V16C4.5 16.3978 4.34196 16.7794 4.06066 17.0607C3.77936 17.342 3.39782 17.5 3 17.5C2.73478 17.5 2.48043 17.6054 2.29289 17.7929C2.10536 17.9804 2 18.2348 2 18.5C2 18.7652 2.10536 19.0196 2.29289 19.2071C2.48043 19.3946 2.73478 19.5 3 19.5H21C21.2652 19.5 21.5196 19.3946 21.7071 19.2071C21.8946 19.0196 22 18.7652 22 18.5C22 18.2348 21.8946 17.9804 21.7071 17.7929C21.5196 17.6054 21.2652 17.5 21 17.5Z" fill="#1C3F93"/>
              <path d="M14.236 21H9.76401C9.70263 21.0001 9.64343 21.0228 9.59769 21.0637C9.55196 21.1047 9.52288 21.161 9.51601 21.222C9.50511 21.3143 9.49977 21.4071 9.50001 21.5C9.50001 22.163 9.7634 22.7989 10.2322 23.2678C10.7011 23.7366 11.337 24 12 24C12.663 24 13.2989 23.7366 13.7678 23.2678C14.2366 22.7989 14.5 22.163 14.5 21.5C14.5002 21.4071 14.4949 21.3143 14.484 21.222C14.4776 21.1608 14.4486 21.1042 14.4028 21.0632C14.357 21.0222 14.2975 20.9997 14.236 21Z" fill="#1C3F93"/>
              </g>
              <defs>
              <clipPath id="clip0_284_2659">
              <rect width="24" height="24" fill="white"/>
              </clipPath>
              </defs>
              </svg>
              
          </div>
        </div>
      </div>
  
      <!-- Profile -->
      <div class="profile">
        <div class="vertical-line"></div>
        <div class="profile-circle">a</div>
        <span class="profile-name"><strong>Agent</strong></span>
        
      </div>
    </div>
  </div>
  
  <!-- Bottom Bar -->
  <div class="bottom-bar">
    <strong><span class="bottom-bar-title">Déclaration des sinistres</span></strong>
    <div class="svg">
    <svg width="130" height="20" viewBox="0 0 130 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect width="130" height="20" rx="6" fill="#009B79"/>
      <text x="50%" y="50%" dominant-baseline="middle" text-anchor="middle" fill="white" font-size="12" font-family="Arial, sans-serif">
        Agent
      </text>
    </svg>
  </div>
  </div>

  <div class="form-container">
    <div class="header">
      <strong><h1>Lieu de survenance du sinistre</h1></strong>
    </div>

    <div class="progress-steps">
      <div class="step active">
        <div class="step-number">
          ✓
            </div>
        <strong><div class="step-title active">Ouverture sinistre</div></strong>
      </div>
      <div class="step active">
        <div class="step-number">2</div>
        <strong><div class="step-title active">Lieu de survenance du sinistre</div></strong>
      </div>
      <div class="step">
        <div class="step-number">3</div>
        <strong><div class="step-title">Données conducteur</div></strong>
      </div>
      <div class="step">
        <div class="step-number">4</div>
        <strong><div class="step-title">Données tiers</div></strong>
      </div>
      <div class="step">
        <div class="step-number">5</div>
        <strong><div class="step-title">Documents</div></strong>
      </div>
      <div class="step">
        <div class="step-number">6</div>
        <strong><div class="step-title">Récapitulatif</div></strong>
      </div>
    </div>
  </div>

  <div class="main-form-content">
   <h2 class="section-title" style="color: #232c86; font-size: 1.5rem; margin-bottom: 2rem; font-weight: 600; position:relative; top:13px">Lieu de survenance du sinistre</h2>

    <form [formGroup]="survenanceForm" (ngSubmit)="onSubmit()" class="form-grid">
      <!-- First Row -->
      <div class="form-row">
        <div class="form-group">
          <label>Gouvernorat<span class="required">*</span></label>
          <div class="input-wrapper">
            <select formControlName="gouvernorat">
              <option value="" disabled selected>Gouvernorat</option>
              <option value="tunis">Tunis</option>
              <option value="ariana">Ariana</option>
              <option value="benarous">Ben Arous</option>
              <!-- Add more options as needed -->
            </select>
            <span class="arrow-icon">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path d="M6 9L12 15L18 9" stroke="#1C3F93" stroke-width="1.5"/>
              </svg>
            </span>
          </div>
          <div *ngIf="gouvernorat?.touched && gouvernorat?.invalid" class="error-message">
            Ce champ est obligatoire
          </div>
        </div>

        <div class="form-group">
          <label>Région<span class="required">*</span></label>
          <div class="input-wrapper">
            <select formControlName="region">
              <option value="" disabled selected>Région</option>
              <!-- Add options dynamically based on selected gouvernorat -->
            </select>
            <span class="arrow-icon">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path d="M6 9L12 15L18 9" stroke="#1C3F93" stroke-width="1.5"/>
              </svg>
            </span>
          </div>
          <div *ngIf="region?.touched && region?.invalid" class="error-message">
            Ce champ est obligatoire
          </div>
        </div>
      </div>

      <!-- Second Row -->
      <div class="form-row">
        <div class="form-group">
          <label>Cité<span class="required">*</span></label>
          <div class="input-wrapper">
            <select formControlName="cite">
              <option value="" disabled selected>Cité</option>
              <!-- Add options dynamically based on selected region -->
            </select>
            <span class="arrow-icon">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path d="M6 9L12 15L18 9" stroke="#1C3F93" stroke-width="1.5"/>
              </svg>
            </span>
          </div>
          <div *ngIf="cite?.touched && cite?.invalid" class="error-message">
            Ce champ est obligatoire
          </div>
        </div>

        <div class="form-group">
          <label>Code postal<span class="required">*</span></label>
          <div class="input-wrapper">
            <select formControlName="codePostal">
              <option value="" disabled selected>Code postal</option>
              <!-- Add options dynamically based on selected cite -->
            </select>
            <span class="arrow-icon">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path d="M6 9L12 15L18 9" stroke="#1C3F93" stroke-width="1.5"/>
              </svg>
            </span>
          </div>
          <div *ngIf="codePostal?.touched && codePostal?.invalid" class="error-message">
            Ce champ est obligatoire
          </div>
        </div>
      </div>

      <!-- Third Row - Full Width -->
      <div class="form-row full-width">
        <div class="form-group">
          <label>Adresse<span class="required">*</span></label>
          <input type="text" class="address-input" formControlName="adresse" placeholder="Adresse">
          <div *ngIf="adresse?.touched && adresse?.invalid" class="error-message">
            Ce champ est obligatoire
          </div>
        </div>
      </div>

      <!-- Fourth Row - Toggles -->
      <div class="form-row toggles">
        <div class="toggle-group">
          <label>Véhicule en stationnement ?</label>
          <div class="toggle-container">
            <span>Non</span>
            <div class="toggle-switch" [class.active]="isParking" (click)="toggleParking()">
              <div class="toggle-circle"></div>
            </div>
            <span>Oui</span>
          </div>
        </div>

        <div class="toggle-group">
          <label>Le conducteur est lui-même l'assuré ?</label>
          <div class="toggle-container">
            <span>Non</span>
            <div class="toggle-switch" [class.active]="isDriver" (click)="toggleDriver()">
              <div class="toggle-circle"></div>
            </div>
            <span>Oui</span>
          </div>
        </div>
      </div>

      <div class="footer-action-bar-container">
        <button class="footer-btn footer-btn-retour" aria-label="Retour">
          <span class="footer-btn-icon-left">&larr;</span> Retour
        </button>
        <button class="footer-btn footer-btn-continuer" aria-label="Continuer">
          Continuer <span class="footer-btn-icon-right">&rarr;</span>
        </button>
      </div>
 
    </form>
  </div>

</body>