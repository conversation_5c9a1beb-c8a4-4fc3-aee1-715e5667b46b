/* Global font styles */
* {
    font-family: 'Gilmer Bold', sans-serif;
  }
  
  /* === Base styles === */
  
  body {
    background-color: #fbfafa;
    margin: 0;
    padding: 0;
    width: 100%;
    min-height: 140vh;
    overflow-y: hidden;
    overflow-x: hidden;
  }
  
  .top-navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 1.5rem;
    background-color: white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    flex-wrap: wrap;
  }
  
  img {
    width: 225px;
    max-width: 100%;
  }
  
  .actions {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    flex-wrap: wrap;
  }
  
  .icon-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 0;
  }
  
  .notification-container {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .notification-badge {
    background-color: #1A3E8D;
    color: white;
    border-radius: 9999px;
    height: 16px;
    width: 16px;
    font-size: 0.625rem;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: -6px;
    right: -6px;
  }
  
  .profile {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex-wrap: nowrap;
  }
  
  .profile-circle {
    background-color: #1A3E8D;
    color: white;
    border-radius: 50%;
    height: 2rem;
    width: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    text-transform: lowercase;
    font-size: 1rem;
  }
  
  .profile-name {
    font-family: 'Gilmer Bold', sans-serif;
    margin-left: 10px;
    color: #1C3F93;
  }
  
  .bottom-bar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 30px;
    background: #1A3E8D;
    margin-bottom: 20px;
    position: relative;
    height: 60px;
    border-bottom-right-radius: 48px;
  }
  
  .bottom-bar-title {
    font-family: 'Gilmer Bold', sans-serif;
    font-size: 23px;
    color: white;
    position: relative;
    left: 250px;
    bottom: 10px;
  }
  
  .svg {
    position: relative;
    right: 980px;
    top: 15px;
    max-width: 100%;
    height: auto;
    display: inline-block;
  }
  
  /* Progress Steps */
  .progress-steps {
    width: 250px;
    margin-right: 10px;
    position: relative;
    left: 50px;
    display: block;
    bottom: 40px;
  }
  
  .step {
    position: relative;
    margin-bottom: 30px;
    padding-left: 40px;
  }
  
  .step-number {
    position: absolute;
    left: 0;
    top: 0;
    width: 24px;
    height: 24px;
    background-color: #B9C3DE;
    border-radius: 50%;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: 'Gilmer Bold', sans-serif;
    font-size: 16px;
  }
  
  .step-number svg {
    width: 24px;
    height: 24px;
    position: absolute;
    top: 0;
    left: 0;
  }
  
  .step.active .step-number {
    background-color: transparent;
  }
  
  .step.active .step-number {
    background-color: #1C3F93;
  }
  
  .step-title {
    font-family: 'Gilmer Bold', sans-serif;
    font-size: 16px;
    color: #B9C3DE;
    margin-bottom: 5px;
  }
  
  .step.active .step-title.active {
    color: #1C3F93;
  }
  
  /* Form Container */
  .form-container {
    display: flex;
    max-width: 100%;
    height: 100vh;
    margin: 0;
    padding: 0;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
  }
  
  .form-content {
    width: 900px;
    max-height: 90vh;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    padding: 20px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
  }
  
  /* Form Fields */
  .form-row {
    display: flex;
    gap: 36px;
    margin-bottom: 24px;
  }
  
  .form-group {
    flex: 1;
    position: relative;
  }
  
  .form-group label {
    display: block;
    font-family: 'Gilmer Bold', sans-serif;
    font-size: 14px;
    line-height: 120%;
    margin-bottom: 8px;
    color: #000000;
  }
  
  .input-wrapper {
    position: relative;
  }
  
  .input-wrapper input,
  .input-wrapper select {
    font-family: 'Gilmer Bold', sans-serif;
    width: 100%;
    height: 56px;
    padding: 16px;
    border: 1px solid #E5E7EB;
    border-radius: 0;
    border-top-right-radius: 24px;
    border-bottom-left-radius: 24px;
    font-size: 14px;
    color: #374151;
    background: white;
    appearance: none;
  }
  
  .arrow-icon {
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    pointer-events: none;
  }
  
  .required {
    color: #FF0000;
  }
  
  .error-message {
    font-family: 'Gilmer Bold', sans-serif;
    color: #dc3545;
    font-size: 12px;
    margin-top: 4px;
  }
  
  /* Custom Material Date Picker */
  .custom-form-field {
    width: 398px !important;
  }
  
  ::ng-deep .mat-mdc-form-field {
    width: 398px !important;
  }
  
  ::ng-deep .mat-mdc-text-field-wrapper {
    width: 398px !important;
    background-color: white !important;
    padding: 0 !important;
    height: 56px !important;
  }
  
  ::ng-deep .mat-mdc-form-field-flex {
    padding: 0 16px !important;
    height: 56px !important;
    border: 1px solid #E5E7EB !important;
    border-radius: 0 !important;
    border-top-right-radius: 24px !important;
    border-bottom-left-radius: 24px !important;
    width: 398px !important;
  }
  
  ::ng-deep .mat-mdc-form-field-infix {
    padding: 16px 0 !important;
    width: 100% !important;
  }
  
  ::ng-deep .mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline__leading,
  ::ng-deep .mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline__notch,
  ::ng-deep .mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline__trailing {
    border-color: transparent !important;
  }
  
  ::ng-deep .mat-mdc-form-field-subscript-wrapper {
    display: none;
  }
  
  ::ng-deep #input-hey {
    width: 398px !important;
  }
  
  ::ng-deep #input-hey .mat-mdc-form-field-flex {
    width: 398px !important;
    height: 56px !important;
  }
  
  ::ng-deep #input-hey .mat-mdc-form-field-infix {
    height: 56px !important;
    display: flex;
    align-items: center;
  }
  
  ::ng-deep #input-hey input.mat-mdc-input-element {
    height: 56px !important;
    box-sizing: border-box;
  }
  
  .main-title {
    font-family: 'Gilmer Bold', sans-serif;
    font-size: 24px;
    color: #000000;
    margin: 30px 0 20px 50px;
    position: relative;
    left: 280px;
    bottom: 15px;
  }
  
  /* Footer Action Bar */
  .footer-action-bar-container {
    width: 880px;
    max-width: 880px;
    min-height: 92px;
    background: #fff;
    border-radius: 24px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 24px;
    margin-top: 2px;
    box-shadow: 0 2px 8px #F4F4F4;
    padding: 0 32px 0 0;
    position: relative;
    right: 15px;
    top: 50px;
    border-bottom-right-radius: 16px;
    border-bottom-left-radius: 16px;
    background: var(--white, #FFFFFF);
  }
  
  @media (max-width: 900px) {
    .footer-action-bar-container {
        width: 98vw;
        min-height: auto;
        border-radius: 18px;
        padding: 0 8px;
        justify-content: center;
    }
    .footer-btn {
        width: 90vw;
        justify-content: center;
    }
  }
  
  .footer-btn {
    font-family: 'Gilmer Bold', sans-serif;
    font-size: 18px;
    border: none;
    outline: none;
    background: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    transition: color 0.2s, background 0.2s;
    height: 48px;
    padding: 0 16px;
    margin: 0;
  }
  
  .footer-btn-retour {
    color: #1C3F93;
    background: none;
    border-radius: 0 24px 24px 0;
    font-weight: 600;
  }
  
  .footer-btn-retour:hover {
    text-decoration: underline;
    background: #F4F4F4;
  }
  
  .footer-btn-continuer {
    color: #fff;
    background: #00A887;
    width: 135px;
    height: 48px;
    border-top-right-radius: 24px;
    border-bottom-left-radius: 24px;
    border-top-left-radius: 0;
    border-bottom-right-radius: 0;
    font-weight: 600;
  }
  
  .footer-btn-continuer:hover {
    background: #00916f;
  }
  
  .footer-btn-icon-left {
    font-size: 22px;
    margin-right: 6px;
    line-height: 1;
  }
  
  .footer-btn-icon-right {
    font-size: 22px;
    margin-left: 6px;
    line-height: 1;
  }
  
  @media (max-width: 480px) {
    .footer-action-bar-container {
        padding: 12px;
    }
  
    .footer-btn {
        padding: 8px 12px;
        font-size: 14px;
    }
  }
  
  @media (max-width: 768px) {
    .header h1 {
        font-size: 24px;
    }
    .header {
        margin-bottom: 20px;
    }
  }
  
  .header h1 {
    font-family: 'Gilmer Bold', sans-serif;
    font-size: 25px;
    color: #0A1633;
    position: relative;
    left: 300px;
    bottom: 20px;
  }
  
  .document-upload {
    position: relative;
    width: 100%;
  }
  
  .file-input {
    position: absolute;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
    z-index: 2;
  }
  
  .upload-placeholder {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    background-color: white;
    border: 1px solid #E5E7EB;
    border-radius: 0;
    border-top-right-radius: 24px;
    border-bottom-left-radius: 24px;
    color: #6B7280;
    font-size: 14px;
    min-height: 56px;
  }
  
  .upload-placeholder svg {
    flex-shrink: 0;
  }
  
  .upload-placeholder span {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  
  .file-preview {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    background-color: #F9FAFB;
    border: 1px solid #E5E7EB;
    border-radius: 0;
    border-top-right-radius: 24px;
    border-bottom-left-radius: 24px;
  }
  
  .file-preview .file-name {
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #1C3F93;
    font-weight: 500;
  }
  
  .file-preview .remove-file {
    background: none;
    border: none;
    padding: 4px;
    cursor: pointer;
    color: #EF4444;
  }