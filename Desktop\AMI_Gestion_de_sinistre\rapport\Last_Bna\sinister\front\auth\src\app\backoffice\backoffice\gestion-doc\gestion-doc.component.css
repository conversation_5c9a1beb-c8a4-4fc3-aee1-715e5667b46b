/* Container and Layout */
.container {
  padding: 24px;
  height: 100vh;
  min-height: 500px;
  display: flex;
  flex-direction: column;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  overflow: hidden;
}

/* Loading Overlay */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.spinner {
  border: 5px solid #f3f3f3;
  border-top: 5px solid #3498db;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Alert Messages */
.alert {
  padding: 15px;
  margin-bottom: 20px;
  border-radius: 4px;
  position: relative;
}

.alert-success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.alert-danger {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.close {
  position: absolute;
  top: 0;
  right: 0;
  padding: 15px;
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
}

/* Header */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-shrink: 0;
}

h2 {
  color: #333;
  font-size: 24px;
  margin: 0;
}

/* Buttons */
.create-btn {
  background-color: #1C3F93;
  color: white;
  border: none;
  padding: 10px 15px;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  transition: background-color 0.2s;
}

.create-btn:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.create-btn:hover:not(:disabled) {
  background-color: #142f75;
}

/* Table Styles */
.table-container {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  height: 600px;
  flex: 1;
}

.table-responsive {
  flex: 1;
  overflow-x: auto;
  overflow-y: auto;
  min-height: 0;
  height: 100%;
}

/* Styles pour le défilement du tableau */
.table-responsive::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.table-responsive::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb:hover {
  background: #555;
}

table {
  width: 100%;
  border-collapse: collapse;
}

th, td {
  padding: 15px;
  text-align: left;
  border-bottom: 1px solid #e0e0e0;
}

th {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #333;
  position: sticky;
  top: 0;
  z-index: 1;
}

tbody tr:hover {
  background-color: #f5f5f5;
}

/* Document List */
td ul {
  margin: 0;
  padding: 0;
  list-style-type: none;
}

td ul li {
  padding: 5px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.add-doc-btn {
  background: none;
  border: none;
  color: #1C3F93;
  cursor: pointer;
  font-weight: 500;
  margin-top: 8px;
  padding: 5px;
  transition: color 0.2s;
}

.add-doc-btn:hover {
  text-decoration: underline;
  color: #142f75;
}

/* Document Items */
.documents-cell {
  max-width: 300px;
}

.document-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  margin: 4px 0;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.document-name {
  font-weight: 500;
  color: #333;
  flex: 1;
  margin-right: 8px;
}

.delete-doc-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.delete-doc-btn:hover {
  background-color: rgba(255, 75, 85, 0.1);
}

.no-documents {
  color: #6c757d;
  font-style: italic;
}

/* Action Buttons */
.actions {
  display: flex;
  gap: 10px;
}

.icon-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 5px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.icon-btn.edit:hover {
  background-color: rgba(28, 63, 147, 0.1);
}

.icon-btn.delete:hover {
  background-color: rgba(255, 75, 85, 0.1);
}

/* Modal Styles */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1;
}

.modal-content {
  background: white;
  border-radius: 8px;
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  padding: 24px;
  z-index: 2;
  position: relative;
  animation: modalFadeIn 0.3s ease;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  margin: 20px;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.modal-header h3 {
  font-family: 'Gilmer Bold', sans-serif;
  color: #1C3F93;
  margin: 0;
}

.close-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: #F3F4F6;
  transform: scale(1.1);
}

.close-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.close-btn:hover {
  color: #333;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
}

.modal-body {
  padding: 20px;
}

/* Form Styles - Simplified */
.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: bold;
  color: #333;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 12px;
  border: 2px solid #ccc;
  border-radius: 5px;
  font-size: 16px;
  background: white;
  color: #333;
  box-sizing: border-box;
}

.form-group select {
  cursor: pointer;
}

.form-group input:focus {
  border-color: #1C3F93;
  outline: none;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #1C3F93;
  box-shadow: 0 0 0 2px rgba(28, 63, 147, 0.1);
}

.form-group input.invalid,
.form-group select.invalid {
  border-color: #ff4b55;
}

.error-message {
  color: #ff4b55;
  font-size: 12px;
  margin-top: 5px;
  animation: shake 0.4s;
}

.required {
  color: #ff4b55;
}

/* Checkbox Styles */
.checkbox-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-weight: normal;
}

.checkbox-label input[type="checkbox"] {
  width: auto;
  margin-right: 8px;
  transform: scale(1.2);
}

.checkmark {
  margin-left: 4px;
}

/* Form Styles */
.form-group {
  margin-bottom: 24px;
}

.form-group label {
  display: block;
  font-family: 'Gilmer Bold', sans-serif;
  font-size: 14px;
  color: #6B7280;
  margin-bottom: 8px;
}

.input-wrapper {
  position: relative;
}

.input-wrapper input,
.input-wrapper textarea {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #E5E7EB;
  border-radius: 8px;
  font-family: 'Gilmer Regular', sans-serif;
  font-size: 14px;
  color: #374151;
  transition: all 0.2s ease;
  box-sizing: border-box;
}

.input-wrapper textarea {
  min-height: 100px;
  resize: vertical;
}

.input-wrapper input:focus,
.input-wrapper textarea:focus {
  outline: none;
  border-color: #1C3F93;
  box-shadow: 0 0 0 3px rgba(28, 63, 147, 0.1);
}

.required {
  color: #FF4B55;
}

.error-message {
  color: #FF4B55;
  font-size: 12px;
  margin-top: 4px;
  animation: shake 0.4s ease;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-4px); }
  75% { transform: translateX(4px); }
}

/* Modal Footer */
.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  margin-top: 24px;
}

.btn-secondary {
  background: white;
  border: 1px solid #E5E7EB;
  color: #6B7280;
  padding: 12px 24px;
  border-radius: 24px;
  cursor: pointer;
  font-family: 'Gilmer Bold', sans-serif;
  transition: all 0.2s ease;
}

.btn-primary {
  background: #1C3F93;
  border: none;
  color: white;
  padding: 12px 24px;
  border-radius: 24px;
  cursor: pointer;
  font-family: 'Gilmer Bold', sans-serif;
  transition: all 0.2s ease;
}

.btn-primary:disabled {
  background: #E5E7EB;
  cursor: not-allowed;
}

.btn-secondary:hover {
  background: #F9FAFB;
  border-color: #D1D5DB;
}

.btn-primary:hover:not(:disabled) {
  background: #152D69;
  transform: translateY(-1px);
}

.spinner-btn {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s ease-in-out infinite;
  margin-right: 8px;
  vertical-align: middle;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Alerts */
.alert {
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.alert-success {
  background-color: #ECFDF5;
  color: #065F46;
  border: 1px solid #A7F3D0;
}

.alert-danger {
  background-color: #FEF2F2;
  color: #B91C1C;
  border: 1px solid #FECACA;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

/* Form Actions - Simplified */
.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.cancel-btn {
  background: #ccc;
  color: #333;
  border: none;
  padding: 12px 20px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 14px;
}

.cancel-btn:hover {
  background: #bbb;
}

.submit-btn {
  background: #1C3F93;
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 14px;
}

.submit-btn:disabled {
  background: #999;
  cursor: not-allowed;
}

.submit-btn:hover:not(:disabled) {
  background: #142f75;
}

/* File Input */
input[type="file"] {
  padding: 8px;
  border: 1px dashed #ddd;
  border-radius: 4px;
  width: 100%;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .modal {
    width: 95vw;
    max-width: none;
  }
}