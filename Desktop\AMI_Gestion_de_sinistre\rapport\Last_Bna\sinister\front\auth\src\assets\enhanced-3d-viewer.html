<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Visualisation 3D</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            overflow: hidden;
            color: white;
        }
        
        .container { 
            width: 100vw; 
            height: 100vh; 
            position: relative;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            background: rgba(0,0,0,0.8);
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            backdrop-filter: blur(10px);
            z-index: 1000;
        }
        
        .header h1 {
            font-size: 24px;
            font-weight: 600;
        }
        
        .actions {
            display: flex;
            gap: 10px;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-primary { background: #007bff; color: white; }
        .btn-primary:hover { background: #0056b3; }
        .btn-secondary { background: #6c757d; color: white; }
        .btn-secondary:hover { background: #545b62; }
        .btn:disabled { opacity: 0.5; cursor: not-allowed; }
        
        .viewer-area {
            flex: 1;
            position: relative;
            background: #1a1a1a;
        }
        
        #threejs-container {
            width: 100%;
            height: 100%;
            position: absolute;
            top: 0;
            left: 0;
        }
        
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 100;
        }
        
        .spinner {
            width: 50px;
            height: 50px;
            border: 4px solid #333;
            border-top: 4px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .status-panel {
            position: absolute;
            bottom: 20px;
            left: 20px;
            background: rgba(0,0,0,0.8);
            padding: 15px;
            border-radius: 8px;
            backdrop-filter: blur(10px);
            min-width: 300px;
        }
        
        .status-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }
        
        .status-row:last-child {
            margin-bottom: 0;
        }
        
        .error-message {
            background: #dc3545;
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 20px;
            text-align: center;
        }
        
        .controls-help {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(0,0,0,0.8);
            padding: 15px;
            border-radius: 8px;
            backdrop-filter: blur(10px);
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>🚗 Visualisation 3D - CSM.ai</h1>
            <div class="actions">
                <button id="downloadBtn" class="btn btn-primary" disabled>
                    ⬇️ Télécharger GLB
                </button>
                <button onclick="window.close()" class="btn btn-secondary">
                    ✖️ Fermer
                </button>
            </div>
        </div>

        <!-- 3D Viewer Area -->
        <div class="viewer-area">
            <div id="threejs-container"></div>
            
            <!-- Loading Overlay -->
            <div id="loadingOverlay" class="loading-overlay">
                <div class="spinner"></div>
                <div id="loadingMessage">Vérification de la génération 3D...</div>
                <div style="margin-top: 15px; font-size: 14px; opacity: 0.8;">
                    <div id="progressDetails">Connexion à l'API CSM.ai...</div>
                    <div style="margin-top: 10px;">
                        <div style="background: rgba(255,255,255,0.2); height: 4px; border-radius: 2px; overflow: hidden;">
                            <div id="progressBar" style="background: #007bff; height: 100%; width: 10%; transition: width 0.3s ease;"></div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Error Message -->
            <div id="errorMessage" class="error-message" style="display: none;">
                Erreur lors du chargement du modèle 3D
            </div>
            
            <!-- Status Panel -->
            <div class="status-panel">
                <div class="status-row">
                    <span>Session ID:</span>
                    <span id="sessionId">-</span>
                </div>
                <div class="status-row">
                    <span>Service:</span>
                    <span>🎯 CSM.ai API</span>
                </div>
                <div class="status-row">
                    <span>Format:</span>
                    <span>GLB</span>
                </div>
                <div class="status-row">
                    <span>Statut:</span>
                    <span id="modelStatus">Chargement...</span>
                </div>
            </div>
            
            <!-- Controls Help -->
            <div class="controls-help">
                <strong>🎮 Contrôles:</strong><br>
                • Clic gauche + glisser: Rotation<br>
                • Molette: Zoom<br>
                • Clic droit + glisser: Panoramique
            </div>
        </div>
    </div>

    <!-- Three.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/controls/OrbitControls.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/loaders/GLTFLoader.js"></script>

    <script>
        // Get session ID from URL
        const urlParams = new URLSearchParams(window.location.search);
        const sessionId = urlParams.get('sessionId') || 'demo-session';
        document.getElementById('sessionId').textContent = sessionId;

        // Three.js setup
        let scene, camera, renderer, controls;
        let currentModel = null;
        let modelUrl = null;

        function updateProgress(message, details, percentage) {
            document.getElementById('loadingMessage').textContent = message;
            if (document.getElementById('progressDetails')) {
                document.getElementById('progressDetails').textContent = details;
            }
            if (document.getElementById('progressBar')) {
                document.getElementById('progressBar').style.width = percentage + '%';
            }
        }

        function initThreeJS() {
            const container = document.getElementById('threejs-container');
            
            // Scene
            scene = new THREE.Scene();
            scene.background = new THREE.Color(0x222222);
            
            // Camera
            camera = new THREE.PerspectiveCamera(75, container.clientWidth / container.clientHeight, 0.1, 1000);
            camera.position.set(5, 5, 5);
            
            // Renderer
            renderer = new THREE.WebGLRenderer({ antialias: true });
            renderer.setSize(container.clientWidth, container.clientHeight);
            renderer.shadowMap.enabled = true;
            renderer.shadowMap.type = THREE.PCFSoftShadowMap;
            container.appendChild(renderer.domElement);
            
            // Controls
            controls = new THREE.OrbitControls(camera, renderer.domElement);
            controls.enableDamping = true;
            controls.dampingFactor = 0.05;
            
            // Lighting
            const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
            scene.add(ambientLight);
            
            const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
            directionalLight.position.set(10, 10, 5);
            directionalLight.castShadow = true;
            scene.add(directionalLight);
            
            // Grid
            const gridHelper = new THREE.GridHelper(10, 10);
            scene.add(gridHelper);
            
            // Animation loop
            animate();
            
            console.log('✅ Three.js initialized');
        }

        function animate() {
            requestAnimationFrame(animate);
            controls.update();
            renderer.render(scene, camera);
        }

        function loadModel(url) {
            console.log('🎯 Loading 3D model from:', url);
            document.getElementById('loadingMessage').textContent = 'Chargement du modèle 3D...';
            
            const loader = new THREE.GLTFLoader();
            
            loader.load(
                url,
                function(gltf) {
                    console.log('✅ Model loaded successfully');
                    
                    // Remove previous model
                    if (currentModel) {
                        scene.remove(currentModel);
                    }
                    
                    currentModel = gltf.scene;
                    
                    // Scale and position the model
                    const box = new THREE.Box3().setFromObject(currentModel);
                    const center = box.getCenter(new THREE.Vector3());
                    const size = box.getSize(new THREE.Vector3());
                    
                    const maxDim = Math.max(size.x, size.y, size.z);
                    const scale = 3 / maxDim;
                    currentModel.scale.setScalar(scale);
                    
                    currentModel.position.sub(center.multiplyScalar(scale));
                    
                    scene.add(currentModel);
                    
                    // Hide loading overlay
                    document.getElementById('loadingOverlay').style.display = 'none';
                    document.getElementById('modelStatus').textContent = 'Modèle chargé';
                    document.getElementById('downloadBtn').disabled = false;
                    
                    console.log('✅ Model added to scene');
                },
                function(progress) {
                    const percent = Math.round((progress.loaded / progress.total) * 100);
                    document.getElementById('loadingMessage').textContent = `Chargement... ${percent}%`;
                },
                function(error) {
                    console.error('❌ Error loading model:', error);
                    document.getElementById('loadingOverlay').style.display = 'none';
                    document.getElementById('errorMessage').style.display = 'block';
                    document.getElementById('errorMessage').textContent = 'Erreur lors du chargement du modèle 3D';
                    document.getElementById('modelStatus').textContent = 'Erreur';
                }
            );
        }

        // Load session data and model
        async function loadSessionData() {
            console.log('🔍 Loading session data for:', sessionId);

            try {
                // For demo sessions, use demo model immediately
                if (sessionId.startsWith('SESSION_demo_')) {
                    console.log('🎭 Demo session detected, loading demo model');
                    updateProgress('Mode démonstration', 'Chargement du modèle de démo...', 90);
                    modelUrl = 'https://threejs.org/examples/models/gltf/DamagedHelmet/glTF/DamagedHelmet.gltf';
                    loadModel(modelUrl);
                    return;
                }

                console.log('🚀 Real session detected, checking CSM.ai API...');
                updateProgress('Vérification du statut', 'Connexion à l\'API CSM.ai...', 20);

                // For real sessions, try both proxy and direct API
                let response;
                try {
                    // Try proxy first
                    response = await fetch(`/api/csm/sessions/${sessionId}`, {
                        headers: {
                            'x-api-key': '793308e392807ce9d1Ad41F3b41EafDF',
                            'Content-Type': 'application/json'
                        }
                    });
                } catch (proxyError) {
                    console.warn('⚠️ Proxy failed, trying direct API:', proxyError);
                    // Try direct API
                    response = await fetch(`https://api.csm.ai/v3/sessions/${sessionId}`, {
                        headers: {
                            'x-api-key': '793308e392807ce9d1Ad41F3b41EafDF',
                            'Content-Type': 'application/json'
                        }
                    });
                }

                if (response.ok) {
                    const data = await response.json();
                    console.log('📊 Session data:', data);
                    console.log('📊 Session status:', data.status);

                    if (data.status === 'complete') {
                        if (data.output?.meshes?.[0]?.data?.glb_url) {
                            modelUrl = data.output.meshes[0].data.glb_url;
                            console.log('🎉 REAL 3D MODEL URL FOUND:', modelUrl);
                            updateProgress('Modèle 3D généré !', 'Chargement du modèle de votre image...', 95);
                            loadModel(modelUrl);
                        } else {
                            console.warn('⚠️ Session complete but no model URL');
                            updateProgress('Modèle généré', 'URL non disponible, chargement du modèle de démo...', 80);
                            // Fallback to demo
                            setTimeout(() => {
                                modelUrl = 'https://threejs.org/examples/models/gltf/DamagedHelmet/glTF/DamagedHelmet.gltf';
                                loadModel(modelUrl);
                            }, 2000);
                        }
                    } else if (data.status === 'failed') {
                        console.error('❌ 3D generation failed');
                        document.getElementById('loadingMessage').textContent = 'Génération échouée, chargement du modèle de démo...';
                        setTimeout(() => {
                            modelUrl = 'https://threejs.org/examples/models/gltf/DamagedHelmet/glTF/DamagedHelmet.gltf';
                            loadModel(modelUrl);
                        }, 2000);
                    } else {
                        // Still processing
                        const statusMessages = {
                            'incomplete': 'Génération en cours...',
                            'processing': 'Traitement de l\'image...',
                            'queued': 'En file d\'attente...'
                        };
                        const message = statusMessages[data.status] || `Statut: ${data.status}`;
                        document.getElementById('loadingMessage').textContent = message;
                        document.getElementById('modelStatus').textContent = data.status;

                        console.log(`🔄 Still processing (${data.status}), polling again in 10 seconds...`);
                        // Poll again in 10 seconds
                        setTimeout(loadSessionData, 10000);
                    }
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

            } catch (error) {
                console.error('❌ Error loading session data:', error);
                console.log('🎭 Falling back to demo model');
                document.getElementById('loadingMessage').textContent = 'Erreur API, chargement du modèle de démo...';
                setTimeout(() => {
                    modelUrl = 'https://threejs.org/examples/models/gltf/DamagedHelmet/glTF/DamagedHelmet.gltf';
                    loadModel(modelUrl);
                }, 2000);
            }
        }

        // Download model
        document.getElementById('downloadBtn').addEventListener('click', function() {
            if (modelUrl) {
                const link = document.createElement('a');
                link.href = modelUrl;
                link.download = `vehicle-3d-model-${sessionId}.gltf`;
                link.click();
                
                console.log('📥 Download initiated for:', modelUrl);
            }
        });

        // Handle window resize
        window.addEventListener('resize', function() {
            const container = document.getElementById('threejs-container');
            camera.aspect = container.clientWidth / container.clientHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(container.clientWidth, container.clientHeight);
        });

        // Initialize everything
        console.log('🚀 Initializing Enhanced 3D Viewer');
        console.log('📋 Session ID:', sessionId);
        
        initThreeJS();
        loadSessionData();
    </script>
</body>
</html>
