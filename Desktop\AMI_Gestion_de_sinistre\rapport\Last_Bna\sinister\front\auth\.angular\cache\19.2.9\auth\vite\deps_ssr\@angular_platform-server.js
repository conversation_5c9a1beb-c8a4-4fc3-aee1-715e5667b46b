import { createRequire } from 'module';const require = createRequire(import.meta.url);
import {
  BEFORE_APP_SERIALIZED,
  ENABLE_DOM_EMULATION,
  INITIAL_CONFIG,
  INTERNAL_SERVER_PLATFORM_PROVIDERS,
  PlatformState,
  SERVER_CONTEXT,
  SERVER_RENDER_PROVIDERS,
  ServerModule,
  VERSION,
  platformServer,
  provideServerRendering,
  renderApplication,
  renderInternal,
  renderModule
} from "./chunk-CQIFWVIF.js";
import "./chunk-H7JTONW6.js";
import "./chunk-G7TVHKQS.js";
import "./chunk-KXIDSWWM.js";
import "./chunk-YRPTRWJS.js";
import "./chunk-4UBR7WAK.js";
import "./chunk-ZUJ64LXG.js";
import "./chunk-XCIYP5SE.js";
import "./chunk-OYTRG5F6.js";
import "./chunk-YHCV7DAQ.js";
export {
  BEFORE_APP_SERIALIZED,
  INITIAL_CONFIG,
  PlatformState,
  ServerModule,
  VERSION,
  platformServer,
  provideServerRendering,
  renderApplication,
  renderModule,
  ENABLE_DOM_EMULATION as ɵENABLE_DOM_EMULATION,
  INTERNAL_SERVER_PLATFORM_PROVIDERS as ɵINTERNAL_SERVER_PLATFORM_PROVIDERS,
  SERVER_CONTEXT as ɵSERVER_CONTEXT,
  SERVER_RENDER_PROVIDERS as ɵSERVER_RENDER_PROVIDERS,
  renderInternal as ɵrenderInternal
};
