{"version": 3, "sources": ["../../../../../../node_modules/three/examples/jsm/controls/OrbitControls.js"], "sourcesContent": ["import { Controls, MOUSE, Quaternion, Spherical, TOUCH, Vector2, Vector3, <PERSON><PERSON>, <PERSON>, MathUtils } from 'three';\n\n/**\n * Fires when the camera has been transformed by the controls.\n *\n * @event OrbitControls#change\n * @type {Object}\n */\nconst _changeEvent = {\n  type: 'change'\n};\n\n/**\n * Fires when an interaction was initiated.\n *\n * @event OrbitControls#start\n * @type {Object}\n */\nconst _startEvent = {\n  type: 'start'\n};\n\n/**\n * Fires when an interaction has finished.\n *\n * @event OrbitControls#end\n * @type {Object}\n */\nconst _endEvent = {\n  type: 'end'\n};\nconst _ray = new Ray();\nconst _plane = new Plane();\nconst _TILT_LIMIT = Math.cos(70 * MathUtils.DEG2RAD);\nconst _v = new Vector3();\nconst _twoPI = 2 * Math.PI;\nconst _STATE = {\n  NONE: -1,\n  ROTATE: 0,\n  DOLLY: 1,\n  PAN: 2,\n  TOUCH_ROTATE: 3,\n  TOUCH_PAN: 4,\n  TOUCH_DOLLY_PAN: 5,\n  TOUCH_DOLLY_ROTATE: 6\n};\nconst _EPS = 0.000001;\n\n/**\n * Orbit controls allow the camera to orbit around a target.\n *\n * OrbitControls performs orbiting, dollying (zooming), and panning. Unlike {@link TrackballControls},\n * it maintains the \"up\" direction `object.up` (+Y by default).\n *\n * - Orbit: Left mouse / touch: one-finger move.\n * - Zoom: Middle mouse, or mousewheel / touch: two-finger spread or squish.\n * - Pan: Right mouse, or left mouse + ctrl/meta/shiftKey, or arrow keys / touch: two-finger move.\n *\n * ```js\n * const controls = new OrbitControls( camera, renderer.domElement );\n *\n * // controls.update() must be called after any manual changes to the camera's transform\n * camera.position.set( 0, 20, 100 );\n * controls.update();\n *\n * function animate() {\n *\n * \t// required if controls.enableDamping or controls.autoRotate are set to true\n * \tcontrols.update();\n *\n * \trenderer.render( scene, camera );\n *\n * }\n * ```\n *\n * @augments Controls\n */\nclass OrbitControls extends Controls {\n  /**\n   * Constructs a new controls instance.\n   *\n   * @param {Object3D} object - The object that is managed by the controls.\n   * @param {?HTMLDOMElement} domElement - The HTML element used for event listeners.\n   */\n  constructor(object, domElement = null) {\n    super(object, domElement);\n    this.state = _STATE.NONE;\n\n    /**\n     * The focus point of the controls, the `object` orbits around this.\n     * It can be updated manually at any point to change the focus of the controls.\n     *\n     * @type {Vector3}\n     */\n    this.target = new Vector3();\n\n    /**\n     * The focus point of the `minTargetRadius` and `maxTargetRadius` limits.\n     * It can be updated manually at any point to change the center of interest\n     * for the `target`.\n     *\n     * @type {Vector3}\n     */\n    this.cursor = new Vector3();\n\n    /**\n     * How far you can dolly in (perspective camera only).\n     *\n     * @type {number}\n     * @default 0\n     */\n    this.minDistance = 0;\n\n    /**\n     * How far you can dolly out (perspective camera only).\n     *\n     * @type {number}\n     * @default Infinity\n     */\n    this.maxDistance = Infinity;\n\n    /**\n     * How far you can zoom in (orthographic camera only).\n     *\n     * @type {number}\n     * @default 0\n     */\n    this.minZoom = 0;\n\n    /**\n     * How far you can zoom out (orthographic camera only).\n     *\n     * @type {number}\n     * @default Infinity\n     */\n    this.maxZoom = Infinity;\n\n    /**\n     * How close you can get the target to the 3D `cursor`.\n     *\n     * @type {number}\n     * @default 0\n     */\n    this.minTargetRadius = 0;\n\n    /**\n     * How far you can move the target from the 3D `cursor`.\n     *\n     * @type {number}\n     * @default Infinity\n     */\n    this.maxTargetRadius = Infinity;\n\n    /**\n     * How far you can orbit vertically, lower limit. Range is `[0, Math.PI]` radians.\n     *\n     * @type {number}\n     * @default 0\n     */\n    this.minPolarAngle = 0;\n\n    /**\n     * How far you can orbit vertically, upper limit. Range is `[0, Math.PI]` radians.\n     *\n     * @type {number}\n     * @default Math.PI\n     */\n    this.maxPolarAngle = Math.PI;\n\n    /**\n     * How far you can orbit horizontally, lower limit. If set, the interval `[ min, max ]`\n     * must be a sub-interval of `[ - 2 PI, 2 PI ]`, with `( max - min < 2 PI )`.\n     *\n     * @type {number}\n     * @default -Infinity\n     */\n    this.minAzimuthAngle = -Infinity;\n\n    /**\n     * How far you can orbit horizontally, upper limit. If set, the interval `[ min, max ]`\n     * must be a sub-interval of `[ - 2 PI, 2 PI ]`, with `( max - min < 2 PI )`.\n     *\n     * @type {number}\n     * @default -Infinity\n     */\n    this.maxAzimuthAngle = Infinity;\n\n    /**\n     * Set to `true` to enable damping (inertia), which can be used to give a sense of weight\n     * to the controls. Note that if this is enabled, you must call `update()` in your animation\n     * loop.\n     *\n     * @type {boolean}\n     * @default false\n     */\n    this.enableDamping = false;\n\n    /**\n     * The damping inertia used if `enableDamping` is set to `true`.\n     *\n     * Note that for this to work, you must call `update()` in your animation loop.\n     *\n     * @type {number}\n     * @default 0.05\n     */\n    this.dampingFactor = 0.05;\n\n    /**\n     * Enable or disable zooming (dollying) of the camera.\n     *\n     * @type {boolean}\n     * @default true\n     */\n    this.enableZoom = true;\n\n    /**\n     * Speed of zooming / dollying.\n     *\n     * @type {number}\n     * @default 1\n     */\n    this.zoomSpeed = 1.0;\n\n    /**\n     * Enable or disable horizontal and vertical rotation of the camera.\n     *\n     * Note that it is possible to disable a single axis by setting the min and max of the\n     * `minPolarAngle` or `minAzimuthAngle` to the same value, which will cause the vertical\n     * or horizontal rotation to be fixed at that value.\n     *\n     * @type {boolean}\n     * @default true\n     */\n    this.enableRotate = true;\n\n    /**\n     * Speed of rotation.\n     *\n     * @type {number}\n     * @default 1\n     */\n    this.rotateSpeed = 1.0;\n\n    /**\n     * How fast to rotate the camera when the keyboard is used.\n     *\n     * @type {number}\n     * @default 1\n     */\n    this.keyRotateSpeed = 1.0;\n\n    /**\n     * Enable or disable camera panning.\n     *\n     * @type {boolean}\n     * @default true\n     */\n    this.enablePan = true;\n\n    /**\n     * Speed of panning.\n     *\n     * @type {number}\n     * @default 1\n     */\n    this.panSpeed = 1.0;\n\n    /**\n     * Defines how the camera's position is translated when panning. If `true`, the camera pans\n     * in screen space. Otherwise, the camera pans in the plane orthogonal to the camera's up\n     * direction.\n     *\n     * @type {boolean}\n     * @default true\n     */\n    this.screenSpacePanning = true;\n\n    /**\n     * How fast to pan the camera when the keyboard is used in\n     * pixels per keypress.\n     *\n     * @type {number}\n     * @default 7\n     */\n    this.keyPanSpeed = 7.0;\n\n    /**\n     * Setting this property to `true` allows to zoom to the cursor's position.\n     *\n     * @type {boolean}\n     * @default false\n     */\n    this.zoomToCursor = false;\n\n    /**\n     * Set to true to automatically rotate around the target\n     *\n     * Note that if this is enabled, you must call `update()` in your animation loop.\n     * If you want the auto-rotate speed to be independent of the frame rate (the refresh\n     * rate of the display), you must pass the time `deltaTime`, in seconds, to `update()`.\n     *\n     * @type {boolean}\n     * @default false\n     */\n    this.autoRotate = false;\n\n    /**\n     * How fast to rotate around the target if `autoRotate` is `true`. The default  equates to 30 seconds\n     * per orbit at 60fps.\n     *\n     * Note that if `autoRotate` is enabled, you must call `update()` in your animation loop.\n     *\n     * @type {number}\n     * @default 2\n     */\n    this.autoRotateSpeed = 2.0;\n\n    /**\n     * This object contains references to the keycodes for controlling camera panning.\n     *\n     * ```js\n     * controls.keys = {\n     * \tLEFT: 'ArrowLeft', //left arrow\n     * \tUP: 'ArrowUp', // up arrow\n     * \tRIGHT: 'ArrowRight', // right arrow\n     * \tBOTTOM: 'ArrowDown' // down arrow\n     * }\n     * ```\n     * @type {Object}\n     */\n    this.keys = {\n      LEFT: 'ArrowLeft',\n      UP: 'ArrowUp',\n      RIGHT: 'ArrowRight',\n      BOTTOM: 'ArrowDown'\n    };\n\n    /**\n     * This object contains references to the mouse actions used by the controls.\n     *\n     * ```js\n     * controls.mouseButtons = {\n     * \tLEFT: THREE.MOUSE.ROTATE,\n     * \tMIDDLE: THREE.MOUSE.DOLLY,\n     * \tRIGHT: THREE.MOUSE.PAN\n     * }\n     * ```\n     * @type {Object}\n     */\n    this.mouseButtons = {\n      LEFT: MOUSE.ROTATE,\n      MIDDLE: MOUSE.DOLLY,\n      RIGHT: MOUSE.PAN\n    };\n\n    /**\n     * This object contains references to the touch actions used by the controls.\n     *\n     * ```js\n     * controls.mouseButtons = {\n     * \tONE: THREE.TOUCH.ROTATE,\n     * \tTWO: THREE.TOUCH.DOLLY_PAN\n     * }\n     * ```\n     * @type {Object}\n     */\n    this.touches = {\n      ONE: TOUCH.ROTATE,\n      TWO: TOUCH.DOLLY_PAN\n    };\n\n    /**\n     * Used internally by `saveState()` and `reset()`.\n     *\n     * @type {Vector3}\n     */\n    this.target0 = this.target.clone();\n\n    /**\n     * Used internally by `saveState()` and `reset()`.\n     *\n     * @type {Vector3}\n     */\n    this.position0 = this.object.position.clone();\n\n    /**\n     * Used internally by `saveState()` and `reset()`.\n     *\n     * @type {number}\n     */\n    this.zoom0 = this.object.zoom;\n\n    // the target DOM element for key events\n    this._domElementKeyEvents = null;\n\n    // internals\n\n    this._lastPosition = new Vector3();\n    this._lastQuaternion = new Quaternion();\n    this._lastTargetPosition = new Vector3();\n\n    // so camera.up is the orbit axis\n    this._quat = new Quaternion().setFromUnitVectors(object.up, new Vector3(0, 1, 0));\n    this._quatInverse = this._quat.clone().invert();\n\n    // current position in spherical coordinates\n    this._spherical = new Spherical();\n    this._sphericalDelta = new Spherical();\n    this._scale = 1;\n    this._panOffset = new Vector3();\n    this._rotateStart = new Vector2();\n    this._rotateEnd = new Vector2();\n    this._rotateDelta = new Vector2();\n    this._panStart = new Vector2();\n    this._panEnd = new Vector2();\n    this._panDelta = new Vector2();\n    this._dollyStart = new Vector2();\n    this._dollyEnd = new Vector2();\n    this._dollyDelta = new Vector2();\n    this._dollyDirection = new Vector3();\n    this._mouse = new Vector2();\n    this._performCursorZoom = false;\n    this._pointers = [];\n    this._pointerPositions = {};\n    this._controlActive = false;\n\n    // event listeners\n\n    this._onPointerMove = onPointerMove.bind(this);\n    this._onPointerDown = onPointerDown.bind(this);\n    this._onPointerUp = onPointerUp.bind(this);\n    this._onContextMenu = onContextMenu.bind(this);\n    this._onMouseWheel = onMouseWheel.bind(this);\n    this._onKeyDown = onKeyDown.bind(this);\n    this._onTouchStart = onTouchStart.bind(this);\n    this._onTouchMove = onTouchMove.bind(this);\n    this._onMouseDown = onMouseDown.bind(this);\n    this._onMouseMove = onMouseMove.bind(this);\n    this._interceptControlDown = interceptControlDown.bind(this);\n    this._interceptControlUp = interceptControlUp.bind(this);\n\n    //\n\n    if (this.domElement !== null) {\n      this.connect(this.domElement);\n    }\n    this.update();\n  }\n  connect(element) {\n    super.connect(element);\n    this.domElement.addEventListener('pointerdown', this._onPointerDown);\n    this.domElement.addEventListener('pointercancel', this._onPointerUp);\n    this.domElement.addEventListener('contextmenu', this._onContextMenu);\n    this.domElement.addEventListener('wheel', this._onMouseWheel, {\n      passive: false\n    });\n    const document = this.domElement.getRootNode(); // offscreen canvas compatibility\n    document.addEventListener('keydown', this._interceptControlDown, {\n      passive: true,\n      capture: true\n    });\n    this.domElement.style.touchAction = 'none'; // disable touch scroll\n  }\n  disconnect() {\n    this.domElement.removeEventListener('pointerdown', this._onPointerDown);\n    this.domElement.removeEventListener('pointermove', this._onPointerMove);\n    this.domElement.removeEventListener('pointerup', this._onPointerUp);\n    this.domElement.removeEventListener('pointercancel', this._onPointerUp);\n    this.domElement.removeEventListener('wheel', this._onMouseWheel);\n    this.domElement.removeEventListener('contextmenu', this._onContextMenu);\n    this.stopListenToKeyEvents();\n    const document = this.domElement.getRootNode(); // offscreen canvas compatibility\n    document.removeEventListener('keydown', this._interceptControlDown, {\n      capture: true\n    });\n    this.domElement.style.touchAction = 'auto';\n  }\n  dispose() {\n    this.disconnect();\n  }\n\n  /**\n   * Get the current vertical rotation, in radians.\n   *\n   * @return {number} The current vertical rotation, in radians.\n   */\n  getPolarAngle() {\n    return this._spherical.phi;\n  }\n\n  /**\n   * Get the current horizontal rotation, in radians.\n   *\n   * @return {number} The current horizontal rotation, in radians.\n   */\n  getAzimuthalAngle() {\n    return this._spherical.theta;\n  }\n\n  /**\n   * Returns the distance from the camera to the target.\n   *\n   * @return {number} The distance from the camera to the target.\n   */\n  getDistance() {\n    return this.object.position.distanceTo(this.target);\n  }\n\n  /**\n   * Adds key event listeners to the given DOM element.\n   * `window` is a recommended argument for using this method.\n   *\n   * @param {HTMLDOMElement} domElement - The DOM element\n   */\n  listenToKeyEvents(domElement) {\n    domElement.addEventListener('keydown', this._onKeyDown);\n    this._domElementKeyEvents = domElement;\n  }\n\n  /**\n   * Removes the key event listener previously defined with `listenToKeyEvents()`.\n   */\n  stopListenToKeyEvents() {\n    if (this._domElementKeyEvents !== null) {\n      this._domElementKeyEvents.removeEventListener('keydown', this._onKeyDown);\n      this._domElementKeyEvents = null;\n    }\n  }\n\n  /**\n   * Save the current state of the controls. This can later be recovered with `reset()`.\n   */\n  saveState() {\n    this.target0.copy(this.target);\n    this.position0.copy(this.object.position);\n    this.zoom0 = this.object.zoom;\n  }\n\n  /**\n   * Reset the controls to their state from either the last time the `saveState()`\n   * was called, or the initial state.\n   */\n  reset() {\n    this.target.copy(this.target0);\n    this.object.position.copy(this.position0);\n    this.object.zoom = this.zoom0;\n    this.object.updateProjectionMatrix();\n    this.dispatchEvent(_changeEvent);\n    this.update();\n    this.state = _STATE.NONE;\n  }\n  update(deltaTime = null) {\n    const position = this.object.position;\n    _v.copy(position).sub(this.target);\n\n    // rotate offset to \"y-axis-is-up\" space\n    _v.applyQuaternion(this._quat);\n\n    // angle from z-axis around y-axis\n    this._spherical.setFromVector3(_v);\n    if (this.autoRotate && this.state === _STATE.NONE) {\n      this._rotateLeft(this._getAutoRotationAngle(deltaTime));\n    }\n    if (this.enableDamping) {\n      this._spherical.theta += this._sphericalDelta.theta * this.dampingFactor;\n      this._spherical.phi += this._sphericalDelta.phi * this.dampingFactor;\n    } else {\n      this._spherical.theta += this._sphericalDelta.theta;\n      this._spherical.phi += this._sphericalDelta.phi;\n    }\n\n    // restrict theta to be between desired limits\n\n    let min = this.minAzimuthAngle;\n    let max = this.maxAzimuthAngle;\n    if (isFinite(min) && isFinite(max)) {\n      if (min < -Math.PI) min += _twoPI;else if (min > Math.PI) min -= _twoPI;\n      if (max < -Math.PI) max += _twoPI;else if (max > Math.PI) max -= _twoPI;\n      if (min <= max) {\n        this._spherical.theta = Math.max(min, Math.min(max, this._spherical.theta));\n      } else {\n        this._spherical.theta = this._spherical.theta > (min + max) / 2 ? Math.max(min, this._spherical.theta) : Math.min(max, this._spherical.theta);\n      }\n    }\n\n    // restrict phi to be between desired limits\n    this._spherical.phi = Math.max(this.minPolarAngle, Math.min(this.maxPolarAngle, this._spherical.phi));\n    this._spherical.makeSafe();\n\n    // move target to panned location\n\n    if (this.enableDamping === true) {\n      this.target.addScaledVector(this._panOffset, this.dampingFactor);\n    } else {\n      this.target.add(this._panOffset);\n    }\n\n    // Limit the target distance from the cursor to create a sphere around the center of interest\n    this.target.sub(this.cursor);\n    this.target.clampLength(this.minTargetRadius, this.maxTargetRadius);\n    this.target.add(this.cursor);\n    let zoomChanged = false;\n    // adjust the camera position based on zoom only if we're not zooming to the cursor or if it's an ortho camera\n    // we adjust zoom later in these cases\n    if (this.zoomToCursor && this._performCursorZoom || this.object.isOrthographicCamera) {\n      this._spherical.radius = this._clampDistance(this._spherical.radius);\n    } else {\n      const prevRadius = this._spherical.radius;\n      this._spherical.radius = this._clampDistance(this._spherical.radius * this._scale);\n      zoomChanged = prevRadius != this._spherical.radius;\n    }\n    _v.setFromSpherical(this._spherical);\n\n    // rotate offset back to \"camera-up-vector-is-up\" space\n    _v.applyQuaternion(this._quatInverse);\n    position.copy(this.target).add(_v);\n    this.object.lookAt(this.target);\n    if (this.enableDamping === true) {\n      this._sphericalDelta.theta *= 1 - this.dampingFactor;\n      this._sphericalDelta.phi *= 1 - this.dampingFactor;\n      this._panOffset.multiplyScalar(1 - this.dampingFactor);\n    } else {\n      this._sphericalDelta.set(0, 0, 0);\n      this._panOffset.set(0, 0, 0);\n    }\n\n    // adjust camera position\n    if (this.zoomToCursor && this._performCursorZoom) {\n      let newRadius = null;\n      if (this.object.isPerspectiveCamera) {\n        // move the camera down the pointer ray\n        // this method avoids floating point error\n        const prevRadius = _v.length();\n        newRadius = this._clampDistance(prevRadius * this._scale);\n        const radiusDelta = prevRadius - newRadius;\n        this.object.position.addScaledVector(this._dollyDirection, radiusDelta);\n        this.object.updateMatrixWorld();\n        zoomChanged = !!radiusDelta;\n      } else if (this.object.isOrthographicCamera) {\n        // adjust the ortho camera position based on zoom changes\n        const mouseBefore = new Vector3(this._mouse.x, this._mouse.y, 0);\n        mouseBefore.unproject(this.object);\n        const prevZoom = this.object.zoom;\n        this.object.zoom = Math.max(this.minZoom, Math.min(this.maxZoom, this.object.zoom / this._scale));\n        this.object.updateProjectionMatrix();\n        zoomChanged = prevZoom !== this.object.zoom;\n        const mouseAfter = new Vector3(this._mouse.x, this._mouse.y, 0);\n        mouseAfter.unproject(this.object);\n        this.object.position.sub(mouseAfter).add(mouseBefore);\n        this.object.updateMatrixWorld();\n        newRadius = _v.length();\n      } else {\n        console.warn('WARNING: OrbitControls.js encountered an unknown camera type - zoom to cursor disabled.');\n        this.zoomToCursor = false;\n      }\n\n      // handle the placement of the target\n      if (newRadius !== null) {\n        if (this.screenSpacePanning) {\n          // position the orbit target in front of the new camera position\n          this.target.set(0, 0, -1).transformDirection(this.object.matrix).multiplyScalar(newRadius).add(this.object.position);\n        } else {\n          // get the ray and translation plane to compute target\n          _ray.origin.copy(this.object.position);\n          _ray.direction.set(0, 0, -1).transformDirection(this.object.matrix);\n\n          // if the camera is 20 degrees above the horizon then don't adjust the focus target to avoid\n          // extremely large values\n          if (Math.abs(this.object.up.dot(_ray.direction)) < _TILT_LIMIT) {\n            this.object.lookAt(this.target);\n          } else {\n            _plane.setFromNormalAndCoplanarPoint(this.object.up, this.target);\n            _ray.intersectPlane(_plane, this.target);\n          }\n        }\n      }\n    } else if (this.object.isOrthographicCamera) {\n      const prevZoom = this.object.zoom;\n      this.object.zoom = Math.max(this.minZoom, Math.min(this.maxZoom, this.object.zoom / this._scale));\n      if (prevZoom !== this.object.zoom) {\n        this.object.updateProjectionMatrix();\n        zoomChanged = true;\n      }\n    }\n    this._scale = 1;\n    this._performCursorZoom = false;\n\n    // update condition is:\n    // min(camera displacement, camera rotation in radians)^2 > EPS\n    // using small-angle approximation cos(x/2) = 1 - x^2 / 8\n\n    if (zoomChanged || this._lastPosition.distanceToSquared(this.object.position) > _EPS || 8 * (1 - this._lastQuaternion.dot(this.object.quaternion)) > _EPS || this._lastTargetPosition.distanceToSquared(this.target) > _EPS) {\n      this.dispatchEvent(_changeEvent);\n      this._lastPosition.copy(this.object.position);\n      this._lastQuaternion.copy(this.object.quaternion);\n      this._lastTargetPosition.copy(this.target);\n      return true;\n    }\n    return false;\n  }\n  _getAutoRotationAngle(deltaTime) {\n    if (deltaTime !== null) {\n      return _twoPI / 60 * this.autoRotateSpeed * deltaTime;\n    } else {\n      return _twoPI / 60 / 60 * this.autoRotateSpeed;\n    }\n  }\n  _getZoomScale(delta) {\n    const normalizedDelta = Math.abs(delta * 0.01);\n    return Math.pow(0.95, this.zoomSpeed * normalizedDelta);\n  }\n  _rotateLeft(angle) {\n    this._sphericalDelta.theta -= angle;\n  }\n  _rotateUp(angle) {\n    this._sphericalDelta.phi -= angle;\n  }\n  _panLeft(distance, objectMatrix) {\n    _v.setFromMatrixColumn(objectMatrix, 0); // get X column of objectMatrix\n    _v.multiplyScalar(-distance);\n    this._panOffset.add(_v);\n  }\n  _panUp(distance, objectMatrix) {\n    if (this.screenSpacePanning === true) {\n      _v.setFromMatrixColumn(objectMatrix, 1);\n    } else {\n      _v.setFromMatrixColumn(objectMatrix, 0);\n      _v.crossVectors(this.object.up, _v);\n    }\n    _v.multiplyScalar(distance);\n    this._panOffset.add(_v);\n  }\n\n  // deltaX and deltaY are in pixels; right and down are positive\n  _pan(deltaX, deltaY) {\n    const element = this.domElement;\n    if (this.object.isPerspectiveCamera) {\n      // perspective\n      const position = this.object.position;\n      _v.copy(position).sub(this.target);\n      let targetDistance = _v.length();\n\n      // half of the fov is center to top of screen\n      targetDistance *= Math.tan(this.object.fov / 2 * Math.PI / 180.0);\n\n      // we use only clientHeight here so aspect ratio does not distort speed\n      this._panLeft(2 * deltaX * targetDistance / element.clientHeight, this.object.matrix);\n      this._panUp(2 * deltaY * targetDistance / element.clientHeight, this.object.matrix);\n    } else if (this.object.isOrthographicCamera) {\n      // orthographic\n      this._panLeft(deltaX * (this.object.right - this.object.left) / this.object.zoom / element.clientWidth, this.object.matrix);\n      this._panUp(deltaY * (this.object.top - this.object.bottom) / this.object.zoom / element.clientHeight, this.object.matrix);\n    } else {\n      // camera neither orthographic nor perspective\n      console.warn('WARNING: OrbitControls.js encountered an unknown camera type - pan disabled.');\n      this.enablePan = false;\n    }\n  }\n  _dollyOut(dollyScale) {\n    if (this.object.isPerspectiveCamera || this.object.isOrthographicCamera) {\n      this._scale /= dollyScale;\n    } else {\n      console.warn('WARNING: OrbitControls.js encountered an unknown camera type - dolly/zoom disabled.');\n      this.enableZoom = false;\n    }\n  }\n  _dollyIn(dollyScale) {\n    if (this.object.isPerspectiveCamera || this.object.isOrthographicCamera) {\n      this._scale *= dollyScale;\n    } else {\n      console.warn('WARNING: OrbitControls.js encountered an unknown camera type - dolly/zoom disabled.');\n      this.enableZoom = false;\n    }\n  }\n  _updateZoomParameters(x, y) {\n    if (!this.zoomToCursor) {\n      return;\n    }\n    this._performCursorZoom = true;\n    const rect = this.domElement.getBoundingClientRect();\n    const dx = x - rect.left;\n    const dy = y - rect.top;\n    const w = rect.width;\n    const h = rect.height;\n    this._mouse.x = dx / w * 2 - 1;\n    this._mouse.y = -(dy / h) * 2 + 1;\n    this._dollyDirection.set(this._mouse.x, this._mouse.y, 1).unproject(this.object).sub(this.object.position).normalize();\n  }\n  _clampDistance(dist) {\n    return Math.max(this.minDistance, Math.min(this.maxDistance, dist));\n  }\n\n  //\n  // event callbacks - update the object state\n  //\n\n  _handleMouseDownRotate(event) {\n    this._rotateStart.set(event.clientX, event.clientY);\n  }\n  _handleMouseDownDolly(event) {\n    this._updateZoomParameters(event.clientX, event.clientX);\n    this._dollyStart.set(event.clientX, event.clientY);\n  }\n  _handleMouseDownPan(event) {\n    this._panStart.set(event.clientX, event.clientY);\n  }\n  _handleMouseMoveRotate(event) {\n    this._rotateEnd.set(event.clientX, event.clientY);\n    this._rotateDelta.subVectors(this._rotateEnd, this._rotateStart).multiplyScalar(this.rotateSpeed);\n    const element = this.domElement;\n    this._rotateLeft(_twoPI * this._rotateDelta.x / element.clientHeight); // yes, height\n\n    this._rotateUp(_twoPI * this._rotateDelta.y / element.clientHeight);\n    this._rotateStart.copy(this._rotateEnd);\n    this.update();\n  }\n  _handleMouseMoveDolly(event) {\n    this._dollyEnd.set(event.clientX, event.clientY);\n    this._dollyDelta.subVectors(this._dollyEnd, this._dollyStart);\n    if (this._dollyDelta.y > 0) {\n      this._dollyOut(this._getZoomScale(this._dollyDelta.y));\n    } else if (this._dollyDelta.y < 0) {\n      this._dollyIn(this._getZoomScale(this._dollyDelta.y));\n    }\n    this._dollyStart.copy(this._dollyEnd);\n    this.update();\n  }\n  _handleMouseMovePan(event) {\n    this._panEnd.set(event.clientX, event.clientY);\n    this._panDelta.subVectors(this._panEnd, this._panStart).multiplyScalar(this.panSpeed);\n    this._pan(this._panDelta.x, this._panDelta.y);\n    this._panStart.copy(this._panEnd);\n    this.update();\n  }\n  _handleMouseWheel(event) {\n    this._updateZoomParameters(event.clientX, event.clientY);\n    if (event.deltaY < 0) {\n      this._dollyIn(this._getZoomScale(event.deltaY));\n    } else if (event.deltaY > 0) {\n      this._dollyOut(this._getZoomScale(event.deltaY));\n    }\n    this.update();\n  }\n  _handleKeyDown(event) {\n    let needsUpdate = false;\n    switch (event.code) {\n      case this.keys.UP:\n        if (event.ctrlKey || event.metaKey || event.shiftKey) {\n          if (this.enableRotate) {\n            this._rotateUp(_twoPI * this.keyRotateSpeed / this.domElement.clientHeight);\n          }\n        } else {\n          if (this.enablePan) {\n            this._pan(0, this.keyPanSpeed);\n          }\n        }\n        needsUpdate = true;\n        break;\n      case this.keys.BOTTOM:\n        if (event.ctrlKey || event.metaKey || event.shiftKey) {\n          if (this.enableRotate) {\n            this._rotateUp(-_twoPI * this.keyRotateSpeed / this.domElement.clientHeight);\n          }\n        } else {\n          if (this.enablePan) {\n            this._pan(0, -this.keyPanSpeed);\n          }\n        }\n        needsUpdate = true;\n        break;\n      case this.keys.LEFT:\n        if (event.ctrlKey || event.metaKey || event.shiftKey) {\n          if (this.enableRotate) {\n            this._rotateLeft(_twoPI * this.keyRotateSpeed / this.domElement.clientHeight);\n          }\n        } else {\n          if (this.enablePan) {\n            this._pan(this.keyPanSpeed, 0);\n          }\n        }\n        needsUpdate = true;\n        break;\n      case this.keys.RIGHT:\n        if (event.ctrlKey || event.metaKey || event.shiftKey) {\n          if (this.enableRotate) {\n            this._rotateLeft(-_twoPI * this.keyRotateSpeed / this.domElement.clientHeight);\n          }\n        } else {\n          if (this.enablePan) {\n            this._pan(-this.keyPanSpeed, 0);\n          }\n        }\n        needsUpdate = true;\n        break;\n    }\n    if (needsUpdate) {\n      // prevent the browser from scrolling on cursor keys\n      event.preventDefault();\n      this.update();\n    }\n  }\n  _handleTouchStartRotate(event) {\n    if (this._pointers.length === 1) {\n      this._rotateStart.set(event.pageX, event.pageY);\n    } else {\n      const position = this._getSecondPointerPosition(event);\n      const x = 0.5 * (event.pageX + position.x);\n      const y = 0.5 * (event.pageY + position.y);\n      this._rotateStart.set(x, y);\n    }\n  }\n  _handleTouchStartPan(event) {\n    if (this._pointers.length === 1) {\n      this._panStart.set(event.pageX, event.pageY);\n    } else {\n      const position = this._getSecondPointerPosition(event);\n      const x = 0.5 * (event.pageX + position.x);\n      const y = 0.5 * (event.pageY + position.y);\n      this._panStart.set(x, y);\n    }\n  }\n  _handleTouchStartDolly(event) {\n    const position = this._getSecondPointerPosition(event);\n    const dx = event.pageX - position.x;\n    const dy = event.pageY - position.y;\n    const distance = Math.sqrt(dx * dx + dy * dy);\n    this._dollyStart.set(0, distance);\n  }\n  _handleTouchStartDollyPan(event) {\n    if (this.enableZoom) this._handleTouchStartDolly(event);\n    if (this.enablePan) this._handleTouchStartPan(event);\n  }\n  _handleTouchStartDollyRotate(event) {\n    if (this.enableZoom) this._handleTouchStartDolly(event);\n    if (this.enableRotate) this._handleTouchStartRotate(event);\n  }\n  _handleTouchMoveRotate(event) {\n    if (this._pointers.length == 1) {\n      this._rotateEnd.set(event.pageX, event.pageY);\n    } else {\n      const position = this._getSecondPointerPosition(event);\n      const x = 0.5 * (event.pageX + position.x);\n      const y = 0.5 * (event.pageY + position.y);\n      this._rotateEnd.set(x, y);\n    }\n    this._rotateDelta.subVectors(this._rotateEnd, this._rotateStart).multiplyScalar(this.rotateSpeed);\n    const element = this.domElement;\n    this._rotateLeft(_twoPI * this._rotateDelta.x / element.clientHeight); // yes, height\n\n    this._rotateUp(_twoPI * this._rotateDelta.y / element.clientHeight);\n    this._rotateStart.copy(this._rotateEnd);\n  }\n  _handleTouchMovePan(event) {\n    if (this._pointers.length === 1) {\n      this._panEnd.set(event.pageX, event.pageY);\n    } else {\n      const position = this._getSecondPointerPosition(event);\n      const x = 0.5 * (event.pageX + position.x);\n      const y = 0.5 * (event.pageY + position.y);\n      this._panEnd.set(x, y);\n    }\n    this._panDelta.subVectors(this._panEnd, this._panStart).multiplyScalar(this.panSpeed);\n    this._pan(this._panDelta.x, this._panDelta.y);\n    this._panStart.copy(this._panEnd);\n  }\n  _handleTouchMoveDolly(event) {\n    const position = this._getSecondPointerPosition(event);\n    const dx = event.pageX - position.x;\n    const dy = event.pageY - position.y;\n    const distance = Math.sqrt(dx * dx + dy * dy);\n    this._dollyEnd.set(0, distance);\n    this._dollyDelta.set(0, Math.pow(this._dollyEnd.y / this._dollyStart.y, this.zoomSpeed));\n    this._dollyOut(this._dollyDelta.y);\n    this._dollyStart.copy(this._dollyEnd);\n    const centerX = (event.pageX + position.x) * 0.5;\n    const centerY = (event.pageY + position.y) * 0.5;\n    this._updateZoomParameters(centerX, centerY);\n  }\n  _handleTouchMoveDollyPan(event) {\n    if (this.enableZoom) this._handleTouchMoveDolly(event);\n    if (this.enablePan) this._handleTouchMovePan(event);\n  }\n  _handleTouchMoveDollyRotate(event) {\n    if (this.enableZoom) this._handleTouchMoveDolly(event);\n    if (this.enableRotate) this._handleTouchMoveRotate(event);\n  }\n\n  // pointers\n\n  _addPointer(event) {\n    this._pointers.push(event.pointerId);\n  }\n  _removePointer(event) {\n    delete this._pointerPositions[event.pointerId];\n    for (let i = 0; i < this._pointers.length; i++) {\n      if (this._pointers[i] == event.pointerId) {\n        this._pointers.splice(i, 1);\n        return;\n      }\n    }\n  }\n  _isTrackingPointer(event) {\n    for (let i = 0; i < this._pointers.length; i++) {\n      if (this._pointers[i] == event.pointerId) return true;\n    }\n    return false;\n  }\n  _trackPointer(event) {\n    let position = this._pointerPositions[event.pointerId];\n    if (position === undefined) {\n      position = new Vector2();\n      this._pointerPositions[event.pointerId] = position;\n    }\n    position.set(event.pageX, event.pageY);\n  }\n  _getSecondPointerPosition(event) {\n    const pointerId = event.pointerId === this._pointers[0] ? this._pointers[1] : this._pointers[0];\n    return this._pointerPositions[pointerId];\n  }\n\n  //\n\n  _customWheelEvent(event) {\n    const mode = event.deltaMode;\n\n    // minimal wheel event altered to meet delta-zoom demand\n    const newEvent = {\n      clientX: event.clientX,\n      clientY: event.clientY,\n      deltaY: event.deltaY\n    };\n    switch (mode) {\n      case 1:\n        // LINE_MODE\n        newEvent.deltaY *= 16;\n        break;\n      case 2:\n        // PAGE_MODE\n        newEvent.deltaY *= 100;\n        break;\n    }\n\n    // detect if event was triggered by pinching\n    if (event.ctrlKey && !this._controlActive) {\n      newEvent.deltaY *= 10;\n    }\n    return newEvent;\n  }\n}\nfunction onPointerDown(event) {\n  if (this.enabled === false) return;\n  if (this._pointers.length === 0) {\n    this.domElement.setPointerCapture(event.pointerId);\n    this.domElement.addEventListener('pointermove', this._onPointerMove);\n    this.domElement.addEventListener('pointerup', this._onPointerUp);\n  }\n\n  //\n\n  if (this._isTrackingPointer(event)) return;\n\n  //\n\n  this._addPointer(event);\n  if (event.pointerType === 'touch') {\n    this._onTouchStart(event);\n  } else {\n    this._onMouseDown(event);\n  }\n}\nfunction onPointerMove(event) {\n  if (this.enabled === false) return;\n  if (event.pointerType === 'touch') {\n    this._onTouchMove(event);\n  } else {\n    this._onMouseMove(event);\n  }\n}\nfunction onPointerUp(event) {\n  this._removePointer(event);\n  switch (this._pointers.length) {\n    case 0:\n      this.domElement.releasePointerCapture(event.pointerId);\n      this.domElement.removeEventListener('pointermove', this._onPointerMove);\n      this.domElement.removeEventListener('pointerup', this._onPointerUp);\n      this.dispatchEvent(_endEvent);\n      this.state = _STATE.NONE;\n      break;\n    case 1:\n      const pointerId = this._pointers[0];\n      const position = this._pointerPositions[pointerId];\n\n      // minimal placeholder event - allows state correction on pointer-up\n      this._onTouchStart({\n        pointerId: pointerId,\n        pageX: position.x,\n        pageY: position.y\n      });\n      break;\n  }\n}\nfunction onMouseDown(event) {\n  let mouseAction;\n  switch (event.button) {\n    case 0:\n      mouseAction = this.mouseButtons.LEFT;\n      break;\n    case 1:\n      mouseAction = this.mouseButtons.MIDDLE;\n      break;\n    case 2:\n      mouseAction = this.mouseButtons.RIGHT;\n      break;\n    default:\n      mouseAction = -1;\n  }\n  switch (mouseAction) {\n    case MOUSE.DOLLY:\n      if (this.enableZoom === false) return;\n      this._handleMouseDownDolly(event);\n      this.state = _STATE.DOLLY;\n      break;\n    case MOUSE.ROTATE:\n      if (event.ctrlKey || event.metaKey || event.shiftKey) {\n        if (this.enablePan === false) return;\n        this._handleMouseDownPan(event);\n        this.state = _STATE.PAN;\n      } else {\n        if (this.enableRotate === false) return;\n        this._handleMouseDownRotate(event);\n        this.state = _STATE.ROTATE;\n      }\n      break;\n    case MOUSE.PAN:\n      if (event.ctrlKey || event.metaKey || event.shiftKey) {\n        if (this.enableRotate === false) return;\n        this._handleMouseDownRotate(event);\n        this.state = _STATE.ROTATE;\n      } else {\n        if (this.enablePan === false) return;\n        this._handleMouseDownPan(event);\n        this.state = _STATE.PAN;\n      }\n      break;\n    default:\n      this.state = _STATE.NONE;\n  }\n  if (this.state !== _STATE.NONE) {\n    this.dispatchEvent(_startEvent);\n  }\n}\nfunction onMouseMove(event) {\n  switch (this.state) {\n    case _STATE.ROTATE:\n      if (this.enableRotate === false) return;\n      this._handleMouseMoveRotate(event);\n      break;\n    case _STATE.DOLLY:\n      if (this.enableZoom === false) return;\n      this._handleMouseMoveDolly(event);\n      break;\n    case _STATE.PAN:\n      if (this.enablePan === false) return;\n      this._handleMouseMovePan(event);\n      break;\n  }\n}\nfunction onMouseWheel(event) {\n  if (this.enabled === false || this.enableZoom === false || this.state !== _STATE.NONE) return;\n  event.preventDefault();\n  this.dispatchEvent(_startEvent);\n  this._handleMouseWheel(this._customWheelEvent(event));\n  this.dispatchEvent(_endEvent);\n}\nfunction onKeyDown(event) {\n  if (this.enabled === false) return;\n  this._handleKeyDown(event);\n}\nfunction onTouchStart(event) {\n  this._trackPointer(event);\n  switch (this._pointers.length) {\n    case 1:\n      switch (this.touches.ONE) {\n        case TOUCH.ROTATE:\n          if (this.enableRotate === false) return;\n          this._handleTouchStartRotate(event);\n          this.state = _STATE.TOUCH_ROTATE;\n          break;\n        case TOUCH.PAN:\n          if (this.enablePan === false) return;\n          this._handleTouchStartPan(event);\n          this.state = _STATE.TOUCH_PAN;\n          break;\n        default:\n          this.state = _STATE.NONE;\n      }\n      break;\n    case 2:\n      switch (this.touches.TWO) {\n        case TOUCH.DOLLY_PAN:\n          if (this.enableZoom === false && this.enablePan === false) return;\n          this._handleTouchStartDollyPan(event);\n          this.state = _STATE.TOUCH_DOLLY_PAN;\n          break;\n        case TOUCH.DOLLY_ROTATE:\n          if (this.enableZoom === false && this.enableRotate === false) return;\n          this._handleTouchStartDollyRotate(event);\n          this.state = _STATE.TOUCH_DOLLY_ROTATE;\n          break;\n        default:\n          this.state = _STATE.NONE;\n      }\n      break;\n    default:\n      this.state = _STATE.NONE;\n  }\n  if (this.state !== _STATE.NONE) {\n    this.dispatchEvent(_startEvent);\n  }\n}\nfunction onTouchMove(event) {\n  this._trackPointer(event);\n  switch (this.state) {\n    case _STATE.TOUCH_ROTATE:\n      if (this.enableRotate === false) return;\n      this._handleTouchMoveRotate(event);\n      this.update();\n      break;\n    case _STATE.TOUCH_PAN:\n      if (this.enablePan === false) return;\n      this._handleTouchMovePan(event);\n      this.update();\n      break;\n    case _STATE.TOUCH_DOLLY_PAN:\n      if (this.enableZoom === false && this.enablePan === false) return;\n      this._handleTouchMoveDollyPan(event);\n      this.update();\n      break;\n    case _STATE.TOUCH_DOLLY_ROTATE:\n      if (this.enableZoom === false && this.enableRotate === false) return;\n      this._handleTouchMoveDollyRotate(event);\n      this.update();\n      break;\n    default:\n      this.state = _STATE.NONE;\n  }\n}\nfunction onContextMenu(event) {\n  if (this.enabled === false) return;\n  event.preventDefault();\n}\nfunction interceptControlDown(event) {\n  if (event.key === 'Control') {\n    this._controlActive = true;\n    const document = this.domElement.getRootNode(); // offscreen canvas compatibility\n\n    document.addEventListener('keyup', this._interceptControlUp, {\n      passive: true,\n      capture: true\n    });\n  }\n}\nfunction interceptControlUp(event) {\n  if (event.key === 'Control') {\n    this._controlActive = false;\n    const document = this.domElement.getRootNode(); // offscreen canvas compatibility\n\n    document.removeEventListener('keyup', this._interceptControlUp, {\n      passive: true,\n      capture: true\n    });\n  }\n}\nexport { OrbitControls };"], "mappings": ";;;;;;;;;;;;;;;AAQA,IAAM,eAAe;AAAA,EACnB,MAAM;AACR;AAQA,IAAM,cAAc;AAAA,EAClB,MAAM;AACR;AAQA,IAAM,YAAY;AAAA,EAChB,MAAM;AACR;AACA,IAAM,OAAO,IAAI,IAAI;AACrB,IAAM,SAAS,IAAI,MAAM;AACzB,IAAM,cAAc,KAAK,IAAI,KAAK,UAAU,OAAO;AACnD,IAAM,KAAK,IAAI,QAAQ;AACvB,IAAM,SAAS,IAAI,KAAK;AACxB,IAAM,SAAS;AAAA,EACb,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,KAAK;AAAA,EACL,cAAc;AAAA,EACd,WAAW;AAAA,EACX,iBAAiB;AAAA,EACjB,oBAAoB;AACtB;AACA,IAAM,OAAO;AA+Bb,IAAM,gBAAN,cAA4B,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOnC,YAAY,QAAQ,aAAa,MAAM;AACrC,UAAM,QAAQ,UAAU;AACxB,SAAK,QAAQ,OAAO;AAQpB,SAAK,SAAS,IAAI,QAAQ;AAS1B,SAAK,SAAS,IAAI,QAAQ;AAQ1B,SAAK,cAAc;AAQnB,SAAK,cAAc;AAQnB,SAAK,UAAU;AAQf,SAAK,UAAU;AAQf,SAAK,kBAAkB;AAQvB,SAAK,kBAAkB;AAQvB,SAAK,gBAAgB;AAQrB,SAAK,gBAAgB,KAAK;AAS1B,SAAK,kBAAkB;AASvB,SAAK,kBAAkB;AAUvB,SAAK,gBAAgB;AAUrB,SAAK,gBAAgB;AAQrB,SAAK,aAAa;AAQlB,SAAK,YAAY;AAYjB,SAAK,eAAe;AAQpB,SAAK,cAAc;AAQnB,SAAK,iBAAiB;AAQtB,SAAK,YAAY;AAQjB,SAAK,WAAW;AAUhB,SAAK,qBAAqB;AAS1B,SAAK,cAAc;AAQnB,SAAK,eAAe;AAYpB,SAAK,aAAa;AAWlB,SAAK,kBAAkB;AAevB,SAAK,OAAO;AAAA,MACV,MAAM;AAAA,MACN,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,QAAQ;AAAA,IACV;AAcA,SAAK,eAAe;AAAA,MAClB,MAAM,MAAM;AAAA,MACZ,QAAQ,MAAM;AAAA,MACd,OAAO,MAAM;AAAA,IACf;AAaA,SAAK,UAAU;AAAA,MACb,KAAK,MAAM;AAAA,MACX,KAAK,MAAM;AAAA,IACb;AAOA,SAAK,UAAU,KAAK,OAAO,MAAM;AAOjC,SAAK,YAAY,KAAK,OAAO,SAAS,MAAM;AAO5C,SAAK,QAAQ,KAAK,OAAO;AAGzB,SAAK,uBAAuB;AAI5B,SAAK,gBAAgB,IAAI,QAAQ;AACjC,SAAK,kBAAkB,IAAI,WAAW;AACtC,SAAK,sBAAsB,IAAI,QAAQ;AAGvC,SAAK,QAAQ,IAAI,WAAW,EAAE,mBAAmB,OAAO,IAAI,IAAI,QAAQ,GAAG,GAAG,CAAC,CAAC;AAChF,SAAK,eAAe,KAAK,MAAM,MAAM,EAAE,OAAO;AAG9C,SAAK,aAAa,IAAI,UAAU;AAChC,SAAK,kBAAkB,IAAI,UAAU;AACrC,SAAK,SAAS;AACd,SAAK,aAAa,IAAI,QAAQ;AAC9B,SAAK,eAAe,IAAI,QAAQ;AAChC,SAAK,aAAa,IAAI,QAAQ;AAC9B,SAAK,eAAe,IAAI,QAAQ;AAChC,SAAK,YAAY,IAAI,QAAQ;AAC7B,SAAK,UAAU,IAAI,QAAQ;AAC3B,SAAK,YAAY,IAAI,QAAQ;AAC7B,SAAK,cAAc,IAAI,QAAQ;AAC/B,SAAK,YAAY,IAAI,QAAQ;AAC7B,SAAK,cAAc,IAAI,QAAQ;AAC/B,SAAK,kBAAkB,IAAI,QAAQ;AACnC,SAAK,SAAS,IAAI,QAAQ;AAC1B,SAAK,qBAAqB;AAC1B,SAAK,YAAY,CAAC;AAClB,SAAK,oBAAoB,CAAC;AAC1B,SAAK,iBAAiB;AAItB,SAAK,iBAAiB,cAAc,KAAK,IAAI;AAC7C,SAAK,iBAAiB,cAAc,KAAK,IAAI;AAC7C,SAAK,eAAe,YAAY,KAAK,IAAI;AACzC,SAAK,iBAAiB,cAAc,KAAK,IAAI;AAC7C,SAAK,gBAAgB,aAAa,KAAK,IAAI;AAC3C,SAAK,aAAa,UAAU,KAAK,IAAI;AACrC,SAAK,gBAAgB,aAAa,KAAK,IAAI;AAC3C,SAAK,eAAe,YAAY,KAAK,IAAI;AACzC,SAAK,eAAe,YAAY,KAAK,IAAI;AACzC,SAAK,eAAe,YAAY,KAAK,IAAI;AACzC,SAAK,wBAAwB,qBAAqB,KAAK,IAAI;AAC3D,SAAK,sBAAsB,mBAAmB,KAAK,IAAI;AAIvD,QAAI,KAAK,eAAe,MAAM;AAC5B,WAAK,QAAQ,KAAK,UAAU;AAAA,IAC9B;AACA,SAAK,OAAO;AAAA,EACd;AAAA,EACA,QAAQ,SAAS;AACf,UAAM,QAAQ,OAAO;AACrB,SAAK,WAAW,iBAAiB,eAAe,KAAK,cAAc;AACnE,SAAK,WAAW,iBAAiB,iBAAiB,KAAK,YAAY;AACnE,SAAK,WAAW,iBAAiB,eAAe,KAAK,cAAc;AACnE,SAAK,WAAW,iBAAiB,SAAS,KAAK,eAAe;AAAA,MAC5D,SAAS;AAAA,IACX,CAAC;AACD,UAAM,WAAW,KAAK,WAAW,YAAY;AAC7C,aAAS,iBAAiB,WAAW,KAAK,uBAAuB;AAAA,MAC/D,SAAS;AAAA,MACT,SAAS;AAAA,IACX,CAAC;AACD,SAAK,WAAW,MAAM,cAAc;AAAA,EACtC;AAAA,EACA,aAAa;AACX,SAAK,WAAW,oBAAoB,eAAe,KAAK,cAAc;AACtE,SAAK,WAAW,oBAAoB,eAAe,KAAK,cAAc;AACtE,SAAK,WAAW,oBAAoB,aAAa,KAAK,YAAY;AAClE,SAAK,WAAW,oBAAoB,iBAAiB,KAAK,YAAY;AACtE,SAAK,WAAW,oBAAoB,SAAS,KAAK,aAAa;AAC/D,SAAK,WAAW,oBAAoB,eAAe,KAAK,cAAc;AACtE,SAAK,sBAAsB;AAC3B,UAAM,WAAW,KAAK,WAAW,YAAY;AAC7C,aAAS,oBAAoB,WAAW,KAAK,uBAAuB;AAAA,MAClE,SAAS;AAAA,IACX,CAAC;AACD,SAAK,WAAW,MAAM,cAAc;AAAA,EACtC;AAAA,EACA,UAAU;AACR,SAAK,WAAW;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,gBAAgB;AACd,WAAO,KAAK,WAAW;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,oBAAoB;AAClB,WAAO,KAAK,WAAW;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,cAAc;AACZ,WAAO,KAAK,OAAO,SAAS,WAAW,KAAK,MAAM;AAAA,EACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,kBAAkB,YAAY;AAC5B,eAAW,iBAAiB,WAAW,KAAK,UAAU;AACtD,SAAK,uBAAuB;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA,EAKA,wBAAwB;AACtB,QAAI,KAAK,yBAAyB,MAAM;AACtC,WAAK,qBAAqB,oBAAoB,WAAW,KAAK,UAAU;AACxE,WAAK,uBAAuB;AAAA,IAC9B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY;AACV,SAAK,QAAQ,KAAK,KAAK,MAAM;AAC7B,SAAK,UAAU,KAAK,KAAK,OAAO,QAAQ;AACxC,SAAK,QAAQ,KAAK,OAAO;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QAAQ;AACN,SAAK,OAAO,KAAK,KAAK,OAAO;AAC7B,SAAK,OAAO,SAAS,KAAK,KAAK,SAAS;AACxC,SAAK,OAAO,OAAO,KAAK;AACxB,SAAK,OAAO,uBAAuB;AACnC,SAAK,cAAc,YAAY;AAC/B,SAAK,OAAO;AACZ,SAAK,QAAQ,OAAO;AAAA,EACtB;AAAA,EACA,OAAO,YAAY,MAAM;AACvB,UAAM,WAAW,KAAK,OAAO;AAC7B,OAAG,KAAK,QAAQ,EAAE,IAAI,KAAK,MAAM;AAGjC,OAAG,gBAAgB,KAAK,KAAK;AAG7B,SAAK,WAAW,eAAe,EAAE;AACjC,QAAI,KAAK,cAAc,KAAK,UAAU,OAAO,MAAM;AACjD,WAAK,YAAY,KAAK,sBAAsB,SAAS,CAAC;AAAA,IACxD;AACA,QAAI,KAAK,eAAe;AACtB,WAAK,WAAW,SAAS,KAAK,gBAAgB,QAAQ,KAAK;AAC3D,WAAK,WAAW,OAAO,KAAK,gBAAgB,MAAM,KAAK;AAAA,IACzD,OAAO;AACL,WAAK,WAAW,SAAS,KAAK,gBAAgB;AAC9C,WAAK,WAAW,OAAO,KAAK,gBAAgB;AAAA,IAC9C;AAIA,QAAI,MAAM,KAAK;AACf,QAAI,MAAM,KAAK;AACf,QAAI,SAAS,GAAG,KAAK,SAAS,GAAG,GAAG;AAClC,UAAI,MAAM,CAAC,KAAK,GAAI,QAAO;AAAA,eAAgB,MAAM,KAAK,GAAI,QAAO;AACjE,UAAI,MAAM,CAAC,KAAK,GAAI,QAAO;AAAA,eAAgB,MAAM,KAAK,GAAI,QAAO;AACjE,UAAI,OAAO,KAAK;AACd,aAAK,WAAW,QAAQ,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,WAAW,KAAK,CAAC;AAAA,MAC5E,OAAO;AACL,aAAK,WAAW,QAAQ,KAAK,WAAW,SAAS,MAAM,OAAO,IAAI,KAAK,IAAI,KAAK,KAAK,WAAW,KAAK,IAAI,KAAK,IAAI,KAAK,KAAK,WAAW,KAAK;AAAA,MAC9I;AAAA,IACF;AAGA,SAAK,WAAW,MAAM,KAAK,IAAI,KAAK,eAAe,KAAK,IAAI,KAAK,eAAe,KAAK,WAAW,GAAG,CAAC;AACpG,SAAK,WAAW,SAAS;AAIzB,QAAI,KAAK,kBAAkB,MAAM;AAC/B,WAAK,OAAO,gBAAgB,KAAK,YAAY,KAAK,aAAa;AAAA,IACjE,OAAO;AACL,WAAK,OAAO,IAAI,KAAK,UAAU;AAAA,IACjC;AAGA,SAAK,OAAO,IAAI,KAAK,MAAM;AAC3B,SAAK,OAAO,YAAY,KAAK,iBAAiB,KAAK,eAAe;AAClE,SAAK,OAAO,IAAI,KAAK,MAAM;AAC3B,QAAI,cAAc;AAGlB,QAAI,KAAK,gBAAgB,KAAK,sBAAsB,KAAK,OAAO,sBAAsB;AACpF,WAAK,WAAW,SAAS,KAAK,eAAe,KAAK,WAAW,MAAM;AAAA,IACrE,OAAO;AACL,YAAM,aAAa,KAAK,WAAW;AACnC,WAAK,WAAW,SAAS,KAAK,eAAe,KAAK,WAAW,SAAS,KAAK,MAAM;AACjF,oBAAc,cAAc,KAAK,WAAW;AAAA,IAC9C;AACA,OAAG,iBAAiB,KAAK,UAAU;AAGnC,OAAG,gBAAgB,KAAK,YAAY;AACpC,aAAS,KAAK,KAAK,MAAM,EAAE,IAAI,EAAE;AACjC,SAAK,OAAO,OAAO,KAAK,MAAM;AAC9B,QAAI,KAAK,kBAAkB,MAAM;AAC/B,WAAK,gBAAgB,SAAS,IAAI,KAAK;AACvC,WAAK,gBAAgB,OAAO,IAAI,KAAK;AACrC,WAAK,WAAW,eAAe,IAAI,KAAK,aAAa;AAAA,IACvD,OAAO;AACL,WAAK,gBAAgB,IAAI,GAAG,GAAG,CAAC;AAChC,WAAK,WAAW,IAAI,GAAG,GAAG,CAAC;AAAA,IAC7B;AAGA,QAAI,KAAK,gBAAgB,KAAK,oBAAoB;AAChD,UAAI,YAAY;AAChB,UAAI,KAAK,OAAO,qBAAqB;AAGnC,cAAM,aAAa,GAAG,OAAO;AAC7B,oBAAY,KAAK,eAAe,aAAa,KAAK,MAAM;AACxD,cAAM,cAAc,aAAa;AACjC,aAAK,OAAO,SAAS,gBAAgB,KAAK,iBAAiB,WAAW;AACtE,aAAK,OAAO,kBAAkB;AAC9B,sBAAc,CAAC,CAAC;AAAA,MAClB,WAAW,KAAK,OAAO,sBAAsB;AAE3C,cAAM,cAAc,IAAI,QAAQ,KAAK,OAAO,GAAG,KAAK,OAAO,GAAG,CAAC;AAC/D,oBAAY,UAAU,KAAK,MAAM;AACjC,cAAM,WAAW,KAAK,OAAO;AAC7B,aAAK,OAAO,OAAO,KAAK,IAAI,KAAK,SAAS,KAAK,IAAI,KAAK,SAAS,KAAK,OAAO,OAAO,KAAK,MAAM,CAAC;AAChG,aAAK,OAAO,uBAAuB;AACnC,sBAAc,aAAa,KAAK,OAAO;AACvC,cAAM,aAAa,IAAI,QAAQ,KAAK,OAAO,GAAG,KAAK,OAAO,GAAG,CAAC;AAC9D,mBAAW,UAAU,KAAK,MAAM;AAChC,aAAK,OAAO,SAAS,IAAI,UAAU,EAAE,IAAI,WAAW;AACpD,aAAK,OAAO,kBAAkB;AAC9B,oBAAY,GAAG,OAAO;AAAA,MACxB,OAAO;AACL,gBAAQ,KAAK,yFAAyF;AACtG,aAAK,eAAe;AAAA,MACtB;AAGA,UAAI,cAAc,MAAM;AACtB,YAAI,KAAK,oBAAoB;AAE3B,eAAK,OAAO,IAAI,GAAG,GAAG,EAAE,EAAE,mBAAmB,KAAK,OAAO,MAAM,EAAE,eAAe,SAAS,EAAE,IAAI,KAAK,OAAO,QAAQ;AAAA,QACrH,OAAO;AAEL,eAAK,OAAO,KAAK,KAAK,OAAO,QAAQ;AACrC,eAAK,UAAU,IAAI,GAAG,GAAG,EAAE,EAAE,mBAAmB,KAAK,OAAO,MAAM;AAIlE,cAAI,KAAK,IAAI,KAAK,OAAO,GAAG,IAAI,KAAK,SAAS,CAAC,IAAI,aAAa;AAC9D,iBAAK,OAAO,OAAO,KAAK,MAAM;AAAA,UAChC,OAAO;AACL,mBAAO,8BAA8B,KAAK,OAAO,IAAI,KAAK,MAAM;AAChE,iBAAK,eAAe,QAAQ,KAAK,MAAM;AAAA,UACzC;AAAA,QACF;AAAA,MACF;AAAA,IACF,WAAW,KAAK,OAAO,sBAAsB;AAC3C,YAAM,WAAW,KAAK,OAAO;AAC7B,WAAK,OAAO,OAAO,KAAK,IAAI,KAAK,SAAS,KAAK,IAAI,KAAK,SAAS,KAAK,OAAO,OAAO,KAAK,MAAM,CAAC;AAChG,UAAI,aAAa,KAAK,OAAO,MAAM;AACjC,aAAK,OAAO,uBAAuB;AACnC,sBAAc;AAAA,MAChB;AAAA,IACF;AACA,SAAK,SAAS;AACd,SAAK,qBAAqB;AAM1B,QAAI,eAAe,KAAK,cAAc,kBAAkB,KAAK,OAAO,QAAQ,IAAI,QAAQ,KAAK,IAAI,KAAK,gBAAgB,IAAI,KAAK,OAAO,UAAU,KAAK,QAAQ,KAAK,oBAAoB,kBAAkB,KAAK,MAAM,IAAI,MAAM;AAC3N,WAAK,cAAc,YAAY;AAC/B,WAAK,cAAc,KAAK,KAAK,OAAO,QAAQ;AAC5C,WAAK,gBAAgB,KAAK,KAAK,OAAO,UAAU;AAChD,WAAK,oBAAoB,KAAK,KAAK,MAAM;AACzC,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA,EACA,sBAAsB,WAAW;AAC/B,QAAI,cAAc,MAAM;AACtB,aAAO,SAAS,KAAK,KAAK,kBAAkB;AAAA,IAC9C,OAAO;AACL,aAAO,SAAS,KAAK,KAAK,KAAK;AAAA,IACjC;AAAA,EACF;AAAA,EACA,cAAc,OAAO;AACnB,UAAM,kBAAkB,KAAK,IAAI,QAAQ,IAAI;AAC7C,WAAO,KAAK,IAAI,MAAM,KAAK,YAAY,eAAe;AAAA,EACxD;AAAA,EACA,YAAY,OAAO;AACjB,SAAK,gBAAgB,SAAS;AAAA,EAChC;AAAA,EACA,UAAU,OAAO;AACf,SAAK,gBAAgB,OAAO;AAAA,EAC9B;AAAA,EACA,SAAS,UAAU,cAAc;AAC/B,OAAG,oBAAoB,cAAc,CAAC;AACtC,OAAG,eAAe,CAAC,QAAQ;AAC3B,SAAK,WAAW,IAAI,EAAE;AAAA,EACxB;AAAA,EACA,OAAO,UAAU,cAAc;AAC7B,QAAI,KAAK,uBAAuB,MAAM;AACpC,SAAG,oBAAoB,cAAc,CAAC;AAAA,IACxC,OAAO;AACL,SAAG,oBAAoB,cAAc,CAAC;AACtC,SAAG,aAAa,KAAK,OAAO,IAAI,EAAE;AAAA,IACpC;AACA,OAAG,eAAe,QAAQ;AAC1B,SAAK,WAAW,IAAI,EAAE;AAAA,EACxB;AAAA;AAAA,EAGA,KAAK,QAAQ,QAAQ;AACnB,UAAM,UAAU,KAAK;AACrB,QAAI,KAAK,OAAO,qBAAqB;AAEnC,YAAM,WAAW,KAAK,OAAO;AAC7B,SAAG,KAAK,QAAQ,EAAE,IAAI,KAAK,MAAM;AACjC,UAAI,iBAAiB,GAAG,OAAO;AAG/B,wBAAkB,KAAK,IAAI,KAAK,OAAO,MAAM,IAAI,KAAK,KAAK,GAAK;AAGhE,WAAK,SAAS,IAAI,SAAS,iBAAiB,QAAQ,cAAc,KAAK,OAAO,MAAM;AACpF,WAAK,OAAO,IAAI,SAAS,iBAAiB,QAAQ,cAAc,KAAK,OAAO,MAAM;AAAA,IACpF,WAAW,KAAK,OAAO,sBAAsB;AAE3C,WAAK,SAAS,UAAU,KAAK,OAAO,QAAQ,KAAK,OAAO,QAAQ,KAAK,OAAO,OAAO,QAAQ,aAAa,KAAK,OAAO,MAAM;AAC1H,WAAK,OAAO,UAAU,KAAK,OAAO,MAAM,KAAK,OAAO,UAAU,KAAK,OAAO,OAAO,QAAQ,cAAc,KAAK,OAAO,MAAM;AAAA,IAC3H,OAAO;AAEL,cAAQ,KAAK,8EAA8E;AAC3F,WAAK,YAAY;AAAA,IACnB;AAAA,EACF;AAAA,EACA,UAAU,YAAY;AACpB,QAAI,KAAK,OAAO,uBAAuB,KAAK,OAAO,sBAAsB;AACvE,WAAK,UAAU;AAAA,IACjB,OAAO;AACL,cAAQ,KAAK,qFAAqF;AAClG,WAAK,aAAa;AAAA,IACpB;AAAA,EACF;AAAA,EACA,SAAS,YAAY;AACnB,QAAI,KAAK,OAAO,uBAAuB,KAAK,OAAO,sBAAsB;AACvE,WAAK,UAAU;AAAA,IACjB,OAAO;AACL,cAAQ,KAAK,qFAAqF;AAClG,WAAK,aAAa;AAAA,IACpB;AAAA,EACF;AAAA,EACA,sBAAsB,GAAG,GAAG;AAC1B,QAAI,CAAC,KAAK,cAAc;AACtB;AAAA,IACF;AACA,SAAK,qBAAqB;AAC1B,UAAM,OAAO,KAAK,WAAW,sBAAsB;AACnD,UAAM,KAAK,IAAI,KAAK;AACpB,UAAM,KAAK,IAAI,KAAK;AACpB,UAAM,IAAI,KAAK;AACf,UAAM,IAAI,KAAK;AACf,SAAK,OAAO,IAAI,KAAK,IAAI,IAAI;AAC7B,SAAK,OAAO,IAAI,EAAE,KAAK,KAAK,IAAI;AAChC,SAAK,gBAAgB,IAAI,KAAK,OAAO,GAAG,KAAK,OAAO,GAAG,CAAC,EAAE,UAAU,KAAK,MAAM,EAAE,IAAI,KAAK,OAAO,QAAQ,EAAE,UAAU;AAAA,EACvH;AAAA,EACA,eAAe,MAAM;AACnB,WAAO,KAAK,IAAI,KAAK,aAAa,KAAK,IAAI,KAAK,aAAa,IAAI,CAAC;AAAA,EACpE;AAAA;AAAA;AAAA;AAAA,EAMA,uBAAuB,OAAO;AAC5B,SAAK,aAAa,IAAI,MAAM,SAAS,MAAM,OAAO;AAAA,EACpD;AAAA,EACA,sBAAsB,OAAO;AAC3B,SAAK,sBAAsB,MAAM,SAAS,MAAM,OAAO;AACvD,SAAK,YAAY,IAAI,MAAM,SAAS,MAAM,OAAO;AAAA,EACnD;AAAA,EACA,oBAAoB,OAAO;AACzB,SAAK,UAAU,IAAI,MAAM,SAAS,MAAM,OAAO;AAAA,EACjD;AAAA,EACA,uBAAuB,OAAO;AAC5B,SAAK,WAAW,IAAI,MAAM,SAAS,MAAM,OAAO;AAChD,SAAK,aAAa,WAAW,KAAK,YAAY,KAAK,YAAY,EAAE,eAAe,KAAK,WAAW;AAChG,UAAM,UAAU,KAAK;AACrB,SAAK,YAAY,SAAS,KAAK,aAAa,IAAI,QAAQ,YAAY;AAEpE,SAAK,UAAU,SAAS,KAAK,aAAa,IAAI,QAAQ,YAAY;AAClE,SAAK,aAAa,KAAK,KAAK,UAAU;AACtC,SAAK,OAAO;AAAA,EACd;AAAA,EACA,sBAAsB,OAAO;AAC3B,SAAK,UAAU,IAAI,MAAM,SAAS,MAAM,OAAO;AAC/C,SAAK,YAAY,WAAW,KAAK,WAAW,KAAK,WAAW;AAC5D,QAAI,KAAK,YAAY,IAAI,GAAG;AAC1B,WAAK,UAAU,KAAK,cAAc,KAAK,YAAY,CAAC,CAAC;AAAA,IACvD,WAAW,KAAK,YAAY,IAAI,GAAG;AACjC,WAAK,SAAS,KAAK,cAAc,KAAK,YAAY,CAAC,CAAC;AAAA,IACtD;AACA,SAAK,YAAY,KAAK,KAAK,SAAS;AACpC,SAAK,OAAO;AAAA,EACd;AAAA,EACA,oBAAoB,OAAO;AACzB,SAAK,QAAQ,IAAI,MAAM,SAAS,MAAM,OAAO;AAC7C,SAAK,UAAU,WAAW,KAAK,SAAS,KAAK,SAAS,EAAE,eAAe,KAAK,QAAQ;AACpF,SAAK,KAAK,KAAK,UAAU,GAAG,KAAK,UAAU,CAAC;AAC5C,SAAK,UAAU,KAAK,KAAK,OAAO;AAChC,SAAK,OAAO;AAAA,EACd;AAAA,EACA,kBAAkB,OAAO;AACvB,SAAK,sBAAsB,MAAM,SAAS,MAAM,OAAO;AACvD,QAAI,MAAM,SAAS,GAAG;AACpB,WAAK,SAAS,KAAK,cAAc,MAAM,MAAM,CAAC;AAAA,IAChD,WAAW,MAAM,SAAS,GAAG;AAC3B,WAAK,UAAU,KAAK,cAAc,MAAM,MAAM,CAAC;AAAA,IACjD;AACA,SAAK,OAAO;AAAA,EACd;AAAA,EACA,eAAe,OAAO;AACpB,QAAI,cAAc;AAClB,YAAQ,MAAM,MAAM;AAAA,MAClB,KAAK,KAAK,KAAK;AACb,YAAI,MAAM,WAAW,MAAM,WAAW,MAAM,UAAU;AACpD,cAAI,KAAK,cAAc;AACrB,iBAAK,UAAU,SAAS,KAAK,iBAAiB,KAAK,WAAW,YAAY;AAAA,UAC5E;AAAA,QACF,OAAO;AACL,cAAI,KAAK,WAAW;AAClB,iBAAK,KAAK,GAAG,KAAK,WAAW;AAAA,UAC/B;AAAA,QACF;AACA,sBAAc;AACd;AAAA,MACF,KAAK,KAAK,KAAK;AACb,YAAI,MAAM,WAAW,MAAM,WAAW,MAAM,UAAU;AACpD,cAAI,KAAK,cAAc;AACrB,iBAAK,UAAU,CAAC,SAAS,KAAK,iBAAiB,KAAK,WAAW,YAAY;AAAA,UAC7E;AAAA,QACF,OAAO;AACL,cAAI,KAAK,WAAW;AAClB,iBAAK,KAAK,GAAG,CAAC,KAAK,WAAW;AAAA,UAChC;AAAA,QACF;AACA,sBAAc;AACd;AAAA,MACF,KAAK,KAAK,KAAK;AACb,YAAI,MAAM,WAAW,MAAM,WAAW,MAAM,UAAU;AACpD,cAAI,KAAK,cAAc;AACrB,iBAAK,YAAY,SAAS,KAAK,iBAAiB,KAAK,WAAW,YAAY;AAAA,UAC9E;AAAA,QACF,OAAO;AACL,cAAI,KAAK,WAAW;AAClB,iBAAK,KAAK,KAAK,aAAa,CAAC;AAAA,UAC/B;AAAA,QACF;AACA,sBAAc;AACd;AAAA,MACF,KAAK,KAAK,KAAK;AACb,YAAI,MAAM,WAAW,MAAM,WAAW,MAAM,UAAU;AACpD,cAAI,KAAK,cAAc;AACrB,iBAAK,YAAY,CAAC,SAAS,KAAK,iBAAiB,KAAK,WAAW,YAAY;AAAA,UAC/E;AAAA,QACF,OAAO;AACL,cAAI,KAAK,WAAW;AAClB,iBAAK,KAAK,CAAC,KAAK,aAAa,CAAC;AAAA,UAChC;AAAA,QACF;AACA,sBAAc;AACd;AAAA,IACJ;AACA,QAAI,aAAa;AAEf,YAAM,eAAe;AACrB,WAAK,OAAO;AAAA,IACd;AAAA,EACF;AAAA,EACA,wBAAwB,OAAO;AAC7B,QAAI,KAAK,UAAU,WAAW,GAAG;AAC/B,WAAK,aAAa,IAAI,MAAM,OAAO,MAAM,KAAK;AAAA,IAChD,OAAO;AACL,YAAM,WAAW,KAAK,0BAA0B,KAAK;AACrD,YAAM,IAAI,OAAO,MAAM,QAAQ,SAAS;AACxC,YAAM,IAAI,OAAO,MAAM,QAAQ,SAAS;AACxC,WAAK,aAAa,IAAI,GAAG,CAAC;AAAA,IAC5B;AAAA,EACF;AAAA,EACA,qBAAqB,OAAO;AAC1B,QAAI,KAAK,UAAU,WAAW,GAAG;AAC/B,WAAK,UAAU,IAAI,MAAM,OAAO,MAAM,KAAK;AAAA,IAC7C,OAAO;AACL,YAAM,WAAW,KAAK,0BAA0B,KAAK;AACrD,YAAM,IAAI,OAAO,MAAM,QAAQ,SAAS;AACxC,YAAM,IAAI,OAAO,MAAM,QAAQ,SAAS;AACxC,WAAK,UAAU,IAAI,GAAG,CAAC;AAAA,IACzB;AAAA,EACF;AAAA,EACA,uBAAuB,OAAO;AAC5B,UAAM,WAAW,KAAK,0BAA0B,KAAK;AACrD,UAAM,KAAK,MAAM,QAAQ,SAAS;AAClC,UAAM,KAAK,MAAM,QAAQ,SAAS;AAClC,UAAM,WAAW,KAAK,KAAK,KAAK,KAAK,KAAK,EAAE;AAC5C,SAAK,YAAY,IAAI,GAAG,QAAQ;AAAA,EAClC;AAAA,EACA,0BAA0B,OAAO;AAC/B,QAAI,KAAK,WAAY,MAAK,uBAAuB,KAAK;AACtD,QAAI,KAAK,UAAW,MAAK,qBAAqB,KAAK;AAAA,EACrD;AAAA,EACA,6BAA6B,OAAO;AAClC,QAAI,KAAK,WAAY,MAAK,uBAAuB,KAAK;AACtD,QAAI,KAAK,aAAc,MAAK,wBAAwB,KAAK;AAAA,EAC3D;AAAA,EACA,uBAAuB,OAAO;AAC5B,QAAI,KAAK,UAAU,UAAU,GAAG;AAC9B,WAAK,WAAW,IAAI,MAAM,OAAO,MAAM,KAAK;AAAA,IAC9C,OAAO;AACL,YAAM,WAAW,KAAK,0BAA0B,KAAK;AACrD,YAAM,IAAI,OAAO,MAAM,QAAQ,SAAS;AACxC,YAAM,IAAI,OAAO,MAAM,QAAQ,SAAS;AACxC,WAAK,WAAW,IAAI,GAAG,CAAC;AAAA,IAC1B;AACA,SAAK,aAAa,WAAW,KAAK,YAAY,KAAK,YAAY,EAAE,eAAe,KAAK,WAAW;AAChG,UAAM,UAAU,KAAK;AACrB,SAAK,YAAY,SAAS,KAAK,aAAa,IAAI,QAAQ,YAAY;AAEpE,SAAK,UAAU,SAAS,KAAK,aAAa,IAAI,QAAQ,YAAY;AAClE,SAAK,aAAa,KAAK,KAAK,UAAU;AAAA,EACxC;AAAA,EACA,oBAAoB,OAAO;AACzB,QAAI,KAAK,UAAU,WAAW,GAAG;AAC/B,WAAK,QAAQ,IAAI,MAAM,OAAO,MAAM,KAAK;AAAA,IAC3C,OAAO;AACL,YAAM,WAAW,KAAK,0BAA0B,KAAK;AACrD,YAAM,IAAI,OAAO,MAAM,QAAQ,SAAS;AACxC,YAAM,IAAI,OAAO,MAAM,QAAQ,SAAS;AACxC,WAAK,QAAQ,IAAI,GAAG,CAAC;AAAA,IACvB;AACA,SAAK,UAAU,WAAW,KAAK,SAAS,KAAK,SAAS,EAAE,eAAe,KAAK,QAAQ;AACpF,SAAK,KAAK,KAAK,UAAU,GAAG,KAAK,UAAU,CAAC;AAC5C,SAAK,UAAU,KAAK,KAAK,OAAO;AAAA,EAClC;AAAA,EACA,sBAAsB,OAAO;AAC3B,UAAM,WAAW,KAAK,0BAA0B,KAAK;AACrD,UAAM,KAAK,MAAM,QAAQ,SAAS;AAClC,UAAM,KAAK,MAAM,QAAQ,SAAS;AAClC,UAAM,WAAW,KAAK,KAAK,KAAK,KAAK,KAAK,EAAE;AAC5C,SAAK,UAAU,IAAI,GAAG,QAAQ;AAC9B,SAAK,YAAY,IAAI,GAAG,KAAK,IAAI,KAAK,UAAU,IAAI,KAAK,YAAY,GAAG,KAAK,SAAS,CAAC;AACvF,SAAK,UAAU,KAAK,YAAY,CAAC;AACjC,SAAK,YAAY,KAAK,KAAK,SAAS;AACpC,UAAM,WAAW,MAAM,QAAQ,SAAS,KAAK;AAC7C,UAAM,WAAW,MAAM,QAAQ,SAAS,KAAK;AAC7C,SAAK,sBAAsB,SAAS,OAAO;AAAA,EAC7C;AAAA,EACA,yBAAyB,OAAO;AAC9B,QAAI,KAAK,WAAY,MAAK,sBAAsB,KAAK;AACrD,QAAI,KAAK,UAAW,MAAK,oBAAoB,KAAK;AAAA,EACpD;AAAA,EACA,4BAA4B,OAAO;AACjC,QAAI,KAAK,WAAY,MAAK,sBAAsB,KAAK;AACrD,QAAI,KAAK,aAAc,MAAK,uBAAuB,KAAK;AAAA,EAC1D;AAAA;AAAA,EAIA,YAAY,OAAO;AACjB,SAAK,UAAU,KAAK,MAAM,SAAS;AAAA,EACrC;AAAA,EACA,eAAe,OAAO;AACpB,WAAO,KAAK,kBAAkB,MAAM,SAAS;AAC7C,aAAS,IAAI,GAAG,IAAI,KAAK,UAAU,QAAQ,KAAK;AAC9C,UAAI,KAAK,UAAU,CAAC,KAAK,MAAM,WAAW;AACxC,aAAK,UAAU,OAAO,GAAG,CAAC;AAC1B;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,mBAAmB,OAAO;AACxB,aAAS,IAAI,GAAG,IAAI,KAAK,UAAU,QAAQ,KAAK;AAC9C,UAAI,KAAK,UAAU,CAAC,KAAK,MAAM,UAAW,QAAO;AAAA,IACnD;AACA,WAAO;AAAA,EACT;AAAA,EACA,cAAc,OAAO;AACnB,QAAI,WAAW,KAAK,kBAAkB,MAAM,SAAS;AACrD,QAAI,aAAa,QAAW;AAC1B,iBAAW,IAAI,QAAQ;AACvB,WAAK,kBAAkB,MAAM,SAAS,IAAI;AAAA,IAC5C;AACA,aAAS,IAAI,MAAM,OAAO,MAAM,KAAK;AAAA,EACvC;AAAA,EACA,0BAA0B,OAAO;AAC/B,UAAM,YAAY,MAAM,cAAc,KAAK,UAAU,CAAC,IAAI,KAAK,UAAU,CAAC,IAAI,KAAK,UAAU,CAAC;AAC9F,WAAO,KAAK,kBAAkB,SAAS;AAAA,EACzC;AAAA;AAAA,EAIA,kBAAkB,OAAO;AACvB,UAAM,OAAO,MAAM;AAGnB,UAAM,WAAW;AAAA,MACf,SAAS,MAAM;AAAA,MACf,SAAS,MAAM;AAAA,MACf,QAAQ,MAAM;AAAA,IAChB;AACA,YAAQ,MAAM;AAAA,MACZ,KAAK;AAEH,iBAAS,UAAU;AACnB;AAAA,MACF,KAAK;AAEH,iBAAS,UAAU;AACnB;AAAA,IACJ;AAGA,QAAI,MAAM,WAAW,CAAC,KAAK,gBAAgB;AACzC,eAAS,UAAU;AAAA,IACrB;AACA,WAAO;AAAA,EACT;AACF;AACA,SAAS,cAAc,OAAO;AAC5B,MAAI,KAAK,YAAY,MAAO;AAC5B,MAAI,KAAK,UAAU,WAAW,GAAG;AAC/B,SAAK,WAAW,kBAAkB,MAAM,SAAS;AACjD,SAAK,WAAW,iBAAiB,eAAe,KAAK,cAAc;AACnE,SAAK,WAAW,iBAAiB,aAAa,KAAK,YAAY;AAAA,EACjE;AAIA,MAAI,KAAK,mBAAmB,KAAK,EAAG;AAIpC,OAAK,YAAY,KAAK;AACtB,MAAI,MAAM,gBAAgB,SAAS;AACjC,SAAK,cAAc,KAAK;AAAA,EAC1B,OAAO;AACL,SAAK,aAAa,KAAK;AAAA,EACzB;AACF;AACA,SAAS,cAAc,OAAO;AAC5B,MAAI,KAAK,YAAY,MAAO;AAC5B,MAAI,MAAM,gBAAgB,SAAS;AACjC,SAAK,aAAa,KAAK;AAAA,EACzB,OAAO;AACL,SAAK,aAAa,KAAK;AAAA,EACzB;AACF;AACA,SAAS,YAAY,OAAO;AAC1B,OAAK,eAAe,KAAK;AACzB,UAAQ,KAAK,UAAU,QAAQ;AAAA,IAC7B,KAAK;AACH,WAAK,WAAW,sBAAsB,MAAM,SAAS;AACrD,WAAK,WAAW,oBAAoB,eAAe,KAAK,cAAc;AACtE,WAAK,WAAW,oBAAoB,aAAa,KAAK,YAAY;AAClE,WAAK,cAAc,SAAS;AAC5B,WAAK,QAAQ,OAAO;AACpB;AAAA,IACF,KAAK;AACH,YAAM,YAAY,KAAK,UAAU,CAAC;AAClC,YAAM,WAAW,KAAK,kBAAkB,SAAS;AAGjD,WAAK,cAAc;AAAA,QACjB;AAAA,QACA,OAAO,SAAS;AAAA,QAChB,OAAO,SAAS;AAAA,MAClB,CAAC;AACD;AAAA,EACJ;AACF;AACA,SAAS,YAAY,OAAO;AAC1B,MAAI;AACJ,UAAQ,MAAM,QAAQ;AAAA,IACpB,KAAK;AACH,oBAAc,KAAK,aAAa;AAChC;AAAA,IACF,KAAK;AACH,oBAAc,KAAK,aAAa;AAChC;AAAA,IACF,KAAK;AACH,oBAAc,KAAK,aAAa;AAChC;AAAA,IACF;AACE,oBAAc;AAAA,EAClB;AACA,UAAQ,aAAa;AAAA,IACnB,KAAK,MAAM;AACT,UAAI,KAAK,eAAe,MAAO;AAC/B,WAAK,sBAAsB,KAAK;AAChC,WAAK,QAAQ,OAAO;AACpB;AAAA,IACF,KAAK,MAAM;AACT,UAAI,MAAM,WAAW,MAAM,WAAW,MAAM,UAAU;AACpD,YAAI,KAAK,cAAc,MAAO;AAC9B,aAAK,oBAAoB,KAAK;AAC9B,aAAK,QAAQ,OAAO;AAAA,MACtB,OAAO;AACL,YAAI,KAAK,iBAAiB,MAAO;AACjC,aAAK,uBAAuB,KAAK;AACjC,aAAK,QAAQ,OAAO;AAAA,MACtB;AACA;AAAA,IACF,KAAK,MAAM;AACT,UAAI,MAAM,WAAW,MAAM,WAAW,MAAM,UAAU;AACpD,YAAI,KAAK,iBAAiB,MAAO;AACjC,aAAK,uBAAuB,KAAK;AACjC,aAAK,QAAQ,OAAO;AAAA,MACtB,OAAO;AACL,YAAI,KAAK,cAAc,MAAO;AAC9B,aAAK,oBAAoB,KAAK;AAC9B,aAAK,QAAQ,OAAO;AAAA,MACtB;AACA;AAAA,IACF;AACE,WAAK,QAAQ,OAAO;AAAA,EACxB;AACA,MAAI,KAAK,UAAU,OAAO,MAAM;AAC9B,SAAK,cAAc,WAAW;AAAA,EAChC;AACF;AACA,SAAS,YAAY,OAAO;AAC1B,UAAQ,KAAK,OAAO;AAAA,IAClB,KAAK,OAAO;AACV,UAAI,KAAK,iBAAiB,MAAO;AACjC,WAAK,uBAAuB,KAAK;AACjC;AAAA,IACF,KAAK,OAAO;AACV,UAAI,KAAK,eAAe,MAAO;AAC/B,WAAK,sBAAsB,KAAK;AAChC;AAAA,IACF,KAAK,OAAO;AACV,UAAI,KAAK,cAAc,MAAO;AAC9B,WAAK,oBAAoB,KAAK;AAC9B;AAAA,EACJ;AACF;AACA,SAAS,aAAa,OAAO;AAC3B,MAAI,KAAK,YAAY,SAAS,KAAK,eAAe,SAAS,KAAK,UAAU,OAAO,KAAM;AACvF,QAAM,eAAe;AACrB,OAAK,cAAc,WAAW;AAC9B,OAAK,kBAAkB,KAAK,kBAAkB,KAAK,CAAC;AACpD,OAAK,cAAc,SAAS;AAC9B;AACA,SAAS,UAAU,OAAO;AACxB,MAAI,KAAK,YAAY,MAAO;AAC5B,OAAK,eAAe,KAAK;AAC3B;AACA,SAAS,aAAa,OAAO;AAC3B,OAAK,cAAc,KAAK;AACxB,UAAQ,KAAK,UAAU,QAAQ;AAAA,IAC7B,KAAK;AACH,cAAQ,KAAK,QAAQ,KAAK;AAAA,QACxB,KAAK,MAAM;AACT,cAAI,KAAK,iBAAiB,MAAO;AACjC,eAAK,wBAAwB,KAAK;AAClC,eAAK,QAAQ,OAAO;AACpB;AAAA,QACF,KAAK,MAAM;AACT,cAAI,KAAK,cAAc,MAAO;AAC9B,eAAK,qBAAqB,KAAK;AAC/B,eAAK,QAAQ,OAAO;AACpB;AAAA,QACF;AACE,eAAK,QAAQ,OAAO;AAAA,MACxB;AACA;AAAA,IACF,KAAK;AACH,cAAQ,KAAK,QAAQ,KAAK;AAAA,QACxB,KAAK,MAAM;AACT,cAAI,KAAK,eAAe,SAAS,KAAK,cAAc,MAAO;AAC3D,eAAK,0BAA0B,KAAK;AACpC,eAAK,QAAQ,OAAO;AACpB;AAAA,QACF,KAAK,MAAM;AACT,cAAI,KAAK,eAAe,SAAS,KAAK,iBAAiB,MAAO;AAC9D,eAAK,6BAA6B,KAAK;AACvC,eAAK,QAAQ,OAAO;AACpB;AAAA,QACF;AACE,eAAK,QAAQ,OAAO;AAAA,MACxB;AACA;AAAA,IACF;AACE,WAAK,QAAQ,OAAO;AAAA,EACxB;AACA,MAAI,KAAK,UAAU,OAAO,MAAM;AAC9B,SAAK,cAAc,WAAW;AAAA,EAChC;AACF;AACA,SAAS,YAAY,OAAO;AAC1B,OAAK,cAAc,KAAK;AACxB,UAAQ,KAAK,OAAO;AAAA,IAClB,KAAK,OAAO;AACV,UAAI,KAAK,iBAAiB,MAAO;AACjC,WAAK,uBAAuB,KAAK;AACjC,WAAK,OAAO;AACZ;AAAA,IACF,KAAK,OAAO;AACV,UAAI,KAAK,cAAc,MAAO;AAC9B,WAAK,oBAAoB,KAAK;AAC9B,WAAK,OAAO;AACZ;AAAA,IACF,KAAK,OAAO;AACV,UAAI,KAAK,eAAe,SAAS,KAAK,cAAc,MAAO;AAC3D,WAAK,yBAAyB,KAAK;AACnC,WAAK,OAAO;AACZ;AAAA,IACF,KAAK,OAAO;AACV,UAAI,KAAK,eAAe,SAAS,KAAK,iBAAiB,MAAO;AAC9D,WAAK,4BAA4B,KAAK;AACtC,WAAK,OAAO;AACZ;AAAA,IACF;AACE,WAAK,QAAQ,OAAO;AAAA,EACxB;AACF;AACA,SAAS,cAAc,OAAO;AAC5B,MAAI,KAAK,YAAY,MAAO;AAC5B,QAAM,eAAe;AACvB;AACA,SAAS,qBAAqB,OAAO;AACnC,MAAI,MAAM,QAAQ,WAAW;AAC3B,SAAK,iBAAiB;AACtB,UAAM,WAAW,KAAK,WAAW,YAAY;AAE7C,aAAS,iBAAiB,SAAS,KAAK,qBAAqB;AAAA,MAC3D,SAAS;AAAA,MACT,SAAS;AAAA,IACX,CAAC;AAAA,EACH;AACF;AACA,SAAS,mBAAmB,OAAO;AACjC,MAAI,MAAM,QAAQ,WAAW;AAC3B,SAAK,iBAAiB;AACtB,UAAM,WAAW,KAAK,WAAW,YAAY;AAE7C,aAAS,oBAAoB,SAAS,KAAK,qBAAqB;AAAA,MAC9D,SAAS;AAAA,MACT,SAAS;AAAA,IACX,CAAC;AAAA,EACH;AACF;", "names": []}