enum EtatSinistre {
    OUVERT, SANSSUITE, DOSSIERTRADIVE
}

enum TypePersonne {
    PERSONNE_MORALE, PERSONNE_PHYSIQUE
}

enum NatureTiers {
    AD, AT, AY, BY, CF, CH, MO, PI, TR, VH
}

enum TypeDegats {
    MATERIEL, BLESSE, OBJET, AUTRE
}

enum TypeSinistre {
    MATRIEL, CORPOREL
}

enum RoleUtilisateur {
    AGENT, ADMIN, GESTIONNAIRE
}

enum CategorieDuPermis {
    TUNISIEN, ETRANGER
}

enum TypeNotification {
    SMS , EMAIL , SYSTEME
}
enum TypeDocument {
 CONSTAT , PHOTOVHICULE , CLAUSESIGNE, AVIS MEDECIN , PERMISCONDUITE , CIN , AUTRE
}
entity Clause999{
 dateSignature ZonedDateTime
 contenu String  
}

entity Notification {
    type TypeNotification
    contenu String 
    dateCreation Instant
    destination RoleUtilisateur
}

entity ConfigurationDocument {
    obligatoire Boolean
    seulDegats BigDecimal
    avecTiers Boolean
    avecRecours Boolean
}
entity Sinistre {
    id Integer
    numSinistre String
    dateDeclaration ZonedDateTime
    dateSurvenance ZonedDateTime
    dateOuvertureSinistre ZonedDateTime
    adresse String
    etat EtatSinistre
    coutEstime BigDecimal
    coutMoyen BigDecimal
    descriptionDegats String maxlength(256)
    typeDegat TypeDegats
    responsable Integer min(0) max(4)
    tierRequis Boolean
    conducteurAssure Boolean
    couvertureContrat Boolean
    constatAttache Boolean
    photosVehicule String 
    casDeBareme String
    tiersCompagnieAssurance String
    numeroContrat String
    immatriculation String
    cinAssure String
    nomPrenomAssure String
    dateNaissanceAssure LocalDate
    immatriculationAssure String
    differentDeAssure Boolean 
    nomPrenomTiers String
    cinTiers String
    dateNaissanceTiers LocalDate
    immatriculationTiers String
    listeGaranties String
    uploadDate ZonedDateTime
}

entity Contrat999 {
    id Integer
    numContrat String maxlength(20)
    immatriculation String
    dateEffet LocalDate
    dateFin LocalDate
    produit String
}

entity Evenement {
    id Integer
    code String maxlength(10)
    libelle String maxlength(255)
    typeDoc String
}

entity DocumentSnistre {
    id Integer
    dateUpload ZonedDateTime
    fichier FilePath
}

entity Tiers {
    id Integer
    nom String maxlength(30)
    prenom String maxlength(30)
    immatriculationTiers String maxlength(20)
    numPermis String
    dateNaissance LocalDate
    compagnieAssurance String
    responsabilite Integer min(0) max(4)
    sexeTiers String
    nature NatureTiers
    description String
    numContratTiers Integer
    numdesinistreTiers Integer
    dateDobtentionTiers Date
    professionTier String
    nombreDejourDerepos Integer
    tauxDincapacite Integer
    accompagnantTiers String 
    degreDePrejudiceMorale String
    sinistreTraite Boolean
    lesAyantDroit String
    nombreDesAyantDroit Integer
    dateDenaissanceDesAyantDroit Date
    ageDesAyantDroit Integer
}

entity Garantie999 {
    id Integer
    code String maxlength(5) 
    libelle String maxlength(100)
    plafond BigDecimal
    franchise BigDecimal
    plafondCouvert String
}

entity Adresse {
    id Integer
    codePostal String
    gouvernorat String
    cite String
    region String
}

entity Client999 {
    id Long
    nom String
    prenom String
    typePersonne TypePersonne
    matriculeFiscal String
    cin String
}

entity CasDeBareme {
    id Integer
    code String
    libelle String
    resX Integer
    resY Integer
}

entity Degat {
    id Integer
    code String
    libele String
    typeDegat TypeDegats
    typeSinistre TypeSinistre
}

entity Conducteur {
    id Integer
    vhiculeEnStationement Boolean
    conducteurMemeAssure Boolean
    nom String
    prenom String
    categorieDuPermis CategorieDuPermis
    numeroDuPermis Integer
    dateDeNaissance Date
    dateObtentionPermis Date 
}

entity Utilisateur999 {
    id Integer
    username String
    password String
    role RoleUtilisateur
    permissions String
}

entity CoutMoyen {
    id Integer
    cout BigDecimal
}

entity ConfigEvenement {
    id Integer
    delaiDeclaration Integer
    tiersRequis Boolean
    responsabilite String
   
}

entity TypeDocument999 {
    id Integer
    code String
    libelle String
}

relationship OneToOne {
    Adresse{sinistre} to Sinistre
    Adresse{tiers} to Tiers
    Sinistre{coutMoyen} to CoutMoyen
    Clause{document} to DocumentSnistre
    Evenement{degat} to Degat
    

}

relationship ManyToOne {
    Sinistre{contrat} to Contrat999
    Sinistre{evenement} to Evenement
    Sinistre{client} to Client999
    Tiers{sinistre} to Sinistre
    Conducteur{sinistre} to Sinistre
    DocumentSnistre{sinistre} to Sinistre
    DocumentSnistre{contrat} to Contrat999
    Garantie999{contrat} to Contrat999
    ConfigEvenement{evenement} to Evenement
    Evenement{typeDocument} to TypeDocument999
    Notification{utilisateur} to Utilisateur999
    ConfigurationDocument{typeDocument} to TypeDocument999
    ConfigurationDocument{typeSinistre} to TypeSinistre
    ConfigurationDocument{typeDegat} to TypeDegats
    ConfigurationDocument{etatSinistre} to EtatSinistre
    Clause999{sinistre} to Sinistre
    CasDeBareme {casDeBareme} to Sinistre
}

relationship OneToMany {
    Contrat999{garanties} to Garantie999
    Evenement{documents} to DocumentSnistre
    Evenement{configs} to ConfigEvenement
    ConfigEvenement {garantieCouvrant} to Garantie999
    TypeDocument999{configurations} to ConfigurationDocument
}

relationship ManyToMany {
    Sinistre{documents} to DocumentSnistre
    Sinistre{tiers} to Tiers
    Sinistre{garanties} to Garantie999
}

dto * with mapstruct
service * with serviceImpl
paginate * with pagination
