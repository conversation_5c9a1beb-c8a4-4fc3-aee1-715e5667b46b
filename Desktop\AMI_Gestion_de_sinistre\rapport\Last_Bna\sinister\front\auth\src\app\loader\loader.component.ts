import { AfterViewInit, Component, ElementRef, Renderer2, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import * as THREE from 'three';
import { MTLLoader } from 'three/examples/jsm/loaders/MTLLoader.js';
import { OBJLoader } from 'three/examples/jsm/loaders/OBJLoader.js';

// ... imports stay the same

@Component({
  selector: 'app-loader',
  template: `<div #loaderCanvas class="loader-canvas"></div>`,
  styleUrls: ['./loader.component.css']
})
export class LoaderComponent implements AfterViewInit {
  @ViewChild('loaderCanvas', { static: false }) loaderCanvasRef!: ElementRef;

  constructor(private router: Router, private renderer2: Renderer2) {}

  ngAfterViewInit(): void {
    // Check if we're in browser environment
    if (typeof window === 'undefined' || typeof document === 'undefined') {
      console.log('SSR environment detected, skipping Three.js initialization');
      // Fallback for SSR - just navigate to login after delay
      setTimeout(() => {
        this.router.navigate(['/login']);
      }, 2000);
      return;
    }

    const container = this.loaderCanvasRef.nativeElement;

    const scene = new THREE.Scene();
    scene.background = new THREE.Color(0xffffff);

    const camera = new THREE.PerspectiveCamera(75, container.clientWidth / container.clientHeight, 0.1, 1000);
    camera.position.z = 5;

    const renderer = new THREE.WebGLRenderer({ alpha: false, antialias: true });
    renderer.setSize(container.clientWidth, container.clientHeight);
    renderer.shadowMap.enabled = true;
    container.appendChild(renderer.domElement);

    const ambientLight = new THREE.AmbientLight(0xffffff, 0.8);
    scene.add(ambientLight);

    const directionalLight = new THREE.DirectionalLight(0xffffff, 1.5);
    directionalLight.position.set(5, 10, 7);
    directionalLight.castShadow = true;
    scene.add(directionalLight);

    const spotLight = new THREE.SpotLight(0xffffff, 1);
    spotLight.position.set(0, 10, 10);
    spotLight.castShadow = true;
    scene.add(spotLight);

    const ground = new THREE.PlaneGeometry(100, 100);
    const groundMaterial = new THREE.ShadowMaterial({ opacity: 0.25 });
    const groundMesh = new THREE.Mesh(ground, groundMaterial);
    groundMesh.rotation.x = -Math.PI / 2;
    groundMesh.position.y = -1.5;
    groundMesh.receiveShadow = true;
    scene.add(groundMesh);

    const mtlLoader = new MTLLoader();
    mtlLoader.load('assets/logo.mtl', (materials) => {
      materials.preload();

      const objLoader = new OBJLoader();
      objLoader.setMaterials(materials);
      objLoader.load('assets/logo.obj', (obj) => {
        obj.traverse((child: any) => {
          if (child.isMesh) {
            child.castShadow = true;
            child.receiveShadow = true;
          }
        });

        obj.scale.set(0.2, 0.2, 0.2); // Start small
        obj.position.y = 0.5;
        scene.add(obj);

        let rotationSpeed = 0.05; // Slower initial rotation
        let targetScale = 3;
        let scale = 0.2;
        let scaleSpeed = 0.05;
        let timeElapsed = 0;

        const animate = () => {
          requestAnimationFrame(animate);

          timeElapsed += 0.016;

          // Scale easing
          if (scale < targetScale) {
            scale += scaleSpeed;
            scaleSpeed *= 0.95;
          }

          // Gradually stop rotation after 4 seconds
          if (timeElapsed >= 4) {
            rotationSpeed *= 0.90;
            if (rotationSpeed < 0.001) rotationSpeed = 0;
          }

          obj.scale.set(scale, scale, scale);
          obj.rotation.y += rotationSpeed;

          renderer.render(scene, camera);
        };

        animate();

        setTimeout(() => {
          this.fadeOutCanvas(container, () => {
            this.router.navigate(['/login']);
          });
        }, 5000);
      });
    });
  }

  fadeOutCanvas(element: HTMLElement, callback: () => void) {
    this.renderer2.setStyle(element, 'transition', 'opacity 1s ease-in-out, transform 1s ease-in-out');
    this.renderer2.setStyle(element, 'opacity', '0');
    this.renderer2.setStyle(element, 'transform', 'scale(1.1)');
    setTimeout(callback, 1000);
  }
}
