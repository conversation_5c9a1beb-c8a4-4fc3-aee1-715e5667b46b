// src/main/java/com/fed/platform/sinister/domain/TypeDegat.java
package com.fed.platform.sinister.domain;

import jakarta.persistence.*;
import java.io.Serializable;

@Entity
@Table(name = "type_degat")
public class TypeDegat implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private TypeDegats code;

    @Column(nullable = false)
    private String libelle;

    private String description;

    // No-arg constructor
    public TypeDegat() {
    }

    // Constructor with enum
    public TypeDegat(TypeDegats code) {
        this.code = code;
        this.libelle = code.getLibelle();
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public TypeDegats getCode() {
        return code;
    }

    public void setCode(TypeDegats code) {
        this.code = code;
        if (code != null && this.libelle == null) {
            this.libelle = code.getLibelle();
        }
    }

    public String getLibelle() {
        return libelle;
    }

    public void setLibelle(String libelle) {
        this.libelle = libelle;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}