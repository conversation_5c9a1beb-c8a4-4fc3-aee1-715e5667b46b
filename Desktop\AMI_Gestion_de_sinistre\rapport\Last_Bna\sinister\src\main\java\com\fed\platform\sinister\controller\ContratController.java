package com.fed.platform.sinister.controller;

import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fed.platform.sinister.domain.Contrat;
import com.fed.platform.sinister.repository.ContratRepository;
import com.fed.platform.sinister.repository.GarantieRepository;

@RestController
@RequestMapping("/api")
@CrossOrigin(origins = "http://localhost:4200")
public class ContratController {

    private static final Logger logger = LoggerFactory.getLogger(ContratController.class);

    @Autowired
    private ContratRepository contratRepository;

    @Autowired
    private GarantieRepository garantieRepository;

    /**
     * Search contracts by immatriculation
     */
    @GetMapping("/search-contrat")
    public ResponseEntity<List<Contrat>> searchContrat(@RequestParam String immatriculation) {
        logger.info("🔍 Searching contracts by immatriculation: {}", immatriculation);

        try {
            List<Contrat> contrats = contratRepository.findByImmatriculationContainingIgnoreCase(immatriculation);
            logger.info("✅ Found {} contracts", contrats.size());
            return ResponseEntity.ok(contrats);
        } catch (Exception e) {
            logger.error("❌ Error searching contracts: {}", e.getMessage());
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Search contracts with date validation
     */
    @PostMapping("/search-contrat-with-validation")
    public ResponseEntity<List<Contrat>> searchContratWithValidation(@RequestBody Map<String, Object> request) {
        logger.info("🔍 Searching contracts with validation");

        try {
            String immatriculation = request.get("immatriculation").toString();
            String dateSurvenance = request.get("dateSurvenance").toString();

            logger.info("📋 Search criteria - Immatriculation: {}, Date survenance: {}", immatriculation,
                    dateSurvenance);

            // Convert string date to Instant for comparison
            LocalDate survenance = LocalDate.parse(dateSurvenance);
            Instant survenanceInstant = survenance.atStartOfDay(ZoneId.systemDefault()).toInstant();

            // Find contracts by immatriculation
            List<Contrat> allContrats = contratRepository.findByImmatriculationContainingIgnoreCase(immatriculation);

            // Filter contracts that cover the date de survenance
            List<Contrat> validContrats = allContrats.stream()
                    .filter(contrat -> {
                        boolean covers = !survenanceInstant.isBefore(contrat.getDateEffet()) &&
                                !survenanceInstant.isAfter(contrat.getDateFin());
                        logger.info("📅 Contract {} covers date: {}", contrat.getNumContrat(), covers);
                        return covers;
                    })
                    .collect(java.util.stream.Collectors.toList());

            logger.info("✅ Found {} valid contracts out of {} total", validContrats.size(), allContrats.size());
            return ResponseEntity.ok(validContrats);

        } catch (Exception e) {
            logger.error("❌ Error searching contracts with validation: {}", e.getMessage());
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Check if contract covers the date de survenance
     */
    @PostMapping("/check-contract-coverage")
    public ResponseEntity<Map<String, Object>> checkContractCoverage(@RequestBody Map<String, Object> request) {
        logger.info("🔍 Checking contract coverage");

        try {
            Long contratId = Long.valueOf(request.get("contratId").toString());
            String dateSurvenance = request.get("dateSurvenance").toString();

            Contrat contrat = contratRepository.findById(contratId).orElse(null);
            Map<String, Object> response = new HashMap<>();

            if (contrat == null) {
                response.put("isValid", false);
                response.put("message", "Contrat non trouvé");
                return ResponseEntity.ok(response);
            }

            // Convert string date to Instant for comparison
            LocalDate survenance = LocalDate.parse(dateSurvenance);
            Instant survenanceInstant = survenance.atStartOfDay(ZoneId.systemDefault()).toInstant();

            // Check if date de survenance is within contract period
            boolean isValid = !survenanceInstant.isBefore(contrat.getDateEffet()) &&
                    !survenanceInstant.isAfter(contrat.getDateFin());

            response.put("isValid", isValid);
            response.put("message", isValid ? "Le contrat couvre la date de survenance"
                    : "Le contrat ne couvre pas la date de survenance");

            logger.info("✅ Contract coverage check result: {}", isValid);
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("❌ Error checking contract coverage: {}", e.getMessage());
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("isValid", false);
            errorResponse.put("message", "Erreur lors de la vérification de la couverture");
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * Check delay between date de declaration and date de survenance
     */
    @PostMapping("/check-delai")
    public ResponseEntity<Map<String, Object>> checkDelai(@RequestBody Map<String, Object> request) {
        logger.info("🔍 Checking delay");

        try {
            String dateDeclaration = request.get("dateDeclaration").toString();
            String dateSurvenance = request.get("dateSurvenance").toString();

            LocalDate declaration = LocalDate.parse(dateDeclaration);
            LocalDate survenance = LocalDate.parse(dateSurvenance);

            long delaiJours = java.time.temporal.ChronoUnit.DAYS.between(survenance, declaration);
            boolean isDelaiExceeded = delaiJours > 30; // Assuming 30 days is the limit

            Map<String, Object> response = new HashMap<>();
            response.put("isDelaiExceeded", isDelaiExceeded);
            response.put("delaiJours", delaiJours);
            response.put("message",
                    isDelaiExceeded ? "Ce sinistre est déclaré hors le délai qui peut être une clause de rejet."
                            : "Le délai de déclaration est respecté.");

            logger.info("✅ Delay check result: {} days, exceeded: {}", delaiJours, isDelaiExceeded);
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("❌ Error checking delay: {}", e.getMessage());
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("isDelaiExceeded", false);
            errorResponse.put("message", "Erreur lors de la vérification des délais");
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * Check guarantee coverage for the sinistre
     */
    @PostMapping("/check-guarantee")
    public ResponseEntity<Map<String, Object>> checkGuarantee(@RequestBody Map<String, Object> request) {
        logger.info("🔍 Checking guarantee coverage");

        try {
            Long contratId = Long.valueOf(request.get("contratId").toString());
            String produit = request.get("produit").toString();
            String evenement = request.get("evenement").toString();
            String typeDegat = request.get("typeDegat").toString();

            // Mock guarantee check logic - replace with actual business logic
            boolean hasMatchingGuarantee = checkGuaranteeLogic(produit, evenement, typeDegat);

            Map<String, Object> response = new HashMap<>();
            response.put("hasMatchingGuarantee", hasMatchingGuarantee);
            response.put("guarantees",
                    hasMatchingGuarantee
                            ? List.of("Responsabilité Civile", "Dommages Matériels", "Protection Juridique")
                            : List.of());
            response.put("message", hasMatchingGuarantee ? "Des garanties couvrent ce sinistre"
                    : "Aucune garantie dans le contrat ne couvre le sinistre");

            logger.info("✅ Guarantee check result: {}", hasMatchingGuarantee);
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("❌ Error checking guarantee: {}", e.getMessage());
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("hasMatchingGuarantee", false);
            errorResponse.put("message", "Erreur lors de la vérification des garanties");
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * Save sinistre with "Sans suite" status
     */
    @PostMapping("/save-sinistre-sans-suite")
    public ResponseEntity<Map<String, Object>> saveSinistreSansSuite(@RequestBody Map<String, Object> request) {
        logger.info("💾 Saving sinistre with 'Sans suite' status");

        try {
            String numeroSinistre = request.get("numeroSinistre").toString();
            Long contratId = Long.valueOf(request.get("contratId").toString());
            String reason = request.get("reason").toString();

            // TODO: Implement actual save logic to database
            logger.info("📝 Sinistre {} saved with status 'Sans suite', reason: {}", numeroSinistre, reason);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Sinistre enregistré avec l'état 'Sans suite'");
            response.put("numeroSinistre", numeroSinistre);
            response.put("etat", "Sans suite");

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("❌ Error saving sinistre: {}", e.getMessage());
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "Erreur lors de l'enregistrement du sinistre");
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * Mock guarantee logic - replace with actual business rules
     */
    private boolean checkGuaranteeLogic(String produit, String evenement, String typeDegat) {
        // Mock logic - in real implementation, this would check against guarantee
        // tables
        if ("AUTO".equalsIgnoreCase(produit)) {
            if ("COLLISION".equalsIgnoreCase(evenement) || "VOL".equalsIgnoreCase(evenement)) {
                return true;
            }
        }

        if ("MATERIEL".equalsIgnoreCase(typeDegat)) {
            return true;
        }

        // Default to false for demo purposes
        return Math.random() > 0.3; // 70% chance of having guarantee for demo
    }
}
