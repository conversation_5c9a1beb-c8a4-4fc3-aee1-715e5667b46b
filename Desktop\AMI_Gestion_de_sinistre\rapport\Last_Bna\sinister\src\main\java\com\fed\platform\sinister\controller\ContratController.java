package com.fed.platform.sinister.controller;

import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fed.platform.sinister.domain.Contrat;
import com.fed.platform.sinister.repository.ContratRepository;
import com.fed.platform.sinister.repository.GarantieRepository;

@RestController
@RequestMapping("/api")
@CrossOrigin(origins = "http://localhost:4200")
public class ContratController {

    private static final Logger logger = LoggerFactory.getLogger(ContratController.class);

    @Autowired
    private ContratRepository contratRepository;

    @Autowired
    private GarantieRepository garantieRepository;

    // Note: OuvertureSinistreRepository would be injected here when imports are
    // fixed

    /**
     * Health check endpoint
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, String>> healthCheck() {
        Map<String, String> response = new HashMap<>();
        response.put("status", "UP");
        response.put("timestamp", java.time.Instant.now().toString());
        response.put("service", "ContratController");
        return ResponseEntity.ok(response);
    }

    /**
     * Search contracts by immatriculation
     */
    @GetMapping("/search-contrat")
    public ResponseEntity<List<Contrat>> searchContrat(@RequestParam String immatriculation) {
        logger.info("🔍 Searching contracts by immatriculation: {}", immatriculation);

        try {
            List<Contrat> contrats = contratRepository.findByImmatriculationContainingIgnoreCase(immatriculation);
            logger.info("✅ Found {} contracts", contrats.size());
            return ResponseEntity.ok(contrats);
        } catch (Exception e) {
            logger.error("❌ Error searching contracts: {}", e.getMessage());
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Search contracts with date validation
     */
    @PostMapping("/search-contrat-with-validation")
    public ResponseEntity<List<Contrat>> searchContratWithValidation(@RequestBody Map<String, Object> request) {
        logger.info("🔍 Searching contracts with validation");

        try {
            String immatriculation = request.get("immatriculation").toString();
            String dateSurvenance = request.get("dateSurvenance").toString();

            logger.info("📋 Search criteria - Immatriculation: {}, Date survenance: {}", immatriculation,
                    dateSurvenance);

            // Convert string date to Instant for comparison
            LocalDate survenance = LocalDate.parse(dateSurvenance);
            Instant survenanceInstant = survenance.atStartOfDay(ZoneId.systemDefault()).toInstant();

            // Find contracts by immatriculation
            List<Contrat> allContrats = contratRepository.findByImmatriculationContainingIgnoreCase(immatriculation);

            // Filter contracts that cover the date de survenance
            List<Contrat> validContrats = allContrats.stream()
                    .filter(contrat -> {
                        boolean covers = !survenanceInstant.isBefore(contrat.getDateEffet()) &&
                                !survenanceInstant.isAfter(contrat.getDateFin());
                        logger.info("📅 Contract {} covers date: {}", contrat.getNumContrat(), covers);
                        return covers;
                    })
                    .collect(java.util.stream.Collectors.toList());

            logger.info("✅ Found {} valid contracts out of {} total", validContrats.size(), allContrats.size());
            return ResponseEntity.ok(validContrats);

        } catch (Exception e) {
            logger.error("❌ Error searching contracts with validation: {}", e.getMessage());
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Check if contract covers the date de survenance
     */
    @PostMapping("/check-contract-coverage")
    public ResponseEntity<Map<String, Object>> checkContractCoverage(@RequestBody Map<String, Object> request) {
        logger.info("🔍 Checking contract coverage");

        try {
            Long contratId = Long.valueOf(request.get("contratId").toString());
            String dateSurvenance = request.get("dateSurvenance").toString();

            Contrat contrat = contratRepository.findById(contratId).orElse(null);
            Map<String, Object> response = new HashMap<>();

            if (contrat == null) {
                response.put("isValid", false);
                response.put("message", "Contrat non trouvé");
                return ResponseEntity.ok(response);
            }

            // Convert string date to Instant for comparison
            LocalDate survenance = LocalDate.parse(dateSurvenance);
            Instant survenanceInstant = survenance.atStartOfDay(ZoneId.systemDefault()).toInstant();

            // Check if date de survenance is within contract period
            boolean isValid = !survenanceInstant.isBefore(contrat.getDateEffet()) &&
                    !survenanceInstant.isAfter(contrat.getDateFin());

            response.put("isValid", isValid);
            response.put("message", isValid ? "Le contrat couvre la date de survenance"
                    : "Le contrat ne couvre pas la date de survenance");

            logger.info("✅ Contract coverage check result: {}", isValid);
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("❌ Error checking contract coverage: {}", e.getMessage());
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("isValid", false);
            errorResponse.put("message", "Erreur lors de la vérification de la couverture");
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * Check delay between date de declaration and date de survenance
     */
    @PostMapping("/check-delai")
    public ResponseEntity<Map<String, Object>> checkDelai(@RequestBody Map<String, Object> request) {
        logger.info("🔍 Checking delay");

        try {
            String dateDeclaration = request.get("dateDeclaration").toString();
            String dateSurvenance = request.get("dateSurvenance").toString();

            LocalDate declaration = LocalDate.parse(dateDeclaration);
            LocalDate survenance = LocalDate.parse(dateSurvenance);

            long delaiJours = java.time.temporal.ChronoUnit.DAYS.between(survenance, declaration);
            boolean isDelaiExceeded = delaiJours > 30; // Assuming 30 days is the limit

            Map<String, Object> response = new HashMap<>();
            response.put("isDelaiExceeded", isDelaiExceeded);
            response.put("delaiJours", delaiJours);
            response.put("message",
                    isDelaiExceeded ? "Ce sinistre est déclaré hors le délai qui peut être une clause de rejet."
                            : "Le délai de déclaration est respecté.");

            logger.info("✅ Delay check result: {} days, exceeded: {}", delaiJours, isDelaiExceeded);
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("❌ Error checking delay: {}", e.getMessage());
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("isDelaiExceeded", false);
            errorResponse.put("message", "Erreur lors de la vérification des délais");
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * Check guarantee coverage for the sinistre
     * Gets evenement, type_degat, responsabilite from ouverture_sinistre table
     * and checks the garantie column for available guarantees
     */
    @PostMapping("/check-guarantee")
    public ResponseEntity<Map<String, Object>> checkGuarantee(@RequestBody Map<String, Object> request) {
        logger.info("🔍 Checking guarantee coverage from garantie table");

        try {
            Long contratId = Long.valueOf(request.get("contratId").toString());
            logger.info("📋 Checking guarantees for contrat_id: {}", contratId);

            // Query garantie table where contrat_id = contratId
            List<com.fed.platform.sinister.domain.Garantie> garanties = garantieRepository.findByContratId(contratId);
            logger.info("✅ Found {} guarantees in garantie table for contrat_id: {}", garanties.size(), contratId);

            // Extract libelle values from garantie table
            List<String> availableGaranties = garanties.stream()
                    .map(garantie -> garantie.getLibelle())
                    .filter(libelle -> libelle != null && !libelle.trim().isEmpty())
                    .collect(java.util.stream.Collectors.toList());

            boolean hasMatchingGuarantee = !availableGaranties.isEmpty();

            Map<String, Object> response = new HashMap<>();
            response.put("hasMatchingGuarantee", hasMatchingGuarantee);
            response.put("guarantees", availableGaranties);
            response.put("message", hasMatchingGuarantee ? "Des garanties trouvées dans la table garantie"
                    : "Aucune garantie trouvée pour ce contrat");

            logger.info("✅ Guarantees from garantie table: {}", availableGaranties);
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("❌ Error checking guarantee: {}", e.getMessage());
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("hasMatchingGuarantee", false);
            errorResponse.put("message", "Erreur lors de la vérification des garanties");
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * Save sinistre with "Sans suite" status
     */
    @PostMapping("/save-sinistre-sans-suite")
    public ResponseEntity<Map<String, Object>> saveSinistreSansSuite(@RequestBody Map<String, Object> request) {
        logger.info("💾 Saving sinistre with 'Sans suite' status");

        try {
            String numeroSinistre = request.get("numeroSinistre").toString();
            Long contratId = Long.valueOf(request.get("contratId").toString());
            String reason = request.get("reason").toString();

            // TODO: Implement actual save logic to database
            logger.info("📝 Sinistre {} saved with status 'Sans suite', reason: {}", numeroSinistre, reason);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Sinistre enregistré avec l'état 'Sans suite'");
            response.put("numeroSinistre", numeroSinistre);
            response.put("etat", "Sans suite");

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("❌ Error saving sinistre: {}", e.getMessage());
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "Erreur lors de l'enregistrement du sinistre");
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * Mock method to simulate getting data from ouverture_sinistre table
     * In real implementation, this would query the database by numeroSinistre
     */
    private Map<String, Object> getMockSinistreData(String numeroSinistre) {
        Map<String, Object> sinistreData = new HashMap<>();

        // Simulate different sinistre data based on numeroSinistre
        if (numeroSinistre != null && numeroSinistre.contains("001")) {
            sinistreData.put("evenement", "Collision");
            sinistreData.put("typeDegat", "Matériel");
            sinistreData.put("responsabilite", "2");
            sinistreData.put("garantie", "Responsabilité Civile,Dommages Matériels,Protection Juridique");
        } else if (numeroSinistre != null && numeroSinistre.contains("002")) {
            sinistreData.put("evenement", "Vol");
            sinistreData.put("typeDegat", "Matériel");
            sinistreData.put("responsabilite", "0");
            sinistreData.put("garantie", "Vol et Tentative de Vol,Assistance Dépannage");
        } else {
            // Default sinistre data
            sinistreData.put("evenement", "Accident");
            sinistreData.put("typeDegat", "Matériel");
            sinistreData.put("responsabilite", "1");
            sinistreData.put("garantie",
                    "Responsabilité Civile,Dommages Matériels,Protection Juridique,Assistance Dépannage");
        }

        return sinistreData;
    }

    /**
     * Parse the garantie column to extract individual guarantee names
     * Assumes garantie column contains comma-separated guarantee names
     */
    private List<String> parseGarantieColumn(String garantieColumn) {
        if (garantieColumn == null || garantieColumn.trim().isEmpty()) {
            return new ArrayList<>();
        }

        // Split by comma and clean up each guarantee name
        return Arrays.stream(garantieColumn.split(","))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .collect(java.util.stream.Collectors.toList());
    }

    /**
     * Get sinistre details by numero sinistre from ouverture_sinistre table
     */
    @GetMapping("/ouverture-sinistre/{numeroSinistre}")
    public ResponseEntity<Map<String, Object>> getSinistreDetails(@PathVariable String numeroSinistre) {
        logger.info("🔍 Getting sinistre details from ouverture_sinistre table for: {}", numeroSinistre);

        try {
            // TODO: In real implementation, this would query the ouverture_sinistre table:
            // OuvertureSinistre sinistre =
            // ouvertureSinistreRepository.findByNumeroSinistre(numeroSinistre);

            // For now, return mock data that simulates the ouverture_sinistre table
            // structure
            Map<String, Object> sinistreDetails = new HashMap<>();
            sinistreDetails.put("numeroSinistre", numeroSinistre);
            sinistreDetails.put("dateCreation", java.time.Instant.now().toString());
            sinistreDetails.put("status", "ACTIVE");

            // Add the sinistre data that would come from ouverture_sinistre table
            Map<String, Object> sinistreData = getMockSinistreData(numeroSinistre);
            sinistreDetails.putAll(sinistreData);

            logger.info("✅ Returning sinistre details: {}", sinistreDetails);
            return ResponseEntity.ok(sinistreDetails);

        } catch (Exception e) {
            logger.error("❌ Error getting sinistre details: {}", e.getMessage());
            return ResponseEntity.internalServerError().build();
        }
    }
}
