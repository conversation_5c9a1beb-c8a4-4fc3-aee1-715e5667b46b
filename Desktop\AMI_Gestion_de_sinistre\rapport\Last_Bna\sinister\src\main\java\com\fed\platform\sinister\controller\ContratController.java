package com.fed.platform.sinister.controller;

import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fed.platform.sinister.domain.Contrat;
import com.fed.platform.sinister.domain.Evenement;
import com.fed.platform.sinister.domain.OuvertureSinistre;
import com.fed.platform.sinister.repository.ContratRepository;
import com.fed.platform.sinister.repository.GarantieRepository;

@RestController
@RequestMapping("/api")
@CrossOrigin(origins = "http://localhost:4200")
public class ContratController {

    private static final Logger logger = LoggerFactory.getLogger(ContratController.class);

    @Autowired
    private ContratRepository contratRepository;

    @Autowired
    private GarantieRepository garantieRepository;

    // Note: OuvertureSinistreRepository would be injected here when imports are
    // fixed

    /**
     * Health check endpoint
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, String>> healthCheck() {
        Map<String, String> response = new HashMap<>();
        response.put("status", "UP");
        response.put("timestamp", java.time.Instant.now().toString());
        response.put("service", "ContratController");
        return ResponseEntity.ok(response);
    }

    /**
     * Debug endpoint to see all contracts in database
     */
    @GetMapping("/debug/contracts")
    public ResponseEntity<Map<String, Object>> debugContracts() {
        logger.info("🔍 Debug: Getting all contracts from database");

        try {
            List<Contrat> allContrats = contratRepository.findAll();

            Map<String, Object> response = new HashMap<>();
            response.put("totalContracts", allContrats.size());
            response.put("contracts", allContrats);

            logger.info("📊 Debug: Found {} contracts in database", allContrats.size());
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("❌ Debug: Error getting contracts: {}", e.getMessage());
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Search contracts by immatriculation (simple search without date validation)
     */
    @GetMapping("/search-contrat")
    public ResponseEntity<List<Contrat>> searchContrat(@RequestParam String immatriculation) {
        logger.info("🔍 Simple search by immatriculation: {}", immatriculation);

        try {
            // First show all contracts for debugging
            List<Contrat> allContrats = contratRepository.findAll();
            logger.info("📊 Total contracts in database: {}", allContrats.size());

            // Try different search methods
            List<Contrat> exactMatch = contratRepository.findByImmatriculation(immatriculation);
            logger.info("🎯 Exact match found: {} contracts", exactMatch.size());

            List<Contrat> containsMatch = contratRepository.findByImmatriculationContainingIgnoreCase(immatriculation);
            logger.info("🔍 Contains match found: {} contracts", containsMatch.size());

            // Return the best match
            List<Contrat> result = !exactMatch.isEmpty() ? exactMatch : containsMatch;
            logger.info("✅ Returning {} contracts", result.size());

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            logger.error("❌ Error searching contracts: {}", e.getMessage());
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Search contracts with date validation - SIMPLIFIED VERSION FOR DEBUGGING
     */
    @PostMapping("/search-contrat-with-validation")
    public ResponseEntity<List<Contrat>> searchContratWithValidation(@RequestBody Map<String, Object> request) {
        logger.info("🔍 Searching contracts with validation - SIMPLIFIED");

        try {
            logger.info("📥 Received request: {}", request);

            // Basic validation
            if (request == null) {
                logger.error("❌ Request is null");
                return ResponseEntity.badRequest().build();
            }

            if (!request.containsKey("immatriculation")) {
                logger.error("❌ Missing immatriculation in request");
                return ResponseEntity.badRequest().build();
            }

            String immatriculation = request.get("immatriculation").toString();
            logger.info("📋 Searching for immatriculation: '{}'", immatriculation);

            // Simple search without date validation for now
            List<Contrat> contrats = contratRepository.findByImmatriculationContainingIgnoreCase(immatriculation);
            logger.info("✅ Found {} contracts", contrats.size());

            return ResponseEntity.ok(contrats);

        } catch (Exception e) {
            logger.error("❌ Error searching contracts: {}", e.getMessage());
            e.printStackTrace(); // Print full stack trace for debugging
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Check if contract covers the date de survenance
     */
    @PostMapping("/check-contract-coverage")
    public ResponseEntity<Map<String, Object>> checkContractCoverage(@RequestBody Map<String, Object> request) {
        logger.info("🔍 Checking contract coverage");

        try {
            Long contratId = Long.valueOf(request.get("contratId").toString());
            String dateSurvenance = request.get("dateSurvenance").toString();

            Contrat contrat = contratRepository.findById(contratId).orElse(null);
            Map<String, Object> response = new HashMap<>();

            if (contrat == null) {
                response.put("isValid", false);
                response.put("message", "Contrat non trouvé");
                return ResponseEntity.ok(response);
            }

            // Convert string date to Instant for comparison
            LocalDate survenance = LocalDate.parse(dateSurvenance);
            Instant survenanceInstant = survenance.atStartOfDay(ZoneId.systemDefault()).toInstant();

            // Check if date de survenance is within contract period
            boolean isValid = !survenanceInstant.isBefore(contrat.getDateEffet()) &&
                    !survenanceInstant.isAfter(contrat.getDateFin());

            response.put("isValid", isValid);
            response.put("message", isValid ? "Le contrat couvre la date de survenance"
                    : "Le contrat ne couvre pas la date de survenance");

            logger.info("✅ Contract coverage check result: {}", isValid);
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("❌ Error checking contract coverage: {}", e.getMessage());
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("isValid", false);
            errorResponse.put("message", "Erreur lors de la vérification de la couverture");
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * Check delay between date de declaration and date de survenance
     */
    @PostMapping("/check-delai")
    public ResponseEntity<Map<String, Object>> checkDelai(@RequestBody Map<String, Object> request) {
        logger.info("🔍 Checking delay");

        try {
            String dateDeclaration = request.get("dateDeclaration").toString();
            String dateSurvenance = request.get("dateSurvenance").toString();

            LocalDate declaration = LocalDate.parse(dateDeclaration);
            LocalDate survenance = LocalDate.parse(dateSurvenance);

            long delaiJours = java.time.temporal.ChronoUnit.DAYS.between(survenance, declaration);
            boolean isDelaiExceeded = delaiJours > 30; // Assuming 30 days is the limit

            Map<String, Object> response = new HashMap<>();
            response.put("isDelaiExceeded", isDelaiExceeded);
            response.put("delaiJours", delaiJours);
            response.put("message",
                    isDelaiExceeded ? "Ce sinistre est déclaré hors le délai qui peut être une clause de rejet."
                            : "Le délai de déclaration est respecté.");

            logger.info("✅ Delay check result: {} days, exceeded: {}", delaiJours, isDelaiExceeded);
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("❌ Error checking delay: {}", e.getMessage());
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("isDelaiExceeded", false);
            errorResponse.put("message", "Erreur lors de la vérification des délais");
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * Check guarantee coverage by comparing event guarantees with contract
     * guarantees
     * Compares garanties from evenement (via ConfigEvenement) with contract
     * garanties
     */
    @PostMapping("/check-guarantee")
    public ResponseEntity<Map<String, Object>> checkGuarantee(@RequestBody Map<String, Object> request) {
        logger.info("🔍 Comparing event guarantees with contract guarantees");

        try {
            Long contratId = Long.valueOf(request.get("contratId").toString());
            String evenement = request.get("evenement") != null ? request.get("evenement").toString() : null;

            logger.info("📋 Checking guarantees for contrat_id: {} and evenement: {}", contratId, evenement);

            // 1. Get contract guarantees from garantie table
            List<com.fed.platform.sinister.domain.Garantie> contractGaranties = garantieRepository
                    .findByContrat_Id(contratId);
            List<String> contractGuaranteeNames = contractGaranties.stream()
                    .map(garantie -> garantie.getLibelle())
                    .filter(libelle -> libelle != null && !libelle.trim().isEmpty())
                    .collect(java.util.stream.Collectors.toList());

            logger.info("📋 Contract guarantees: {}", contractGuaranteeNames);

            // 2. Get event guarantees from evenement table garantie column
            String numeroSinistre = request.get("sinistreId") != null ? request.get("sinistreId").toString() : null;
            List<String> eventGuaranties = getEventGuarantiesFromDatabase(numeroSinistre, evenement);
            logger.info("📋 Event guarantees from database: {}", eventGuaranties);

            // 3. Find matching guarantees (intersection between event and contract)
            List<String> matchingGuaranties = eventGuaranties.stream()
                    .filter(contractGuaranteeNames::contains)
                    .collect(java.util.stream.Collectors.toList());

            boolean hasMatchingGuarantee = !matchingGuaranties.isEmpty();

            Map<String, Object> response = new HashMap<>();
            response.put("hasMatchingGuarantee", hasMatchingGuarantee);
            response.put("guarantees", matchingGuaranties);
            response.put("eventGuaranties", eventGuaranties);
            response.put("contractGuaranties", contractGuaranteeNames);
            response.put("message",
                    hasMatchingGuarantee ? "Garanties correspondantes trouvées entre l'événement et le contrat"
                            : "Aucune garantie correspondante entre l'événement et le contrat");

            logger.info("✅ Matching guarantees: {}", matchingGuaranties);
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("❌ Error checking guarantee: {}", e.getMessage());
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("hasMatchingGuarantee", false);
            errorResponse.put("message", "Erreur lors de la vérification des garanties");
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * Save sinistre with "Sans suite" status
     */
    @PostMapping("/save-sinistre-sans-suite")
    public ResponseEntity<Map<String, Object>> saveSinistreSansSuite(@RequestBody Map<String, Object> request) {
        logger.info("💾 Saving sinistre with 'Sans suite' status");

        try {
            String numeroSinistre = request.get("numeroSinistre").toString();
            Long contratId = Long.valueOf(request.get("contratId").toString());
            String reason = request.get("reason").toString();

            // TODO: Implement actual save logic to database
            logger.info("📝 Sinistre {} saved with status 'Sans suite', reason: {}", numeroSinistre, reason);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Sinistre enregistré avec l'état 'Sans suite'");
            response.put("numeroSinistre", numeroSinistre);
            response.put("etat", "Sans suite");

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("❌ Error saving sinistre: {}", e.getMessage());
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "Erreur lors de l'enregistrement du sinistre");
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * Mock method to simulate getting data from ouverture_sinistre table
     * In real implementation, this would query the database by numeroSinistre
     */
    private Map<String, Object> getMockSinistreData(String numeroSinistre) {
        Map<String, Object> sinistreData = new HashMap<>();

        // Simulate different sinistre data based on numeroSinistre
        if (numeroSinistre != null && numeroSinistre.contains("001")) {
            sinistreData.put("evenement", "Collision");
            sinistreData.put("typeDegat", "Matériel");
            sinistreData.put("responsabilite", "2");
            sinistreData.put("garantie", "Responsabilité Civile,Dommages Matériels,Protection Juridique");
        } else if (numeroSinistre != null && numeroSinistre.contains("002")) {
            sinistreData.put("evenement", "Vol");
            sinistreData.put("typeDegat", "Matériel");
            sinistreData.put("responsabilite", "0");
            sinistreData.put("garantie", "Vol et Tentative de Vol,Assistance Dépannage");
        } else {
            // Default sinistre data
            sinistreData.put("evenement", "Accident");
            sinistreData.put("typeDegat", "Matériel");
            sinistreData.put("responsabilite", "1");
            sinistreData.put("garantie",
                    "Responsabilité Civile,Dommages Matériels,Protection Juridique,Assistance Dépannage");
        }

        return sinistreData;
    }

    /**
     * Get event guarantees from database using real query
     * Gets evenement, type_degat, responsabilite from ouverture_sinistre table
     * Then queries evenement table to get garantie column
     */
    private List<String> getEventGuarantiesFromDatabase(String numeroSinistre, String evenementCode) {
        List<String> eventGuaranties = new ArrayList<>();

        try {
            // 1. Get sinistre data from ouverture_sinistre table
            String typeDegat = null;
            Integer responsabilite = null;

            if (numeroSinistre != null) {
                Optional<OuvertureSinistre> sinistreOpt = ouvertureSinistreRepository
                        .findByNumeroSinistre(numeroSinistre);
                if (sinistreOpt.isPresent()) {
                    OuvertureSinistre sinistre = sinistreOpt.get();
                    evenementCode = sinistre.getEvenement();
                    typeDegat = sinistre.getTypeDegat();
                    responsabilite = sinistre.getResponsabilite();

                    logger.info("📋 Found sinistre data - evenement: {}, type_degat: {}, responsabilite: {}",
                            evenementCode, typeDegat, responsabilite);
                }
            }

            // 2. Query evenement table to get garantie column
            if (evenementCode != null) {
                List<Evenement> evenements = evenementRepository.findByCodeAndTypeDegat(evenementCode, typeDegat);
                for (Evenement evenement : evenements) {
                    if (evenement.getGarantie() != null && !evenement.getGarantie().trim().isEmpty()) {
                        // Parse comma-separated garanties
                        String[] garantieArray = evenement.getGarantie().split(",");
                        for (String garantie : garantieArray) {
                            String trimmedGarantie = garantie.trim();
                            if (!trimmedGarantie.isEmpty()) {
                                eventGuaranties.add(trimmedGarantie);
                            }
                        }
                    }
                }
                logger.info("📋 Event guarantees from evenement table: {}", eventGuaranties);
            }
        } catch (Exception e) {
            logger.error("❌ Error getting event guarantees from database: {}", e.getMessage());
        }

        return eventGuaranties;
    }

    /**
     * Parse the garantie column to extract individual guarantee names
     * Assumes garantie column contains comma-separated guarantee names
     */
    private List<String> parseGarantieColumn(String garantieColumn) {
        if (garantieColumn == null || garantieColumn.trim().isEmpty()) {
            return new ArrayList<>();
        }

        // Split by comma and clean up each guarantee name
        return Arrays.stream(garantieColumn.split(","))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .collect(java.util.stream.Collectors.toList());
    }

    /**
     * Get sinistre details by numero sinistre from ouverture_sinistre table
     */
    @GetMapping("/ouverture-sinistre/{numeroSinistre}")
    public ResponseEntity<Map<String, Object>> getSinistreDetails(@PathVariable String numeroSinistre) {
        logger.info("🔍 Getting sinistre details from ouverture_sinistre table for: {}", numeroSinistre);

        try {
            // TODO: In real implementation, this would query the ouverture_sinistre table:
            // OuvertureSinistre sinistre =
            // ouvertureSinistreRepository.findByNumeroSinistre(numeroSinistre);

            // For now, return mock data that simulates the ouverture_sinistre table
            // structure
            Map<String, Object> sinistreDetails = new HashMap<>();
            sinistreDetails.put("numeroSinistre", numeroSinistre);
            sinistreDetails.put("dateCreation", java.time.Instant.now().toString());
            sinistreDetails.put("status", "ACTIVE");

            // Add the sinistre data that would come from ouverture_sinistre table
            Map<String, Object> sinistreData = getMockSinistreData(numeroSinistre);
            sinistreDetails.putAll(sinistreData);

            logger.info("✅ Returning sinistre details: {}", sinistreDetails);
            return ResponseEntity.ok(sinistreDetails);

        } catch (Exception e) {
            logger.error("❌ Error getting sinistre details: {}", e.getMessage());
            return ResponseEntity.internalServerError().build();
        }
    }
}
