spring.application.name=sinister

# PostgreSQL Configuration
spring.datasource.url=************************************************
spring.datasource.username=postgres
spring.datasource.password=
spring.datasource.driver-class-name=org.postgresql.Driver

# JPA (Hibernate) Configuration
spring.jpa.database-platform=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.open-in-view=false

# File Upload Configuration - Increase limits for vehicle photos and documents
spring.servlet.multipart.max-file-size=50MB
spring.servlet.multipart.max-request-size=100MB
spring.servlet.multipart.enabled=true

# Server Configuration
server.tomcat.max-swallow-size=100MB
