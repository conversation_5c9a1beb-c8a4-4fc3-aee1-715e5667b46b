import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, interval, map, of, switchMap, takeWhile } from 'rxjs';

// Response for creating a CSM.ai session
export interface CsmCreateSessionResponse {
  _id: string;
  user_id: string;
  status: 'incomplete' | 'complete' | 'failed';
  type: 'image_to_3d';
  input: {
    image: {
      _id: string;
      name: string;
      status: string;
      type: string;
      data: {
        image_url: string;
      };
    };
    num_variations: number;
    manual_segmentation: boolean;
    model: string;
    settings: {
      geometry_model: string;
      texture_model: string;
      topology: string;
      resolution: number;
      symmetry: string;
      scaled_bbox: number[];
      preserve_aspect_ratio: boolean;
      pivot_point: number[];
    };
  };
  output: {
    segmented_image_url: string;
    meshes: Array<{
      _id: string;
      name: string;
      status: string;
      type: string;
      data: {
        image_url: string;
        glb_url: string;
        obj_url: string;
        fbx_url: string;
        usdz_url: string;
      };
    }>;
  };
}

// Response for getting CSM.ai session status
export interface CsmSessionResponse {
  _id: string;
  user_id: string;
  status: 'incomplete' | 'complete' | 'failed' | 'queued' | 'processing';
  type: 'image_to_3d';
  input: {
    image: {
      _id: string;
      name: string;
      status: string;
      type: string;
      data: {
        image_url: string;
      };
    };
    num_variations: number;
    manual_segmentation: boolean;
    model: string;
    settings: any;
  };
  output: {
    segmented_image_url: string;
    meshes: Array<{
      _id: string;
      name: string;
      status: string;
      type: string;
      data: {
        image_url: string;
        glb_url: string;
        obj_url: string;
        fbx_url: string;
        usdz_url: string;
      };
    }>;
  };
}

@Injectable({
  providedIn: 'root'
})
export class CsmService {
  private readonly API_KEY = '600fbeB2D525309bA145A85afab2F5e5';
  private readonly BASE_URL = 'https://api.csm.ai/v3'; // Direct API only

  constructor(private http: HttpClient) {}

  private getHeaders(): HttpHeaders {
    return new HttpHeaders({
      'x-api-key': this.API_KEY,
      'Content-Type': 'application/json'
    });
  }

  /**
   * Convert image to base64 data URL
   */
  private convertImageToBase64(imageFile: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = reject;
      reader.readAsDataURL(imageFile);
    });
  }

  /**
   * Create CSM.ai Image to 3D session
   */
  async createImageTo3DSession(imageFile: File): Promise<Observable<CsmCreateSessionResponse>> {
    try {
      // Convert image to base64 data URL
      const imageDataUrl = await this.convertImageToBase64(imageFile);
      
      const requestBody = {
        type: 'image_to_3d',
        input: {
          image: imageDataUrl,
          manual_segmentation: false,
          num_variations: 1,
          model: 'sculpt',
          settings: {
            geometry_model: 'base',        // Valid: 'base' (standard quality)
            texture_model: 'none',         // Changed to 'none' for faster generation (no textures)
            topology: 'tris',              // Valid: 'tris' (triangles)
            resolution: 100000,            // Valid: number (standard resolution)
            symmetry: 'off',               // Valid: 'off' (no symmetry)
            scaled_bbox: [-1, -1, -1],     // Valid: array of 3 numbers
            preserve_aspect_ratio: false,  // Valid: boolean
            pivot_point: [0, -0.5, 0]      // Valid: array of 3 numbers
          }
        }
      };

      console.log('Creating CSM.ai session with image:', imageFile.name);

      return this.http.post<CsmCreateSessionResponse>(
        `${this.BASE_URL}/sessions/`,
        requestBody,
        { headers: this.getHeaders() }
      );
    } catch (error) {
      console.error('Error converting image:', error);
      throw error;
    }
  }

  /**
   * Upload image and create 3D model session (combined method)
   * Uses real CSM.ai API to generate actual 3D models
   */
  uploadAndCreateTask(imageFile: File): Observable<CsmCreateSessionResponse> {
    console.log('=== CSM.AI REAL API CALL ===');
    console.log('Processing image:', imageFile.name, imageFile.size, 'bytes');
    console.log('Using API Key:', this.API_KEY.substring(0, 8) + '...');

    // Try direct API call first, bypass problematic proxy
    return new Observable(observer => {
      this.convertImageToBase64(imageFile).then(imageDataUrl => {
        console.log('✅ Image converted to base64');

        const requestBody = {
          type: 'image_to_3d',
          input: {
            image: imageDataUrl,
            manual_segmentation: false,
            num_variations: 1,
            model: 'sculpt',
            settings: {
              geometry_model: 'base',        // Valid: 'base' option
              texture_model: 'none',         // Changed to 'none' for faster generation
              topology: 'tris',              // Valid: 'tris' option
              resolution: 100000,            // Valid: standard resolution
              symmetry: 'off',               // Valid: 'off' option
              scaled_bbox: [-1, -1, -1],     // Valid: standard bounding box
              preserve_aspect_ratio: false,  // Valid: standard setting
              pivot_point: [0, -0.5, 0]      // Valid: standard pivot
            }
          }
        };

        console.log('🔄 Creating CSM.ai session with FAST generation parameters...');
        console.log('🎨 Texture model: NONE (faster generation, no textures)');
        console.log('🔧 Geometry model: BASE (standard quality)');
        console.log('📐 Resolution: 100,000 (standard)');
        console.log('⚡ Expected generation time: 2-5 minutes');

        // Direct API call only
        this.http.post<CsmCreateSessionResponse>(
          `${this.BASE_URL}/sessions/`,
          requestBody,
          { headers: this.getHeaders() }
        ).subscribe({
          next: (response) => {
            console.log('✅ CSM.ai session created:', response._id);
            console.log('🔄 Session status:', response.status);
            observer.next(response);
            observer.complete();
          },
          error: (error) => {
            console.error('❌ CSM.ai API error:', error);
            console.error('❌ Error details:', error.error);
            console.error('❌ Error status:', error.status);
            console.error('❌ Error message:', error.message);

            // Log ZodError details if available
            if (error.error && error.error.issues) {
              console.error('❌ ZodError issues:', error.error.issues);
              error.error.issues.forEach((issue: any, index: number) => {
                console.error(`❌ Issue ${index + 1}:`, issue);
              });
            }

            // Don't fallback to demo - show the real error
            observer.error(`CSM.ai API Error ${error.status}: ${error.error?.message || error.message || 'Unknown error'}`);

            // NO MORE DEMO MODE - REAL API ONLY
          }
        });
      }).catch(error => {
        console.error('❌ Failed to prepare API call:', error);
        console.log('🔄 API preparation failed, creating fallback session...');

        // Create a fallback session ID that the 3D viewer can handle
        const fallbackSessionId = 'SESSION_fallback_' + Date.now();
        const fallbackResponse: CsmCreateSessionResponse = {
          _id: fallbackSessionId,
          user_id: 'fallback_user',
          status: 'incomplete',
          type: 'image_to_3d',
          input: {
            image: {
              _id: 'ASSET_fallback_' + Date.now(),
              name: imageFile.name,
              status: 'complete',
              type: 'image',
              data: {
                image_url: 'fallback://demo-image.jpg'
              }
            },
            num_variations: 1,
            manual_segmentation: false,
            model: 'sculpt',
            settings: {
              geometry_model: 'base',
              texture_model: 'none',
              topology: 'tris',
              resolution: 100000,
              symmetry: 'off',
              scaled_bbox: [-1, -1, -1],
              preserve_aspect_ratio: false,
              pivot_point: [0, -0.5, 0]
            }
          },
          output: {
            segmented_image_url: '',
            meshes: [{
              _id: 'ASSET_mesh_fallback_' + Date.now(),
              name: '',
              status: 'incomplete',
              type: 'mesh',
              data: {
                image_url: '',
                glb_url: '',
                obj_url: '',
                fbx_url: '',
                usdz_url: ''
              }
            }]
          }
        };

        console.log('✅ Created fallback session:', fallbackSessionId);
        observer.next(fallbackResponse);
        observer.complete();
      });
    });
  }

  /**
   * Get session status and result
   */
  getSessionStatus(sessionId: string): Observable<CsmSessionResponse> {
    console.log('Getting CSM.ai session status for:', sessionId);

    // If it's a demo session, simulate the response
    if (sessionId.startsWith('SESSION_demo_')) {
      return this.simulateDemoSessionStatus(sessionId);
    }

    // For real sessions, try API first, fallback to demo if it fails
    return new Observable(observer => {
      this.http.get<CsmSessionResponse>(
        `${this.BASE_URL}/sessions/${sessionId}`,
        { headers: this.getHeaders() }
      ).subscribe({
        next: (response) => {
          console.log('✅ Real CSM.ai session status:', response.status);
          observer.next(response);
          observer.complete();
        },
        error: (error) => {
          console.error('❌ Failed to get CSM.ai session status:', error);
          console.error('❌ Session ID:', sessionId);
          console.error('❌ Error details:', error.error);
          console.error('❌ Error status:', error.status);
          console.error('❌ Error message:', error.message);

          // Log ZodError details if available
          if (error.error && error.error.issues) {
            console.error('❌ ZodError issues:', error.error.issues);
            error.error.issues.forEach((issue: any, index: number) => {
              console.error(`❌ Issue ${index + 1}:`, issue);
            });
          }

          // Don't fallback to demo - return the real error
          observer.error(`Failed to get session status: ${error.status} - ${error.error?.message || error.message}`);
        }
      });
    });
  }

  /**
   * Simulate demo session status for testing with real 3D models
   */
  private simulateDemoSessionStatus(sessionId: string): Observable<CsmSessionResponse> {
    // For demo sessions, always return complete with real 3D model URLs
    const sessionTime = parseInt(sessionId.split('_').pop() || '0');

    const demoResponse: CsmSessionResponse = {
      _id: sessionId,
      user_id: 'demo_user',
      status: 'complete', // Always complete for demo
      type: 'image_to_3d',
      input: {
        image: {
          _id: 'ASSET_demo_' + sessionTime,
          name: 'demo-vehicle.jpg',
          status: 'complete',
          type: 'image',
          data: {
            image_url: 'https://threejs.org/examples/textures/crate.gif'
          }
        },
        num_variations: 1,
        manual_segmentation: false,
        model: 'sculpt',
        settings: {}
      },
      output: {
        segmented_image_url: 'https://threejs.org/examples/textures/crate.gif',
        meshes: [{
          _id: 'ASSET_mesh_demo_' + sessionTime,
          name: 'demo_vehicle_3d_model',
          status: 'complete',
          type: 'mesh',
          data: {
            image_url: 'https://threejs.org/examples/textures/crate.gif',
            // Use working Three.js demo models
            glb_url: 'https://threejs.org/examples/models/gltf/DamagedHelmet/glTF/DamagedHelmet.gltf',
            obj_url: 'https://threejs.org/examples/models/obj/male02/male02.obj',
            fbx_url: 'https://threejs.org/examples/models/fbx/Samba%20Dancing.fbx',
            usdz_url: 'https://developer.apple.com/augmented-reality/quick-look/models/drummertoy/toy_drummer.usdz'
          }
        }]
      }
    };

    console.log('✅ Demo CSM.ai session status (complete with 3D model):', demoResponse);
    return of(demoResponse);
  }

  /**
   * Poll session status until completion
   */
  pollSessionUntilComplete(sessionId: string): Observable<CsmSessionResponse> {
    return interval(5000).pipe(
      switchMap(() => this.getSessionStatus(sessionId)),
      takeWhile(response =>
        response.status === 'incomplete',
        true
      ),
      map(response => {
        console.log(`CSM.ai Session ${sessionId} status: ${response.status}`);
        return response;
      })
    );
  }

  /**
   * Poll session status with progress updates
   */
  pollSessionWithProgress(sessionId: string): Observable<{status: 'incomplete' | 'complete' | 'failed' | 'queued' | 'processing', progress: number, message: string, data?: CsmSessionResponse}> {
    return new Observable(observer => {
      let attempts = 0;
      const maxAttempts = 60; // 5 minutes max (60 * 5 seconds)
      let startTime = Date.now();

      const poll = () => {
        attempts++;
        const elapsed = Date.now() - startTime;
        const estimatedTotal = 180000; // 3 minutes estimated
        let baseProgress = Math.min((elapsed / estimatedTotal) * 80, 80); // 0% to 80% based on time

        this.getSessionStatus(sessionId).subscribe({
          next: (sessionData) => {
            console.log(`🔍 Poll attempt ${attempts}/${maxAttempts}:`, sessionData);

            let progress = baseProgress;
            let message = '';
            let status = sessionData.status;

            switch (sessionData.status) {
              case 'incomplete':
              case 'queued':
                progress = Math.max(baseProgress, 15);
                message = 'En file d\'attente...';
                status = 'queued';
                break;

              case 'processing':
                progress = Math.max(baseProgress, 40);
                message = 'Génération du modèle 3D en cours...';
                status = 'processing';
                break;

              case 'complete':
                if (sessionData.output?.meshes?.[0]?.data?.glb_url) {
                  progress = 100;
                  message = 'Modèle 3D généré avec succès !';
                  status = 'complete';

                  observer.next({
                    status,
                    progress,
                    message,
                    data: sessionData
                  });
                  observer.complete();
                  return;
                } else {
                  observer.error('Modèle généré mais URL non disponible');
                  return;
                }

              case 'failed':
                observer.error('La génération du modèle 3D a échoué');
                return;

              default:
                progress = Math.max(baseProgress, 20);
                message = `Traitement en cours... (${sessionData.status})`;
            }

            observer.next({
              status,
              progress: Math.round(progress),
              message,
              data: sessionData
            });

            // Continue polling if not complete and not exceeded max attempts
            const shouldContinuePolling = ['incomplete', 'queued', 'processing'].includes(sessionData.status);
            if (attempts < maxAttempts && shouldContinuePolling) {
              setTimeout(poll, 5000); // Poll every 5 seconds
            } else if (attempts >= maxAttempts) {
              observer.error('Timeout - La génération prend trop de temps');
            }
          },
          error: (error) => {
            console.error('❌ Error polling session:', error);
            if (attempts < maxAttempts) {
              // Retry on error with exponential backoff
              const delay = Math.min(5000 * Math.pow(1.5, Math.min(attempts - 1, 5)), 30000);
              setTimeout(poll, delay);
            } else {
              observer.error('Erreur de connexion à l\'API CSM.ai');
            }
          }
        });
      };

      // Start polling immediately
      poll();
    });
  }

  /**
   * Alias for backward compatibility with Tripo3D API
   */
  pollTaskUntilComplete(sessionId: string): Observable<CsmSessionResponse> {
    return this.pollSessionUntilComplete(sessionId);
  }
}
