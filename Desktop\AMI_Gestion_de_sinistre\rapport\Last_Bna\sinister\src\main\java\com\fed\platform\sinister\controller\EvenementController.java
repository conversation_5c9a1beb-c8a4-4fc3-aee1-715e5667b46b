package com.fed.platform.sinister.controller;

import java.util.List;
import java.util.Optional;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.fed.platform.sinister.domain.Evenement;
import com.fed.platform.sinister.repository.EvenementRepository;

@RestController
@RequestMapping("/api/evenements")
@CrossOrigin(origins = "http://localhost:4200")
public class EvenementController {

    private final EvenementRepository evenementRepository;

    public EvenementController(EvenementRepository evenementRepository) {
        this.evenementRepository = evenementRepository;
    }

    @GetMapping
    public List<Evenement> getAllEvenements() {
        return evenementRepository.findAll();
    }

    @GetMapping("/{id}")
    public ResponseEntity<Evenement> getEvenementById(@PathVariable Long id) {
        Optional<Evenement> evenement = evenementRepository.findById(id);
        return evenement.map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    @PostMapping
    public Evenement createEvenement(@RequestBody Evenement evenement) {
        System.out.println("🔍 Backend received event data:");
        System.out.println("  - Code: " + evenement.getCode());
        System.out.println("  - Libelle: " + evenement.getLibelle());
        System.out.println("  - Type de degat: " + evenement.getType_de_degat());
        System.out.println(
                "  - Type de degat (null check): " + (evenement.getType_de_degat() == null ? "NULL" : "NOT NULL"));
        System.out.println("  - Tier: " + evenement.getTier());
        System.out.println("  - Responsabilite: " + evenement.getResponsabilite());

        // Test manual assignment before save
        if (evenement.getType_de_degat() != null) {
            System.out.println("🔍 Type de degat is NOT NULL before save: " + evenement.getType_de_degat());
        } else {
            System.out.println("❌ Type de degat is NULL before save - setting test value");
            evenement.setType_de_degat("TEST_MANUAL");
        }

        Evenement saved = evenementRepository.save(evenement);

        System.out.println("🔍 Backend saved event data:");
        System.out.println("  - ID: " + saved.getId());
        System.out.println("  - Type de degat after save: " + saved.getType_de_degat());

        // Verify in database by re-fetching
        Evenement verified = evenementRepository.findById(saved.getId()).orElse(null);
        if (verified != null) {
            System.out.println("🔍 Verified from DB - Type de degat: " + verified.getType_de_degat());
        }

        return saved;
    }

    @PutMapping("/{id}")
    public ResponseEntity<Evenement> updateEvenement(
            @PathVariable Long id,
            @RequestBody Evenement evenementDetails) {
        System.out.println("🔍 Backend UPDATE received for ID: " + id);
        System.out.println("  - Code: " + evenementDetails.getCode());
        System.out.println("  - Libelle: " + evenementDetails.getLibelle());
        System.out.println("  - Type de degat: " + evenementDetails.getType_de_degat());
        System.out.println("  - Tier: " + evenementDetails.getTier());
        System.out.println("  - Responsabilite: " + evenementDetails.getResponsabilite());

        Optional<Evenement> evenementOptional = evenementRepository.findById(id);

        if (evenementOptional.isEmpty()) {
            return ResponseEntity.notFound().build();
        }

        Evenement evenement = evenementOptional.get();
        System.out.println("🔍 Before update - Type de degat: " + evenement.getType_de_degat());

        evenement.setCode(evenementDetails.getCode());
        evenement.setLibelle(evenementDetails.getLibelle());
        evenement.setType_de_degat(evenementDetails.getType_de_degat());
        evenement.setTier(evenementDetails.getTier());
        evenement.setResponsabilite(evenementDetails.getResponsabilite());

        Evenement updatedEvenement = evenementRepository.save(evenement);
        System.out.println("🔍 After update - Type de degat: " + updatedEvenement.getType_de_degat());

        return ResponseEntity.ok(updatedEvenement);
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteEvenement(@PathVariable Long id) {
        if (!evenementRepository.existsById(id)) {
            return ResponseEntity.notFound().build();
        }

        evenementRepository.deleteById(id);
        return ResponseEntity.noContent().build();
    }

    @PostMapping("/test-db")
    public ResponseEntity<String> testDatabase() {
        try {
            // Create a test event with type_de_degat
            Evenement testEvent = new Evenement();
            testEvent.setCode("TEST_DB_" + System.currentTimeMillis());
            testEvent.setLibelle("Test Database Connection");
            testEvent.setType_de_degat("MATERIEL");
            testEvent.setTier(true);
            testEvent.setResponsabilite(2);

            System.out.println("🧪 Testing database insertion...");
            System.out.println("  - Type de degat before save: " + testEvent.getType_de_degat());

            Evenement saved = evenementRepository.save(testEvent);

            System.out.println("  - Type de degat after save: " + saved.getType_de_degat());
            System.out.println("  - Saved ID: " + saved.getId());

            // Verify by re-fetching
            Evenement verified = evenementRepository.findById(saved.getId()).orElse(null);
            if (verified != null) {
                System.out.println("  - Type de degat verified from DB: " + verified.getType_de_degat());
                return ResponseEntity.ok("✅ Database test successful. Type de degat: " + verified.getType_de_degat());
            } else {
                return ResponseEntity.ok("❌ Could not verify saved event");
            }

        } catch (Exception e) {
            System.out.println("❌ Database test failed: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.ok("❌ Database test failed: " + e.getMessage());
        }
    }
}