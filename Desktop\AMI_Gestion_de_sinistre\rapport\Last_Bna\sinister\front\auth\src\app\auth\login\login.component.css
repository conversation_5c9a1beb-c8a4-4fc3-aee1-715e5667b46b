:root {
  --primary-color: #4361ee;
  --primary-hover: #3a56d4;
  --text-color: #2b2d42;
  --light-gray: #f8f9fa;
  --medium-gray: #e9ecef;
  --dark-gray: #6c757d;
  --error-color: #e63946;
  --white: #ffffff;
  --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  --transition: all 0.3s ease;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Gilmer Bold', sans-serif;
  color: var(--text-color);
  line-height: 1.6;
  background-color: var(--light-gray);
}

.login-container {
  display: flex;
  flex-wrap: wrap;
  min-height: 100vh;
  width: 100%;
}

.image-section {
  flex: 1 1 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem;
  background-color: #cde8e5;
}

.logo-wrapper {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}


.logo-image {
  width: 100%;
  height: auto;
  max-height: 300px;
  object-fit: contain;
  transform-style: preserve-3d;
  animation: spin3D 2s linear forwards;
  position: relative;
  z-index: 2;
}

@keyframes shadowPulse {
  0%, 100% {
    transform: scaleX(1);
    opacity: 0.3;
  }
  50% {
    transform: scaleX(1.2);
    opacity: 0.5;
  }
}

@keyframes spin3D {
  0% {
    transform: rotateY(0deg);
  }
  100% {
    transform: rotateY(360deg);
  }
}

/* Form Section */
.form-section {
  flex: 1 1 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem;
  background-color: var(--white);
}

.form-wrapper {
  width: 100%;
  max-width: 400px;
}

.form-title {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  color: var(--text-color);
}

.form-subtitle {
  color: var(--dark-gray);
  margin-bottom: 2rem;
  font-size: 0.9rem;
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-label {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--text-color);
}

.form-input {
  padding: 0.8rem 1rem;
  border: 1px solid black;
  border-radius: 6px;
  font-size: 1rem;
  transition: var(--transition);
}

.form-input:focus {
  outline: none;
  border-color: var(--text-color);
  box-shadow: 0 0 0 2px rgba(67, 97, 238, 0.2);
}

.error-message {
  color: var(--error-color);
  font-size: 0.8rem;
  height: 1rem;
}

.submit-button {
  padding: 0.8rem 1.5rem;
  background-color: #099e8a;
  color: var(--white);
  border: 1px solid #099e8a;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  width: 100%;
}

.submit-button:hover {
  background-color: #088775;
  border-color: #088775;
}

.submit-button:disabled {
  background-color: #b2dfdb;
  border-color: #b2dfdb;
  cursor: not-allowed;
}

.button-icon {
  font-size: 1.2rem;
}

.additional-options {
  margin-top: 1.5rem;
  text-align: center;
}

.forgot-password {
  color: var(--primary-color);
  text-decoration: none;
  font-size: 0.9rem;
  display: inline-block;
  margin-bottom: 1rem;
}

.forgot-password:hover {
  text-decoration: underline;
}

.signup-text {
  color: var(--dark-gray);
  font-size: 0.9rem;
}

.signup-link {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 600;
}

.signup-link:hover {
  text-decoration: underline;
}

@media (max-width: 992px) {
  .login-container {
    flex-direction: column;
  }

  .image-section,
  .form-section {
    flex: 1 1 100%;
    padding: 1.5rem;
  }

  .logo-image {
    max-height: 200px;
    width: 70%;
  }
}

@media (max-width: 768px) {
  .form-wrapper {
    padding: 0 15px;
  }

  .form-title {
    font-size: 1.7rem;
  }

  .form-subtitle {
    font-size: 0.85rem;
  }

  .form-input,
  .submit-button {
    padding: 0.7rem 1rem;
  }

  .logo-image {
    width: 80%;
  }
}

@media (max-width: 576px) {
  .form-title {
    font-size: 1.5rem;
  }

  .logo-image {
    max-height: 180px;
    width: 90%;
  }

  .form-input,
  .submit-button {
    padding: 0.6rem 1rem;
  }
}

@media (max-width: 400px) {
  .form-title {
    font-size: 1.4rem;
  }

  .form-subtitle,
  .signup-text,
  .forgot-password {
    font-size: 0.8rem;
  }
}

/* Secondary Logo Responsive */
.logo-wrapper2 {
  position: absolute;
  right: 30px;
  top: 30px;
}

.responsive-logo {
  max-width: 150px;
  height: auto;
  display: block;
}

@media (max-width: 992px) {
  .logo-wrapper2 {
    position: relative;
    right: auto;
    top: 10px;
    text-align: center;
    margin-bottom: 1rem;
  }

  .responsive-logo {
    margin: 0 auto;
    max-width: 120px;
  }
}

#logoCanvas {
  width: 100%;
  height: auto;
  max-width: 300px;
  display: block;
  margin: 0 auto;
}
