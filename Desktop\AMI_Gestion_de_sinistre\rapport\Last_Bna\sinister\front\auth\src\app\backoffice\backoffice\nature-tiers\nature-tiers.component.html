<!-- nature-tiers.component.html -->
<div class="container">
  <div class="header">
    <h2>Gestion des Natures Tiers</h2>
    <button class="create-btn" (click)="openModal()">
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
        <path d="M12 5V19M5 12H19" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
      Créer Nature Tiers
    </button>
  </div>

  <div class="table-container">
    <div class="table-responsive">
      <table>
        <thead>
          <tr>
            <th>Code</th>
            <th>Libellé</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let nature of natures">
            <td>{{ nature.code }}</td>
            <td>{{ nature.libelle }}</td>
            <td class="actions">
              <button class="icon-btn edit" (click)="editNature(nature)">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                  <path d="M11 4H4C3.46957 4 2.96086 4.21071 2.58579 4.58579C2.21071 4.96086 2 5.46957 2 6V20C2 20.5304 2.21071 21.0391 2.58579 21.4142C2.96086 21.7893 3.46957 22 4 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V13" stroke="#1C3F93" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  <path d="M18.5 2.50001C18.8978 2.10219 19.4374 1.87869 20 1.87869C20.5626 1.87869 21.1022 2.10219 21.5 2.50001C21.8978 2.89784 22.1213 3.4374 22.1213 4.00001C22.1213 4.56262 21.8978 5.10219 21.5 5.50001L12 15L8 16L9 12L18.5 2.50001Z" stroke="#1C3F93" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </button>
              <button class="icon-btn delete" (click)="deleteNature(nature.code)">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                  <path d="M3 6H5H21" stroke="#FF4B55" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  <path d="M8 6V4C8 3.46957 8.21071 2.96086 8.58579 2.58579C8.96086 2.21071 9.46957 2 10 2H14C14.5304 2 15.0391 2.21071 15.4142 2.58579C15.7893 2.96086 16 3.46957 16 4V6M19 6V20C19 20.5304 18.7893 21.0391 18.4142 21.4142C18.0391 21.7893 17.5304 22 17 22H7C6.46957 22 5.96086 21.7893 5.58579 21.4142C5.21071 21.0391 5 20.5304 5 20V6H19Z" stroke="#FF4B55" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </button>
            </td>
          </tr>
          <!-- Lignes de test pour vérifier le défilement -->
          <tr><td>TEST1</td><td>Test nature tiers 1</td><td class="actions"><button class="icon-btn edit">✏️</button></td></tr>
          <tr><td>TEST2</td><td>Test nature tiers 2</td><td class="actions"><button class="icon-btn edit">✏️</button></td></tr>
          <tr><td>TEST3</td><td>Test nature tiers 3</td><td class="actions"><button class="icon-btn edit">✏️</button></td></tr>
          <tr><td>TEST4</td><td>Test nature tiers 4</td><td class="actions"><button class="icon-btn edit">✏️</button></td></tr>
          <tr><td>TEST5</td><td>Test nature tiers 5</td><td class="actions"><button class="icon-btn edit">✏️</button></td></tr>
          <tr><td>TEST6</td><td>Test nature tiers 6</td><td class="actions"><button class="icon-btn edit">✏️</button></td></tr>
          <tr><td>TEST7</td><td>Test nature tiers 7</td><td class="actions"><button class="icon-btn edit">✏️</button></td></tr>
          <tr><td>TEST8</td><td>Test nature tiers 8</td><td class="actions"><button class="icon-btn edit">✏️</button></td></tr>
          <tr><td>TEST9</td><td>Test nature tiers 9</td><td class="actions"><button class="icon-btn edit">✏️</button></td></tr>
          <tr><td>TEST10</td><td>Test nature tiers 10</td><td class="actions"><button class="icon-btn edit">✏️</button></td></tr>
          <tr><td>TEST11</td><td>Test nature tiers 11</td><td class="actions"><button class="icon-btn edit">✏️</button></td></tr>
          <tr><td>TEST12</td><td>Test nature tiers 12</td><td class="actions"><button class="icon-btn edit">✏️</button></td></tr>
        </tbody>
      </table>
    </div>
  </div>
</div>

    <!-- Modal -->
    <div *ngIf="showModal" class="modal d-block" tabindex="-1">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">{{ isEdit ? 'Modifier' : 'Créer' }} Nature Tiers</h5>
            <button type="button" class="btn-close" (click)="closeModal()"></button>
          </div>
          <div class="modal-body">
            <form>
              <div class="mb-3">
                <label for="code" class="form-label">Code</label>
                <input type="text" class="form-control" [(ngModel)]="form.code" name="code" id="code" [readonly]="isEdit">
              </div>
              <div class="mb-3">
                <label for="libelle" class="form-label">Libelle</label>
                <input type="text" class="form-control" [(ngModel)]="form.libelle" name="libelle" id="libelle">
              </div>
            </form>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" (click)="closeModal()">Annuler</button>
            <button type="button" class="btn btn-primary" (click)="saveNature()">{{ isEdit ? 'Modifier' : 'Créer' }}</button>
          </div>
        </div>
      </div>
    </div>
