import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { TypeDegat, TypeDegatService } from '../type-de-degat/type-degat.service';
import { Evenement } from './evenement.model';
import { EvenementService } from './evenement.service';

@Component({
  selector: 'app-evenements',
  templateUrl: './evenements.component.html',
  styleUrls: ['./evenements.component.css'],
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule]
})
export class EvenementsComponent implements OnInit {
  events: Evenement[] = [];
  typeDegats: TypeDegat[] = [];
  eventForm: FormGroup;
  detailsForm: FormGroup;
  showModal = false;
  showDetailsModal = false;
  editingEvent: Evenement | null = null;
  selectedEvent: Evenement | null = null;
  isLoading = false;
  isLoadingTypeDegats = false;
  errorMessage: string | null = null;
  hasChanges = false;
  originalFormValue: any = null;

  constructor(
    private fb: FormBuilder,
    private evenementService: EvenementService,
    private typeDegatService: TypeDegatService
  ) {
    this.eventForm = this.fb.group({
      code: ['', [Validators.required, Validators.maxLength(10)]],
      libelle: ['', Validators.required],
      type_de_degat: [''],
      tier: [false],
      responsabilite: ['']
    });

    this.detailsForm = this.fb.group({
      code: [''],
      libelle: [''],
      type_de_degat: [''],
      tier: [false],
      responsabilite: ['']
    });

    // Watch for changes in details form
    this.detailsForm.valueChanges.subscribe(() => {
      this.checkForChanges();
    });
  }

  ngOnInit(): void {
    this.loadEvents();
    this.loadTypeDegats();
  }

  loadEvents(): void {
    this.isLoading = true;
    this.evenementService.getAll().subscribe({
      next: (events) => {
        this.events = events;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading events', error);
        this.errorMessage = 'Failed to load events. Please try again later.';
        this.isLoading = false;
      }
    });
  }

  loadTypeDegats(): void {
    this.isLoadingTypeDegats = true;
    this.typeDegatService.getAll().subscribe({
      next: (typeDegats) => {
        this.typeDegats = typeDegats;
        this.isLoadingTypeDegats = false;
        console.log('✅ Type dégâts loaded:', typeDegats);
      },
      error: (error) => {
        console.error('❌ Error loading type dégâts:', error);
        this.isLoadingTypeDegats = false;
      }
    });
  }

  openModal(): void {
    this.showModal = true;
    this.editingEvent = null;
    this.eventForm.reset();
    this.errorMessage = null;
  }

  closeModal(): void {
    this.showModal = false;
    this.editingEvent = null;
    this.eventForm.reset();
    this.errorMessage = null;
  }

  editEvent(event: Evenement): void {
    this.editingEvent = event;
    this.eventForm.patchValue({
      code: event.code,
      libelle: event.libelle
    });
    this.showModal = true;
  }

  deleteEvent(event: Evenement): void {
    if (confirm('Êtes-vous sûr de vouloir supprimer cet événement ?') && event.id) {
      this.isLoading = true;
      this.evenementService.delete(event.id).subscribe({
        next: () => {
          this.events = this.events.filter(e => e.id !== event.id);
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error deleting event', error);
          this.errorMessage = 'Failed to delete event. Please try again.';
          this.isLoading = false;
        }
      });
    }
  }

  onSubmit(): void {
    if (this.eventForm.invalid) return;

    const formValue = this.eventForm.value;
    console.log('📝 Form values before creating event:', formValue);
    console.log('🔍 Raw type_de_degat value:', formValue.type_de_degat);
    console.log('🔍 Type of type_de_degat:', typeof formValue.type_de_degat);
    console.log('🔍 Is empty string?', formValue.type_de_degat === '');
    console.log('🔍 Is null?', formValue.type_de_degat === null);
    console.log('🔍 Is undefined?', formValue.type_de_degat === undefined);

    const processedTypeDegat = formValue.type_de_degat && formValue.type_de_degat.trim() !== '' ? formValue.type_de_degat : null;
    console.log('🔍 Processed type_de_degat:', processedTypeDegat);

    const eventData: Evenement = {
      code: formValue.code,
      libelle: formValue.libelle,
      type_de_degat: processedTypeDegat,
      tier: formValue.tier || false,
      responsabilite: formValue.responsabilite && formValue.responsabilite !== '' ? parseInt(formValue.responsabilite) : null
    };

    console.log('📤 Event data being sent to backend:', eventData);
    console.log('📤 JSON stringified:', JSON.stringify(eventData));
    this.isLoading = true;
    this.errorMessage = null;

    this.evenementService.create(eventData).subscribe({
      next: (newEvent) => {
        console.log('✅ Event created successfully:', newEvent);
        this.events.push(newEvent);
        this.closeModal();
        this.isLoading = false;
      },
      error: (error) => {
        console.error('❌ Error creating event:', error);
        this.errorMessage = 'Failed to create event. Please try again.';
        this.isLoading = false;
      }
    });
  }

  // View event details in popup - Load fresh data from backend
  viewEventDetails(event: Evenement): void {
    if (!event.id) return;

    this.isLoading = true;
    this.errorMessage = null;

    // Fetch fresh data from backend to ensure we have all fields
    this.evenementService.getById(event.id).subscribe({
      next: (fullEvent) => {
        console.log('📥 Raw event data from backend:', fullEvent);
        console.log('🔍 Type de degat value:', fullEvent.type_de_degat);
        console.log('🔍 Type de degat type:', typeof fullEvent.type_de_degat);
        console.log('🔍 Responsabilite value:', fullEvent.responsabilite);
        console.log('🔍 Responsabilite type:', typeof fullEvent.responsabilite);
        console.log('🔍 Tier value:', fullEvent.tier);
        console.log('🔍 Tier type:', typeof fullEvent.tier);

        this.selectedEvent = fullEvent;
        // Patch form values
        const patchData = {
          code: fullEvent.code || '',
          libelle: fullEvent.libelle || '',
          type_de_degat: fullEvent.type_de_degat || '',
          tier: fullEvent.tier || false,
          responsabilite: fullEvent.responsabilite !== null && fullEvent.responsabilite !== undefined ? fullEvent.responsabilite : ''
        };

        console.log('📝 Responsabilite patch value:', patchData.responsabilite);

        console.log('📝 Patch data being applied:', patchData);
        this.detailsForm.patchValue(patchData);

        console.log('📝 Form values after patch:', this.detailsForm.value);
        console.log('📝 Type de degat control value:', this.detailsForm.get('type_de_degat')?.value);
        console.log('📝 Responsabilite control value:', this.detailsForm.get('responsabilite')?.value);
        console.log('📝 Tier control value:', this.detailsForm.get('tier')?.value);

        // Force change detection for the select
        setTimeout(() => {
          console.log('📝 Type de degat control value after timeout:', this.detailsForm.get('type_de_degat')?.value);
          console.log('📝 Responsabilite control value after timeout:', this.detailsForm.get('responsabilite')?.value);
        }, 100);
        this.originalFormValue = this.detailsForm.value;
        this.hasChanges = false;
        this.showDetailsModal = true;
        this.isLoading = false;
        console.log('✅ Loaded event details:', fullEvent);
      },
      error: (error) => {
        console.error('❌ Error loading event details:', error);
        this.errorMessage = 'Failed to load event details. Please try again.';
        this.isLoading = false;
      }
    });
  }

  // Close details modal
  closeDetailsModal(): void {
    this.showDetailsModal = false;
    this.selectedEvent = null;
    this.detailsForm.reset();
    this.hasChanges = false;
    this.errorMessage = null;
  }

  // Check for changes in details form
  checkForChanges(): void {
    if (this.originalFormValue) {
      const currentValue = this.detailsForm.value;
      this.hasChanges = JSON.stringify(currentValue) !== JSON.stringify(this.originalFormValue);
    }
  }

  // Submit details form changes
  onDetailsSubmit(): void {
    console.log('🔄 onDetailsSubmit called');
    console.log('🔍 selectedEvent:', this.selectedEvent);
    console.log('🔍 hasChanges:', this.hasChanges);

    if (!this.selectedEvent || !this.selectedEvent.id || !this.hasChanges) {
      console.log('❌ Cannot submit: missing event, ID, or no changes');
      return;
    }

    const formValue = this.detailsForm.value;
    console.log('📝 Form values before updating event:', formValue);
    const eventData: Evenement = {
      code: formValue.code,
      libelle: formValue.libelle,
      type_de_degat: formValue.type_de_degat && formValue.type_de_degat.trim() !== '' ? formValue.type_de_degat : null,
      tier: formValue.tier || false,
      responsabilite: formValue.responsabilite && formValue.responsabilite !== '' ? parseInt(formValue.responsabilite) : null
    };

    this.isLoading = true;
    this.errorMessage = null;

    this.evenementService.update(this.selectedEvent.id, eventData).subscribe({
      next: (updatedEvent) => {
        const index = this.events.findIndex(e => e.id === updatedEvent.id);
        if (index !== -1) {
          this.events[index] = updatedEvent;
        }
        this.closeDetailsModal();
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error updating event', error);
        this.errorMessage = 'Failed to update event. Please try again.';
        this.isLoading = false;
      }
    });
  }
}