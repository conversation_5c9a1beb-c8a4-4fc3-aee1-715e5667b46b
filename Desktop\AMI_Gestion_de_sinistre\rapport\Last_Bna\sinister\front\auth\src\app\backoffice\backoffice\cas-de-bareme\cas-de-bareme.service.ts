import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';

export interface CasBareme {
  id: number;
  libelle: string;
  resX: number;
  resY: number;
}

@Injectable({
  providedIn: 'root'
})
export class CasDeBaremeService {
  private apiUrl = '/api/cas-de-bareme';

  constructor(private http: HttpClient) {}

  // Create
  create(casBareme: Omit<CasBareme, 'id'>): Observable<CasBareme> {
    return this.http.post<CasBareme>(this.apiUrl, casBareme);
  }

  // Read All
  getAll(): Observable<CasBareme[]> {
    return this.http.get<CasBareme[]>(this.apiUrl);
  }

  // Read One
  getById(id: number): Observable<CasBareme> {
    return this.http.get<CasBareme>(`${this.apiUrl}/${id}`);
  }

  // Update
  update(id: number, casBareme: CasBareme): Observable<CasBareme> {
    return this.http.put<CasBareme>(`${this.apiUrl}/${id}`, casBareme);
  }

  // Delete
  delete(id: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}`);
  }
}