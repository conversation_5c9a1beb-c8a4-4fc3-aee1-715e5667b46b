.container {
  padding: 24px;
  background: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  margin: 24px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  flex-wrap: wrap;
  gap: 16px;
}

.header h2 {
  color: #1F2937;
  font-size: 24px;
  font-weight: 600;
  margin: 0;
}

.search-container {
  flex: 1;
  max-width: 400px;
  min-width: 250px;
}

.search-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: 12px;
  z-index: 1;
  pointer-events: none;
}

.search-input {
  width: 100%;
  padding: 12px 16px 12px 44px;
  border: 1px solid #D1D5DB;
  border-radius: 8px;
  font-size: 14px;
  background: #FFFFFF;
  transition: all 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: #1C3F93;
  box-shadow: 0 0 0 3px rgba(28, 63, 147, 0.1);
}

.search-input::placeholder {
  color: #9CA3AF;
}

.clear-search {
  position: absolute;
  right: 8px;
  background: none;
  border: none;
  padding: 4px;
  cursor: pointer;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
}

.clear-search:hover {
  background-color: #F3F4F6;
}

.error-message {
  background-color: #FEF2F2;
  border: 1px solid #FECACA;
  color: #DC2626;
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 16px;
  font-size: 14px;
}

.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 48px;
}

.spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #E5E7EB;
  border-top: 3px solid #1C3F93;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.table-responsive {
  overflow-x: auto;
  border-radius: 8px;
  border: 1px solid #E5E7EB;
}

table {
  width: 100%;
  border-collapse: collapse;
  background: #FFFFFF;
}

thead {
  background: #F9FAFB;
}

th {
  padding: 16px;
  text-align: left;
  font-weight: 600;
  color: #374151;
  font-size: 14px;
  border-bottom: 1px solid #E5E7EB;
}

td {
  padding: 16px;
  border-bottom: 1px solid #F3F4F6;
  color: #1F2937;
  font-size: 14px;
}

tr:hover {
  background-color: #F9FAFB;
}

.numero-sinistre {
  font-weight: 600;
  color: #1C3F93;
}

.status-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-en-cours {
  background-color: #FEF3C7;
  color: #D97706;
}

.status-traite {
  background-color: #D1FAE5;
  color: #059669;
}

.status-attente {
  background-color: #DBEAFE;
  color: #2563EB;
}

.actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.icon-btn {
  background: none;
  border: none;
  padding: 8px;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.icon-btn:hover {
  background-color: #F3F4F6;
  transform: scale(1.05);
}

.icon-btn.view:hover {
  background-color: #EBF4FF;
}

.icon-btn.edit:hover {
  background-color: #EBF4FF;
}

.icon-btn.delete:hover {
  background-color: #FEF2F2;
}

.no-data {
  text-align: center;
  padding: 48px 24px;
  color: #6B7280;
}

.no-data svg {
  margin-bottom: 16px;
  opacity: 0.5;
}

.no-data p {
  font-size: 16px;
  margin: 0;
}

@media (max-width: 768px) {
  .container {
    margin: 16px;
    padding: 16px;
  }

  .header {
    flex-direction: column;
    align-items: stretch;
  }

  .search-container {
    max-width: none;
  }

  .table-responsive {
    font-size: 12px;
  }

  th, td {
    padding: 12px 8px;
  }

  .actions {
    flex-direction: column;
    gap: 4px;
  }

  .icon-btn {
    padding: 6px;
  }
}