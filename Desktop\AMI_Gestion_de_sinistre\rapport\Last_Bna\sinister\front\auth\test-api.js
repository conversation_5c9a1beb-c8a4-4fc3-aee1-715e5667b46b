// Test script for CSM.ai API
const API_KEY = '793308e392807ce9d1Ad41F3b41EafDF';
const API_BASE = 'https://api.csm.ai/v3';

// Test with a simple image URL
async function testCSMApi() {
    console.log('🧪 Testing CSM.ai API...');
    console.log('🔑 API Key:', API_KEY.substring(0, 8) + '...');
    
    try {
        // Create a simple test image (1x1 pixel PNG in base64)
        const testImageBase64 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';
        
        const requestBody = {
            type: 'image_to_3d',
            input: {
                image: testImageBase64,
                manual_segmentation: false,
                num_variations: 1,
                model: 'sculpt',
                settings: {
                    geometry_model: 'base',
                    texture_model: 'none',
                    topology: 'tris',
                    resolution: 100000,
                    symmetry: 'off',
                    scaled_bbox: [-1, -1, -1],
                    preserve_aspect_ratio: false,
                    pivot_point: [0, -0.5, 0]
                }
            }
        };

        console.log('🌐 Sending request to CSM.ai...');
        
        const response = await fetch(`${API_BASE}/sessions/`, {
            method: 'POST',
            headers: {
                'x-api-key': API_KEY,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestBody)
        });

        console.log('📡 Response status:', response.status);
        console.log('📡 Response headers:', Object.fromEntries(response.headers.entries()));

        if (!response.ok) {
            const errorText = await response.text();
            console.error('❌ API Error:', errorText);
            return;
        }

        const result = await response.json();
        console.log('✅ Success! Session created:', result._id);
        console.log('📊 Full response:', JSON.stringify(result, null, 2));
        
        // Test getting session status
        console.log('\n🔍 Testing session status...');
        await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds
        
        const statusResponse = await fetch(`${API_BASE}/sessions/${result._id}`, {
            method: 'GET',
            headers: {
                'x-api-key': API_KEY,
                'Content-Type': 'application/json'
            }
        });

        if (statusResponse.ok) {
            const statusResult = await statusResponse.json();
            console.log('📊 Session status:', statusResult.status);
            console.log('📊 Full status response:', JSON.stringify(statusResult, null, 2));
        } else {
            console.error('❌ Status check failed:', statusResponse.status);
        }

    } catch (error) {
        console.error('❌ Test failed:', error.message);
        console.error('Full error:', error);
    }
}

// Run the test
testCSMApi();
