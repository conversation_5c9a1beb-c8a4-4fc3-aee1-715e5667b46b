/* Container and Layout */
.container {
  padding: 24px;
  height: 100%;
  min-height: 500px;
  display: flex;
  flex-direction: column;
  position: relative;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  flex-shrink: 0;
}

.header h2 {
  font-family: 'Gilmer Bold', sans-serif;
  color: #1C3F93;
  margin: 0;
}

/* Buttons */
.create-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  background: #1C3F93;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 24px;
  cursor: pointer;
  font-family: '<PERSON><PERSON> Bold', sans-serif;
  transition: all 0.3s ease;
}

.create-btn:hover {
  background: #152D69;
  transform: translateY(-1px);
}

.create-btn:disabled {
  background: #E5E7EB;
  cursor: not-allowed;
  transform: none;
}

/* Table Styles */
.table-container {
  flex: 1;
  min-height: 0;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.table-responsive {
  flex: 1;
  overflow-x: auto;
  overflow-y: auto;
  min-height: 0;
  max-height: 400px; /* Hauteur fixe pour forcer le défilement */
}

.table-responsive::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.table-responsive::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.table-responsive::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 3px;
}

.table-responsive::-webkit-scrollbar-thumb:hover {
  background: #555;
}

table {
  width: 100%;
  border-collapse: collapse;
  background: white;
}

th, td {
  padding: 16px;
  text-align: left;
  border-bottom: 1px solid #E5E7EB;
}

th {
  position: sticky;
  top: 0;
  background: #F9FAFB;
  font-family: 'Gilmer Bold', sans-serif;
  color: #6B7280;
  z-index: 1;
}

td {
  font-family: 'Gilmer Regular', sans-serif;
  color: #374151;
  max-width: 400px;
  white-space: normal;
  word-wrap: break-word;
}

tr:hover td {
  background: #F9FAFB;
}

.actions {
  display: flex;
  gap: 8px;
  white-space: nowrap;
}

.icon-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.icon-btn:hover {
  background: #F3F4F6;
  transform: translateY(-1px);
}

.icon-btn.edit:hover {
  background: #EBF5FF;
}

.icon-btn.delete:hover {
  background: #FEE2E2;
}

.icon-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* Modal Styles */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1;
}

.modal-content {
  background: white;
  border-radius: 8px;
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  padding: 24px;
  z-index: 2;
  position: relative;
  animation: modalFadeIn 0.3s ease;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  margin: 20px;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.modal-header h3 {
  font-family: 'Gilmer Bold', sans-serif;
  color: #1C3F93;
  margin: 0;
}

.close-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: #F3F4F6;
  transform: scale(1.1);
}

.close-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* Form Styles */
.form-group {
  margin-bottom: 24px;
}

.form-group label {
  display: block;
  font-family: 'Gilmer Bold', sans-serif;
  font-size: 14px;
  color: #6B7280;
  margin-bottom: 8px;
}

.input-wrapper {
  position: relative;
}

.input-wrapper input,
.input-wrapper textarea {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #E5E7EB;
  border-radius: 8px;
  font-family: 'Gilmer Regular', sans-serif;
  font-size: 14px;
  color: #374151;
  transition: all 0.2s ease;
}

.input-wrapper textarea {
  min-height: 100px;
  resize: vertical;
}

.input-wrapper input:focus,
.input-wrapper textarea:focus {
  outline: none;
  border-color: #1C3F93;
  box-shadow: 0 0 0 3px rgba(28, 63, 147, 0.1);
}

.required {
  color: #FF4B55;
}

.error-message {
  color: #FF4B55;
  font-size: 12px;
  margin-top: 4px;
  animation: shake 0.4s ease;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-4px); }
  75% { transform: translateX(4px); }
}

/* Form Layout */
.form-row {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
}

.form-row .form-group {
  flex: 1;
  margin-bottom: 0;
}

/* Footer Buttons */
.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  margin-top: 24px;
}

.btn-secondary {
  background: white;
  border: 1px solid #E5E7EB;
  color: #6B7280;
  padding: 12px 24px;
  border-radius: 24px;
  cursor: pointer;
  font-family: 'Gilmer Bold', sans-serif;
  transition: all 0.2s ease;
}

.btn-primary {
  background: #1C3F93;
  border: none;
  color: white;
  padding: 12px 24px;
  border-radius: 24px;
  cursor: pointer;
  font-family: 'Gilmer Bold', sans-serif;
  transition: all 0.2s ease;
}

.btn-primary:disabled {
  background: #E5E7EB;
  cursor: not-allowed;
}

.btn-secondary:hover {
  background: #F9FAFB;
  transform: translateY(-1px);
}

.btn-primary:hover:not(:disabled) {
  background: #152D69;
  transform: translateY(-1px);
}

/* Loading States */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(28, 63, 147, 0.2);
  border-radius: 50%;
  border-top-color: #1C3F93;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.spinner-btn {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s ease-in-out infinite;
  margin-right: 8px;
  vertical-align: middle;
}

/* Alerts */
.alert {
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.alert-success {
  background-color: #ECFDF5;
  color: #065F46;
  border: 1px solid #A7F3D0;
}

.alert-danger {
  background-color: #FEF2F2;
  color: #B91C1C;
  border: 1px solid #FECACA;
}

.alert .close {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 18px;
  color: inherit;
}

.no-data {
  text-align: center;
  padding: 40px;
  color: #6B7280;
  font-family: 'Gilmer Regular', sans-serif;
}