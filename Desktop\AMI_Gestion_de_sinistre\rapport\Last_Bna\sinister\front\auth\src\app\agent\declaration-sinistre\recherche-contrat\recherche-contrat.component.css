/* === Base styles === */
body {
    background-color: #fbfafa;
    margin: 0;
    padding: 0;
    width: 100%;
    min-height: 200vh;
    font-family: 'Gilmer Bold', sans-serif;
  }
.form-container {
  display: flex;
  max-width: 2000px;
  margin: 1px;
  padding: 30px;
}

@media (max-width: 900px) {
  .form-container {
    padding: 10px;
    overflow-x: auto;
  }
}

/* Form content styles */
.form-content {
  padding: 2rem;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  max-width: 800px;
  margin: 0 auto;
}

.form-title {
  color: #1a237e;
  font-size: 1.5rem;
  margin-bottom: 2rem;
  font-weight: 600;
}

.form-row {
  display: flex;
  gap: 2rem;
  margin-bottom: 1.5rem;
}

.form-group {
  flex: 1;
}

.form-group label {
  display: block;
  font-family: 'Gilmer Bold', sans-serif;
  font-size: 14px;
  color: #6B6B6B;
  margin-bottom: 8px;
}

.required {
  color: #f44336;
  margin-left: 4px;
}

.input-container,
.select-container {
  position: relative;
  width: 100%;
}

input[type="text"],
input[type="number"],
input[type="email"],
input[type="tel"],
input[type="date"],
textarea {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #E0E0E0;
  border-top-right-radius: 24px;
  border-bottom-left-radius: 24px;
  border-top-left-radius: 0;
  border-bottom-right-radius: 0;
  font-family: 'Gilmer Bold', sans-serif;
  font-size: 14px;
  color: #323232;
  height: 56px;
  transition: border-color 0.2s;
}

input[type="text"]:focus,
select:focus {
  outline: none;
  border-color: #1a237e;
}

.calendar-icon,
.dropdown-icon {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #757575;
  pointer-events: none;
}

.form-section {
  margin-bottom: 1.5rem;
}

.radio-group {
  display: flex;
  gap: 2rem;
  margin-top: 0.5rem;
}

.radio-option {
  flex: 1;
  display: flex;
  align-items: center;
  padding: 1rem 1.5rem;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
  background: #fff;
}

.radio-option:hover {
  background: #f5f5f5;
}

.radio-option input[type="radio"] {
  display: none;
}

.radio-option label {
  display: flex;
  align-items: center;
  margin: 0;
  cursor: pointer;
  width: 100%;
}

.radio-icon {
  margin-right: 8px;
}

.radio-option svg {
  margin-right: 8px;
  width: 24px;
  height: 24px;
}

.radio-option input[type="radio"]:checked + label {
  color: #1a237e;
}

.radio-option input[type="radio"]:checked + label .radio-icon {
  color: #1a237e;
}

.radio-option input[type="radio"]:checked + label {
  color: #1C3F93;
  font-weight: 500;
}

/* Footer Action Bar */


.footer-action-bar-container {
    width:820px;
    max-width: 880px;
    min-height: 92px;
    background: #fff;
    border-radius: 24px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 24px;
    margin-top: 2zpx;
    box-shadow: 0 2px 8px #F4F4F4;
    padding: 0 32px 0 0;
    position: relative;
    right: 40px;
    top: 60px;
    border-bottom-right-radius: 16px;
    border-bottom-left-radius: 16px;
    background: var(--white, #FFFFFF);
  }
  
  @media (max-width: 900px) {
    .footer-action-bar-container {
      width: 98vw;
      min-height: auto;
      border-radius: 18px;
      padding: 0 8px;
      justify-content: center;
    }
    .footer-btn {
      width: 90vw;
      justify-content: center;
    }
  }


  .footer-btn {
    font-family: 'Gilmer Bold', sans-serif;
    font-size: 18px;
    border: none;
    outline: none;
    background: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    transition: color 0.2s, background 0.2s;
    height: 48px;
    padding: 0 16px;
    margin: 0;
  }
  .footer-btn-retour {
    color: #1C3F93;
    background: none;
    border-radius: 0 24px 24px 0;
    font-weight: 600;
  }
  .footer-btn-retour:hover {
    text-decoration: underline;
    background: #F4F4F4;
  }
  .footer-btn-continuer {
    color: #fff;
    background: #00A887;
    width: 135px;
    height: 48px;
    border-top-right-radius: 24px;
    border-bottom-left-radius: 24px;
    border-top-left-radius: 0;
    border-bottom-right-radius: 0;
    font-weight: 700;
    box-shadow: none;
    padding: 0 16px;
    gap: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .footer-btn-continuer:hover {
    background: #00916f;
  }
  .footer-btn-icon-left {
    font-size: 22px;
    margin-right: 6px;
    line-height: 1;
  }
  .footer-btn-icon-right {
    font-size: 22px;
    margin-left: 6px;
    line-height: 1;
  }

.radio-option:has(input[type="radio"]:checked) {
  border-color: #1a237e;
  background: rgba(26, 35, 126, 0.05);
}

/* Contracts Section */
.contracts-section {
  position: relative;
  width: 880px;
  margin: 220px auto 0;
  transform: translateX(-20px);
}

.contracts-title {
font-family: 'Gilmer Bold', sans-serif;
  font-weight: 700;
  font-size: 25px;
  line-height: 120%;
  color: #0A1633;
  margin-bottom: 16px;
}

.contract-card {
  position: relative;
  width: 880px;
  height: 104px;
  background: #FFFFFF;
  border-radius: 16px;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  padding: 24px;
}

.contract-info-left,
.contract-info-right {
  width: 280px;
  height: 56px;
}

.client-name {
font-family: 'Gilmer Bold', sans-serif;
  font-size: 20px;
  line-height: 120%;
  color: #009B79;
  margin-bottom: 8px;
}

.client-code,
.contract-number {
  font-family: 'Gilmer Bold', sans-serif;
  font-size: 20px;
  line-height: 120%;
  color: #8F8F8F;
}
.step-title {
    font-family: 'Gilmer Bold', sans-serif;
    font-size: 16px;
    color: #B9C3DE;
    margin-bottom: 5px;
  }

.contract-label {
font-family: 'Gilmer Bold', sans-serif;
  font-size: 20px;
  line-height: 120%;
  color: #1C3F93;
  margin-bottom: 8px;
}

.contract-divider {
  width: 1px;
  height: 64px;
  background: #B9C3DE;
  margin: 0 24px;
}

.select-btn {
  position: absolute;
  right: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 16px;
  gap: 8px;
  width: 132px;
  height: 40px;
  background: #1C3F93;
  border-radius: 0 24px;
  border: none;
  font-family: 'Gilmer Bold', sans-serif;
  font-size: 16px;
  line-height: 120%;
  color: #E6F5F2;
  cursor: pointer;
}

.select-btn:hover {
  background: #15307A;
}

/* Header */
.header h1 {
  font-family: 'Gilmer Bold', sans-serif;
  font-size: 25px;
  color: #0A1633;
  position: relative;
  left: 300px;
  bottom: 20px;
}

/* Progress steps */
.progress-steps {
  width: 250px;
  margin-right: 50px;
  position: relative;
  right: 180px;
}

.step {
  position: relative;
  margin-bottom: 30px;
  padding-left: 40px;
}

.step-number {
  position: absolute;
  left: 0;
  top: 0;
  width: 24px;
  height: 24px;
  background-color: #B9C3DE;
  border-radius: 50%;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: 'Gilmer Bold', sans-serif;
  font-size: 16px;
}

.step.active .step-number {
  background-color: #1C3F93;
}

.step-title {
  font-family: 'Gilmer Bold', sans-serif;
  font-size: 16px;
  color: #B9C3DE;
  margin-bottom: 5px;
}

.step.active .step-title {
  color: #1C3F93;
}

.step-subtitle {
  font-family: 'Gilmer Bold', sans-serif;
  font-size: 13px;
  margin-top: 5px;
}

.step-subtitle .green {
  color: #009B79;
  display: block;
}

.step-subtitle .blue {
  color: #1C3F93;
  display: block;
}

/* Form container styles */
.form-container {
  display: flex;
  max-width: 2000px;
  margin: 1px;
  padding: 30px;
}

.form-content {
  position: absolute;
  width: 880px;
  height: 460px;
  min-height: 176px;
  left: calc(50% - 440px + 2px);
  top: 190px;
  background: white;
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.form-content h2 {
    font-family: 'Gilmer Bold', sans-serif;
  font-size: 20px;
  font-weight: 700;
  color: #1C3F93;
  margin-bottom: 30px;
}

/* Form rows */
.form-row {
  display: flex;
  gap: 30px;
  margin-bottom: 20px;
  align-items: flex-start;
}

.form-group {
  flex: 1;
  max-width: 398px;
}

/* Input Styles */
.select-container {
  position: relative;
  width: 100%;
}

.custom-select,
.select-container select {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #E0E0E0;
  border-top-right-radius: 24px;
  border-bottom-left-radius: 24px;
  border-top-left-radius: 0;
  border-bottom-right-radius: 0;
  font-family: 'Gilmer Bold', sans-serif;
  font-size: 14px;
  color: #323232;
  appearance: none;
  cursor: pointer;
  height: 56px;
}

.custom-select:focus,
.select-container select:focus {
  border-color: #1C3F93;
  outline: none;
}

.dropdown-icon {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: #1C3F93;
  pointer-events: none;
  font-size: 12px;
}

/* Material Date Picker Customization */
::ng-deep .mat-mdc-form-field {
  width: 100% !important;
}

::ng-deep .mat-mdc-text-field-wrapper {
  background-color: white !important;
  border: 1px solid #E0E0E0 !important;
  border-top-right-radius: 24px !important;
  border-bottom-left-radius: 24px !important;
  border-top-left-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
  padding: 0 !important;
}

::ng-deep .mat-mdc-form-field-flex {
  padding: 0 16px !important;
  height: 56px !important;
  align-items: center !important;
}

::ng-deep .mat-mdc-text-field-wrapper.mdc-text-field--outlined .mat-mdc-form-field-infix {
  padding: 16px 0 !important;
  min-height: 56px !important;
}

::ng-deep .mat-mdc-form-field-icon-suffix {
  margin-left: 8px !important;
}

::ng-deep .mat-mdc-form-field:not(.mat-form-field-disabled) .mat-mdc-text-field-wrapper {
  background-color: white !important;
}

::ng-deep .mat-mdc-form-field-subscript-wrapper {
  display: none;
}

::ng-deep .mat-mdc-form-field-icon-suffix {
  color: #1C3F93;
}

/* Label Styles */
label {
  display: block;
  margin-bottom: 8px;
  font-family: 'Gilmer Bold', sans-serif;
  font-size: 14px;
  color: #6B6B6B;
}

.required {
  color: #E53935;
  margin-left: 2px;
}

/* Responsive styles */
::ng-deep .mat-mdc-form-field-appearance-outline .mat-mdc-form-field-outline {
  background-color: white;
}

::ng-deep .mat-mdc-form-field {
  width: 100%;
}

::ng-deep .mat-mdc-text-field-wrapper {
  border-radius: 8px !important;
}

::ng-deep .mat-mdc-form-field-appearance-outline .mat-mdc-form-field-outline {
  color: #E0E0E0;
}

::ng-deep .mat-mdc-form-field-appearance-outline.mat-focused .mat-mdc-form-field-outline-thick {
  color: #1C3F93;
}

.required {
  color: #E53935;
  font-weight: bold;
  margin-left: 2px;
}

/* Responsive design */
@media (max-width: 900px) {
  .form-container {
    padding: 10px;
    overflow-x: auto;
  }
  
  .form-content {
    width: calc(100% - 40px);
    left: 20px;
    right: 20px;
    margin: 0;
  }

  .form-row {
    flex-direction: column;
    gap: 20px;
  }
}

@media (max-width: 600px) {
  .form-container {
    padding: 2px;
  }

  .form-content {
    width: calc(100% - 20px);
    left: 10px;
    right: 10px;
    padding: 15px;
  }

  .form-row {
    gap: 15px;
  }
}

.top-navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 1.5rem;
    background-color: white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    flex-wrap: wrap;
  }
  
  img {
    width: 225px;
    max-width: 100%;
  }
  
  .actions {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    flex-wrap: wrap;
  }
  
  .icon-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 0;
  }
  
  .notification-container {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .notification-icon-wrapper {
    position: relative;
    display: inline-flex;
  }
  
  .notification-icon {
    width: 24px;
    height: 24px;
    color: #1A3E8D;
  }
  
  .notification-badge {
    background-color: #1A3E8D;
    color: white;
    border-radius: 9999px;
    height: 16px;
    width: 16px;
    font-size: 0.625rem;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: -6px;
    right: -6px;
  }
  
  .profile {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex-wrap: nowrap;
  }
  
  .profile-circle {
    background-color: #1A3E8D;
    color: white;
    border-radius: 50%;
    height: 2rem;
    width: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    text-transform: lowercase;
    font-size: 1rem;
  }
  
  .profile-name {
    font-size: 0.875rem;
    color: #4B5563;
  }
  
  .bottom-bar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 30px;
    background: #1A3E8D;
    margin-bottom: 20px;
    position: relative;
    height: 60px;
    border-bottom-right-radius: 48px;
  }
  
  .bottom-bar-title {
    font-family: 'Gilmer Bold', sans-serif;
    font-size: 23px;
    color: white;
    position: relative;
    left: 250px;
    bottom: 10px;
  }
  
  .svg {
    position: relative;
    right: 980px;
    top: 15px;
    max-width: 100%;
    height: auto;
    display: inline-block;
  }
  .bottom-bar-close {
    font-size: 1rem;
    font-weight: bold;
    cursor: pointer;
    position: relative;
    right: 300px;
  }


  
  
  /* === Responsive Media Queries === */
  @media (max-width: 1440px) {
    .form-content {
      width: 80%;
      left: 10%;
      max-width: 880px;
    }

    .contracts-section {
      width: 80%;
      max-width: 880px;
      margin: 120px auto 0;
      transform: translateX(0);
    }

    .contract-card {
      width: 100%;
    }

    .header h1 {
      left: 10%;
    }

    .bottom-bar {
      width: 80%;
      left: 10%;
    }
  }

  @media (max-width: 1024px) {
    .form-content {
      width: 90%;
      left: 5%;
    }

    .contracts-section {
      width: 90%;
      margin: 100px auto 0;
    }

    .contract-card {
      padding: 16px;
    }

    .contract-info-left,
    .contract-info-right {
      width: auto;
      flex: 1;
    }

    .header h1 {
      left: 5%;
    }

    .bottom-bar {
      width: 90%;
      left: 5%;
    }
  }

  @media (max-width: 768px) {
    .form-content {
      width: 95%;
      left: 2.5%;
      padding: 16px;
    }

    .contracts-section {
      width: 95%;
      margin: 80px auto 0;
    }

    .contract-card {
      flex-direction: column;
      height: auto;
      gap: 16px;
      align-items: flex-start;
    }

    .contract-divider {
      display: none;
    }

    .select-btn {
      position: relative;
      right: 0;
      width: 100%;
    }

    .header h1 {
      left: 2.5%;
    }

    .bottom-bar {
      width: 95%;
      left: 2.5%;
    }

    .form-group {
      width: 100%;
    }

    .radio-options {
      flex-direction: column;
      gap: 16px;
    }

    .radio-option {
      width: 100%;
    }
  }

  @media (max-width: 480px) {
    body {
      padding: 8px;
    }

    .form-content,
    .contracts-section,
    .bottom-bar {
      width: 100%;
      left: 0;
      right: 0;
      padding: 12px;
    }

    .header h1 {
      left: 0;
      text-align: center;
      width: 100%;
    }

    .client-name,
    .client-code,
    .contract-label,
    .contract-number {
      font-size: 16px;
    }

    .contracts-title {
      font-size: 20px;
    }

    .footer-action-bar-container {
      padding: 12px;
    }

    .footer-btn {
      padding: 8px 12px;
      font-size: 14px;
    }
  }

  @media (max-width: 1024px) {
    .bottom-bar-title {
      left: 100px;
      bottom: 0;
      font-size: 1.2rem;
    }
    .bottom-bar-close {
      right: 100px;
    }
    .svg {
      right: 200px;
      top: 10px;
    }
  }
  
  @media (max-width: 768px) {
    .top-navbar {
      flex-direction: column;
      align-items: flex-start;
      gap: 1rem;
    }
  
    .actions {
      width: 100%;
      justify-content: space-between;
    }
  
    .bottom-bar {
      flex-direction: column;
      gap: 1rem;
      text-align: center;
    }
  
    .bottom-bar-title,
    .bottom-bar-close {
      position: static;
      text-align: center;
    }
  
    .svg {
      position: static;
      margin-top: 10px;
    }
  }
  
  @media (max-width: 480px) {
    .profile-name {
      font-size: 0.75rem;
    }
  
    .bottom-bar-title {
      font-size: 1rem;
    }
  
    .notification-icon,
    .notification-badge {
      transform: scale(0.85);
    }
  
    .h-8 {
      position: static;
      width: 180px;
      height: auto;
    }
  }

/* Sinistre Information Section */
.sinistre-info-section {
  margin-bottom: 30px;
}

.sinistre-info-card {
  background: linear-gradient(135deg, #e8f5e8, #f0f8ff);
  border: 2px solid #28a745;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.1);
}

.sinistre-info-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 15px;
  border-bottom: 1px solid #28a745;
}

.sinistre-info-header h3 {
  color: #28a745;
  font-family: 'Gilmer Bold', sans-serif;
  font-size: 18px;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.sinistre-numero {
  background: #28a745;
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
}

.sinistre-info-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
}

.sinistre-detail {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  border: 1px solid rgba(40, 167, 69, 0.2);
}

.sinistre-detail .label {
  font-weight: 600;
  color: #1C3F93;
  font-size: 14px;
}

.sinistre-detail .value {
  font-weight: 500;
  color: #333;
  font-size: 14px;
  text-align: right;
}

/* Responsive design for sinistre info */
@media (max-width: 768px) {
  .sinistre-info-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .sinistre-info-details {
    grid-template-columns: 1fr;
  }

  .sinistre-detail {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }

  .sinistre-detail .value {
    text-align: left;
  }
}

/* Enhanced Popup Styling */
.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.popup-container {
  background: #ffffff;
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  animation: slideUp 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.popup-header {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 25px 30px 20px;
  border-bottom: 1px solid #e9ecef;
}

.popup-icon {
  font-size: 32px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.popup-icon.warning {
  background: linear-gradient(45deg, #ffc107, #ff9800);
  color: white;
  box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
}

.popup-icon.info {
  background: linear-gradient(45deg, #17a2b8, #007bff);
  color: white;
  box-shadow: 0 4px 15px rgba(23, 162, 184, 0.3);
}

.popup-icon.error {
  background: linear-gradient(45deg, #dc3545, #c82333);
  color: white;
  box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
}

.popup-header h3 {
  font-family: 'Gilmer Bold', sans-serif;
  font-size: 22px;
  font-weight: 700;
  color: #0A1633;
  margin: 0;
}

.popup-content {
  padding: 25px 30px;
}

.popup-message {
  font-family: 'Gilmer Bold', sans-serif;
  font-size: 16px;
  line-height: 1.6;
  color: #495057;
  margin-bottom: 20px;
}

.popup-actions {
  display: flex;
  gap: 15px;
  padding: 20px 30px 30px;
  justify-content: flex-end;
}

.popup-btn {
  font-family: 'Gilmer Bold', sans-serif;
  font-size: 16px;
  font-weight: 600;
  padding: 12px 24px;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
  min-width: 120px;
  justify-content: center;
}

.cancel-btn {
  background: #f8f9fa;
  color: #6c757d;
  border: 2px solid #dee2e6;
}

.cancel-btn:hover {
  background: #e9ecef;
  border-color: #adb5bd;
  transform: translateY(-1px);
}

.continue-btn {
  background: linear-gradient(45deg, #1A3E8D, #2563eb);
  color: white;
  box-shadow: 0 4px 15px rgba(26, 62, 141, 0.3);
}

.continue-btn:hover {
  background: linear-gradient(45deg, #15307A, #1d4ed8);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(26, 62, 141, 0.4);
}

.popup-btn .btn-icon {
  font-size: 16px;
}

/* Guarantee Selection Popup Styling */
.guarantee-selection-popup {
  max-width: 600px;
  max-height: 80vh;
}

.garanties-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin: 20px 0;
  max-height: 300px;
  overflow-y: auto;
}

.garantie-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #ffffff;
}

.garantie-item:hover {
  border-color: #1A3E8D;
  background: #f8f9ff;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(26, 62, 141, 0.1);
}

.garantie-item.selected {
  border-color: #009B79;
  background: linear-gradient(135deg, #f0fdf9 0%, #ecfdf5 100%);
  box-shadow: 0 4px 15px rgba(0, 155, 121, 0.2);
}

.garantie-checkbox {
  width: 24px;
  height: 24px;
  border: 2px solid #dee2e6;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #ffffff;
  transition: all 0.3s ease;
}

.garantie-item.selected .garantie-checkbox {
  background: #009B79;
  border-color: #009B79;
}

.check-icon {
  color: white;
  font-weight: bold;
  font-size: 14px;
}

.garantie-info {
  flex: 1;
}

.garantie-name {
  font-family: 'Gilmer Bold', sans-serif;
  font-size: 16px;
  font-weight: 600;
  color: #0A1633;
  margin: 0 0 5px 0;
}

.garantie-description {
  font-family: 'Gilmer Bold', sans-serif;
  font-size: 14px;
  color: #6c757d;
  margin: 0;
}

.selection-summary {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 1px solid #dee2e6;
  border-radius: 12px;
  padding: 15px;
  margin-top: 20px;
}

.selection-summary p {
  font-family: 'Gilmer Bold', sans-serif;
  font-size: 14px;
  color: #495057;
  margin: 0 0 10px 0;
}

.selection-summary ul {
  margin: 0;
  padding-left: 20px;
}

.selection-summary li {
  font-family: 'Gilmer Bold', sans-serif;
  font-size: 13px;
  color: #009B79;
  margin-bottom: 5px;
}
  