import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';

interface Sinistre {
  id: number;
  numeroSinistre: string;
  nomEvenement: string;
  dateCreation: string;
  statut: string;
}

@Component({
  selector: 'app-list-sinistre',
  templateUrl: './list-sinistre.component.html',
  styleUrls: ['./list-sinistre.component.css'],
  imports: [
    CommonModule,
    FormsModule,
    RouterModule,
  ],
  standalone: true
})
export class ListSinistreComponent implements OnInit {
  sinistres: Sinistre[] = [];
  filteredSinistres: Sinistre[] = [];
  searchTerm: string = '';
  isLoading: boolean = false;
  errorMessage: string = '';

  constructor() {}

  ngOnInit(): void {
    this.loadSinistres();
  }

  loadSinistres(): void {
    this.isLoading = true;
    this.errorMessage = '';

    // Simulation de données - à remplacer par un service backend
    setTimeout(() => {
      this.sinistres = [
        {
          id: 1,
          numeroSinistre: 'SIN-2024-001',
          nomEvenement: 'Accident de circulation',
          dateCreation: '2024-01-15',
          statut: 'En cours'
        },
        {
          id: 2,
          numeroSinistre: 'SIN-2024-002',
          nomEvenement: 'Incendie véhicule',
          dateCreation: '2024-01-16',
          statut: 'Traité'
        },
        {
          id: 3,
          numeroSinistre: 'SIN-2024-003',
          nomEvenement: 'Vol de véhicule',
          dateCreation: '2024-01-17',
          statut: 'En attente'
        },
        {
          id: 4,
          numeroSinistre: 'SIN-2024-004',
          nomEvenement: 'Dégâts matériels',
          dateCreation: '2024-01-18',
          statut: 'En cours'
        },
        {
          id: 5,
          numeroSinistre: 'SIN-2024-005',
          nomEvenement: 'Accident corporel',
          dateCreation: '2024-01-19',
          statut: 'Traité'
        }
      ];

      this.filteredSinistres = [...this.sinistres];
      this.isLoading = false;
    }, 1000);
  }

  onSearch(): void {
    if (!this.searchTerm.trim()) {
      this.filteredSinistres = [...this.sinistres];
      return;
    }

    const searchTermLower = this.searchTerm.toLowerCase().trim();
    this.filteredSinistres = this.sinistres.filter(sinistre =>
      sinistre.numeroSinistre.toLowerCase().includes(searchTermLower) ||
      sinistre.nomEvenement.toLowerCase().includes(searchTermLower) ||
      sinistre.statut.toLowerCase().includes(searchTermLower)
    );
  }

  clearSearch(): void {
    this.searchTerm = '';
    this.filteredSinistres = [...this.sinistres];
  }

  viewSinistre(sinistre: Sinistre): void {
    console.log('Viewing sinistre:', sinistre);
    // TODO: Navigate to sinistre details
  }

  editSinistre(sinistre: Sinistre): void {
    console.log('Editing sinistre:', sinistre);
    // TODO: Navigate to edit sinistre
  }

  deleteSinistre(sinistre: Sinistre): void {
    if (confirm(`Êtes-vous sûr de vouloir supprimer le sinistre ${sinistre.numeroSinistre} ?`)) {
      console.log('Deleting sinistre:', sinistre);
      // TODO: Call backend service to delete
      this.sinistres = this.sinistres.filter(s => s.id !== sinistre.id);
      this.onSearch(); // Refresh filtered list
    }
  }
}
