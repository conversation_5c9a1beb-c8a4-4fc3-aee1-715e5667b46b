import { Routes } from '@angular/router';
import { Simple3DViewerComponent } from './3d-viewer.component';
import { ConducteurComponent } from './agent/declaration-sinistre/conducteur/conducteur.component';
import { DeclarationSinistreComponent } from './agent/declaration-sinistre/declaration-sinistre.component';
import { DocumentsComponent } from './agent/declaration-sinistre/documents/documents.component';
import { RecapitulatifComponent } from './agent/declaration-sinistre/recapitulatif/recapitulatif.component';
import { RechercheContratComponent } from './agent/declaration-sinistre/recherche-contrat/recherche-contrat.component';
import { SurvenanceSinistreComponent } from './agent/declaration-sinistre/survenance-sinistre/survenance-sinistre.component';
import { TiersComponent } from './agent/declaration-sinistre/tiers/tiers.component';
import { LoginComponent } from './auth/login/login.component';
import { BackofficeComponent } from './backoffice/backoffice/backoffice.component';
import { LoaderComponent } from './loader/loader.component';

export const routes: Routes = [
  { path: '', component: LoaderComponent },
  { path: 'login', component: LoginComponent },
  { path: 'sinistre', component: DeclarationSinistreComponent },
  { path: 'backoffice', component: BackofficeComponent },
  { path: 'recherche-contrat', component: RechercheContratComponent },
  { path: 'survenance-sinistre', component: SurvenanceSinistreComponent },
  { path: 'conducteur', component: ConducteurComponent },
  { path: 'tiers', component: TiersComponent },
  { path: 'documents', component: DocumentsComponent },
  { path: 'recap', component: RecapitulatifComponent },
  {
    path: '3d-model',
    component: Simple3DViewerComponent
  },

  { path: '**', redirectTo: 'login' }
];
