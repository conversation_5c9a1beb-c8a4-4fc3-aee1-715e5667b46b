package com.fed.platform.sinister.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

public class OuvertureSinistreDTO {

    // Basic sinistre information
    @NotBlank(message = "Evenement is required")
    @JsonProperty("evenement")
    private String evenement;

    @NotBlank(message = "Type de degat is required")
    @JsonProperty("degatType")
    private String typeDeDegat;

    @Size(max = 256, message = "Description must be less than 256 characters")
    @JsonProperty("description")
    private String descriptionDeDegat;

    // Tier information
    @JsonProperty("tiersExiste")
    private Boolean tier;

    @JsonProperty("tierType")
    private String typeDuTier;

    @JsonProperty("compagnieTier")
    private String compagnieTier;

    @JsonProperty("casBareme")
    private String casDeBareme;

    @JsonProperty("nombreDeTier")
    private Integer nombreDeTier;

    // Damage and responsibility
    @JsonProperty("degatEstimatif")
    private String degatEstimatif;

    @JsonProperty("responsabilite")
    private Integer responsabilite;

    // 3D and damage information
    @JsonProperty("linkVehicule")
    private String linkVehicule;

    @JsonProperty("selectedDamagePoints")
    private List<DamagePointDTO> selectedDamagePoints;

    // File information (we'll handle files separately in multipart)
    @JsonProperty("hasVehiclePhotos")
    private Boolean hasVehiclePhotos = false;

    @JsonProperty("hasAttachments")
    private Boolean hasAttachments = false;

    // File names for database storage
    @JsonProperty("vehiclePhotoNames")
    private List<String> vehiclePhotoNames;

    @JsonProperty("documentNames")
    private List<String> documentNames;

    // Additional metadata
    @JsonProperty("lieu")
    private String lieu;

    @JsonProperty("date")
    private String date;

    @JsonProperty("heure")
    private String heure;

    // Constructors
    public OuvertureSinistreDTO() {
    }

    // Getters and Setters
    public String getEvenement() {
        return evenement;
    }

    public void setEvenement(String evenement) {
        this.evenement = evenement;
    }

    public String getTypeDeDegat() {
        return typeDeDegat;
    }

    public void setTypeDeDegat(String typeDeDegat) {
        this.typeDeDegat = typeDeDegat;
    }

    public String getDescriptionDeDegat() {
        return descriptionDeDegat;
    }

    public void setDescriptionDeDegat(String descriptionDeDegat) {
        this.descriptionDeDegat = descriptionDeDegat;
    }

    public Boolean getTier() {
        return tier;
    }

    public void setTier(Boolean tier) {
        this.tier = tier;
    }

    public String getTypeDuTier() {
        return typeDuTier;
    }

    public void setTypeDuTier(String typeDuTier) {
        this.typeDuTier = typeDuTier;
    }

    public String getCompagnieTier() {
        return compagnieTier;
    }

    public void setCompagnieTier(String compagnieTier) {
        this.compagnieTier = compagnieTier;
    }

    public String getCasDeBareme() {
        return casDeBareme;
    }

    public void setCasDeBareme(String casDeBareme) {
        this.casDeBareme = casDeBareme;
    }

    public Integer getNombreDeTier() {
        return nombreDeTier;
    }

    public void setNombreDeTier(Integer nombreDeTier) {
        this.nombreDeTier = nombreDeTier;
    }

    public String getDegatEstimatif() {
        return degatEstimatif;
    }

    public void setDegatEstimatif(String degatEstimatif) {
        this.degatEstimatif = degatEstimatif;
    }

    public Integer getResponsabilite() {
        return responsabilite;
    }

    public void setResponsabilite(Integer responsabilite) {
        this.responsabilite = responsabilite;
    }

    public String getLinkVehicule() {
        return linkVehicule;
    }

    public void setLinkVehicule(String linkVehicule) {
        this.linkVehicule = linkVehicule;
    }

    public List<DamagePointDTO> getSelectedDamagePoints() {
        return selectedDamagePoints;
    }

    public void setSelectedDamagePoints(List<DamagePointDTO> selectedDamagePoints) {
        this.selectedDamagePoints = selectedDamagePoints;
    }

    public Boolean getHasVehiclePhotos() {
        return hasVehiclePhotos;
    }

    public void setHasVehiclePhotos(Boolean hasVehiclePhotos) {
        this.hasVehiclePhotos = hasVehiclePhotos;
    }

    public Boolean getHasAttachments() {
        return hasAttachments;
    }

    public void setHasAttachments(Boolean hasAttachments) {
        this.hasAttachments = hasAttachments;
    }

    public String getLieu() {
        return lieu;
    }

    public void setLieu(String lieu) {
        this.lieu = lieu;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public String getHeure() {
        return heure;
    }

    public void setHeure(String heure) {
        this.heure = heure;
    }

    public List<String> getVehiclePhotoNames() {
        return vehiclePhotoNames;
    }

    public void setVehiclePhotoNames(List<String> vehiclePhotoNames) {
        this.vehiclePhotoNames = vehiclePhotoNames;
    }

    public List<String> getDocumentNames() {
        return documentNames;
    }

    public void setDocumentNames(List<String> documentNames) {
        this.documentNames = documentNames;
    }

    @Override
    public String toString() {
        return "OuvertureSinistreDTO{" +
                "evenement='" + evenement + '\'' +
                ", typeDeDegat='" + typeDeDegat + '\'' +
                ", tier=" + tier +
                ", responsabilite=" + responsabilite +
                ", hasVehiclePhotos=" + hasVehiclePhotos +
                ", hasAttachments=" + hasAttachments +
                '}';
    }

    // Inner class for damage points
    public static class DamagePointDTO {
        private Double x;
        private Double y;
        private Double z;
        private String type;
        private String part;
        private String severity;

        // Constructors
        public DamagePointDTO() {
        }

        // Getters and Setters
        public Double getX() {
            return x;
        }

        public void setX(Double x) {
            this.x = x;
        }

        public Double getY() {
            return y;
        }

        public void setY(Double y) {
            this.y = y;
        }

        public Double getZ() {
            return z;
        }

        public void setZ(Double z) {
            this.z = z;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public String getPart() {
            return part;
        }

        public void setPart(String part) {
            this.part = part;
        }

        public String getSeverity() {
            return severity;
        }

        public void setSeverity(String severity) {
            this.severity = severity;
        }

        @Override
        public String toString() {
            return "DamagePointDTO{" +
                    "x=" + x + ", y=" + y + ", z=" + z +
                    ", type='" + type + '\'' +
                    ", part='" + part + '\'' +
                    ", severity='" + severity + '\'' +
                    '}';
        }
    }
}
