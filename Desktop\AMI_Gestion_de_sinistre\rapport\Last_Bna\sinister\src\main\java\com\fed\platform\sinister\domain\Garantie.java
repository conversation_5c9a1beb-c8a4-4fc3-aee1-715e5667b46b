package com.fed.platform.sinister.domain;

import java.io.Serializable;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;

@Entity
@Table(name = "garantie")
public class Garantie implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Size(max = 5)
    @Column(name = "code", length = 5)
    private String code;

    @Size(max = 100)
    @Column(name = "libelle", length = 100)
    private String libelle;

    @Column(name = "plafond")
    private Double plafond;

    @Column(name = "franchise")
    private Double franchise;

    @Column(name = "plafond_couvert")
    private String plafondCouvert;

    // Relationship with Contrat
    @ManyToOne
    @JoinColumn(name = "contrat_id")
    private Contrat contrat;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getLibelle() {
        return libelle;
    }

    public void setLibelle(String libelle) {
        this.libelle = libelle;
    }

    public Double getPlafond() {
        return plafond;
    }

    public void setPlafond(Double plafond) {
        this.plafond = plafond;
    }

    public Double getFranchise() {
        return franchise;
    }

    public void setFranchise(Double franchise) {
        this.franchise = franchise;
    }

    public String getPlafondCouvert() {
        return plafondCouvert;
    }

    public void setPlafondCouvert(String plafondCouvert) {
        this.plafondCouvert = plafondCouvert;
    }

    public Contrat getContrat() {
        return contrat;
    }

    public void setContrat(Contrat contrat) {
        this.contrat = contrat;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof Garantie)) {
            return false;
        }
        return id != null && id.equals(((Garantie) o).id);
    }

    @Override
    public int hashCode() {
        return 31;
    }

    @Override
    public String toString() {
        return "Garantie{" +
                "id=" + getId() +
                ", code='" + getCode() + "'" +
                ", libelle='" + getLibelle() + "'" +
                ", plafond=" + getPlafond() +
                ", franchise=" + getFranchise() +
                ", plafondCouvert='" + getPlafondCouvert() + "'" +
                "}";
    }
}
