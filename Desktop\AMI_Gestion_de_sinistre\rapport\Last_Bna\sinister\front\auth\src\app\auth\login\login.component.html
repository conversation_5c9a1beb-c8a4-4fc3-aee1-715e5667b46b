<!-- login.component.html -->
<div class="login-container">
  <!-- Left side: Image -->
  <div class="image-section">
    <div class="logo-wrapper">
      <img src="assets/newlogo.png" alt="Company Logo" class="logo-image" />
    </div>
  </div>

  <div class="logo-wrapper2">
    <div #logoCanvas style="width: 300px; height: 300px;"></div>
  </div>
  <!-- Right side: Login Form -->
  <div class="form-section">
    <div class="form-wrapper">
      <h2 class="form-title">Bienvenue à nouveau</h2>
      <p class="form-subtitle">Veuillez saisir vos identifiants pour vous connecter</p>

      <form [formGroup]="loginForm" (ngSubmit)="onSubmit()" class="login-form">
        <div *ngFor="let field of formConfig.fields; trackBy: trackByName" class="form-group">
          <label class="form-label">{{ field.label }}</label>
          <input
            class="form-input"
            [formControlName]="field.name"
            [type]="field.type || 'text'"
            [placeholder]="field.placeholder"
          />
          <div *ngIf="loginForm.get(field.name)?.invalid && loginForm.get(field.name)?.touched" class="error-message">
            <p style="color: red;" *ngIf="loginError">Invalid identifiants</p>
          </div>
        </div>

        <div class="form-actions">
          <button type="submit" class="submit-button" [disabled]="loginForm.invalid">
            Login
            <span class="button-icon">→</span>
          </button>
        </div>
      </form>
    </div>
  </div>
</div>
