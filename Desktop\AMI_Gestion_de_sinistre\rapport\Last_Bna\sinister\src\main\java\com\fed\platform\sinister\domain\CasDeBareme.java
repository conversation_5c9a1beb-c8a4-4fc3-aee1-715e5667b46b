package com.fed.platform.sinister.domain;

import jakarta.persistence.*;
import java.io.Serializable;

@Entity
@Table(name = "cas_de_bareme")
public class CasDeBareme implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "libelle", nullable = false, columnDefinition = "TEXT")
    private String libelle;

    @Column(name = "res_x", nullable = false)
    private Integer resX;

    @Column(name = "res_y", nullable = false)
    private Integer resY;

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getLibelle() {
        return libelle;
    }

    public void setLibelle(String libelle) {
        this.libelle = libelle;
    }

    public Integer getResX() {
        return resX;
    }

    public void setResX(Integer resX) {
        this.resX = resX;
    }

    public Integer getResY() {
        return resY;
    }

    public void setResY(Integer resY) {
        this.resY = resY;
    }
}