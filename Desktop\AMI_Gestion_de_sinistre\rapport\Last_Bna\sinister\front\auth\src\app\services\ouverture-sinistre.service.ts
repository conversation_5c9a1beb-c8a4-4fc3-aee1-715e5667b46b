import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';

export interface OuvertureSinistreData {
  evenement: string;
  degatType: string;
  description?: string;
  tiersExiste: boolean;
  tierType?: string;
  compagnieTier?: string;
  casBareme?: string;
  nombreDeTier?: number;
  degatEstimatif?: string;
  responsabilite?: number;
  linkVehicule?: string;
  selectedDamagePoints?: DamagePoint[];
  lieu?: string;
  date?: string;
  heure?: string;
  dateCreation?: string;
}

export interface DamagePoint {
  x: number;
  y: number;
  z: number;
  type: string;
  part: string;
  severity: string;
}

export interface OuvertureSinistreResponse {
  success: boolean;
  message: string;
  numeroSinistre?: string;
  id?: number;
  data?: any;
}

@Injectable({
  providedIn: 'root'
})
export class OuvertureSinistreService {
  private apiUrl = 'http://localhost:8080/api/ouverture-sinistre';

  constructor(private http: HttpClient) {}

  /**
   * Submit ouverture sinistre with form data and files
   */
  submitOuvertureSinistre(
    formData: OuvertureSinistreData,
    vehiclePhotos?: File[],
    attachments?: File[]
  ): Observable<OuvertureSinistreResponse> {
    console.log('🔍 Submitting ouverture sinistre:', formData);
    console.log('📸 Vehicle photos:', vehiclePhotos?.length || 0);
    console.log('📎 Attachments:', attachments?.length || 0);

    // Create FormData for multipart upload
    const multipartData = new FormData();

    // Add form data as JSON string
    multipartData.append('formData', JSON.stringify(formData));

    // Add vehicle photos
    if (vehiclePhotos && vehiclePhotos.length > 0) {
      vehiclePhotos.forEach((photo, index) => {
        multipartData.append('vehiclePhotos', photo, photo.name);
      });
    }

    // Add attachments
    if (attachments && attachments.length > 0) {
      attachments.forEach((attachment, index) => {
        multipartData.append('attachments', attachment, attachment.name);
      });
    }

    return this.http.post<OuvertureSinistreResponse>(this.apiUrl, multipartData).pipe(
      tap(response => {
        console.log('✅ Ouverture sinistre submitted successfully:', response);
        if (response.success) {
          console.log('📄 Generated numero sinistre:', response.numeroSinistre);
        }
      })
    );
  }

  /**
   * Submit ouverture sinistre with JSON data only (for testing)
   */
  submitOuvertureSinistreJson(formData: OuvertureSinistreData, vehiclePhotos?: File[], documents?: File[]): Observable<OuvertureSinistreResponse> {
    console.log('🔍 Submitting ouverture sinistre (JSON only):', formData);
    console.log('📸 Vehicle photos:', vehiclePhotos?.length || 0);
    console.log('📄 Documents:', documents?.length || 0);

    // Add file names to the form data
    const enhancedFormData = {
      ...formData,
      vehiclePhotoNames: vehiclePhotos?.map(photo => photo.name) || [],
      documentNames: documents?.map(doc => doc.name) || []
    };

    console.log('📋 Enhanced form data:', enhancedFormData);

    const headers = new HttpHeaders({
      'Content-Type': 'application/json'
    });

    return this.http.post<OuvertureSinistreResponse>(`${this.apiUrl}/json`, enhancedFormData, { headers }).pipe(
      tap(response => {
        console.log('✅ Ouverture sinistre submitted successfully (JSON):', response);
        if (response.success) {
          console.log('📄 Generated numero sinistre:', response.numeroSinistre);
        }
      })
    );
  }

  /**
   * Save 3D session data (photos + session link)
   */
  save3DSession(sessionId: string, vehiclePhotos?: File[]): Observable<any> {
    console.log('🔍 Saving 3D session:', sessionId);
    console.log('📸 Vehicle photos:', vehiclePhotos?.length || 0);

    const multipartData = new FormData();
    multipartData.append('sessionId', sessionId);

    // Add vehicle photos
    if (vehiclePhotos && vehiclePhotos.length > 0) {
      vehiclePhotos.forEach((photo) => {
        multipartData.append('vehiclePhotos', photo, photo.name);
      });
    }

    return this.http.post<any>(`${this.apiUrl}/3d-session`, multipartData).pipe(
      tap(response => {
        console.log('✅ 3D session saved successfully:', response);
      })
    );
  }

  /**
   * Auto-save form data when user starts filling
   */
  autoSaveFormData(formData: any): void {
    console.log('💾 Auto-saving form data...');

    // Add current date
    const dataWithDate = {
      ...formData,
      dateCreation: new Date().toISOString()
    };

    // Store in localStorage for persistence
    localStorage.setItem('sinistre_form_autosave', JSON.stringify(dataWithDate));
    console.log('✅ Form data auto-saved to localStorage');
  }

  /**
   * Load auto-saved form data
   */
  loadAutoSavedFormData(): any | null {
    console.log('📖 Loading auto-saved form data...');

    const savedData = localStorage.getItem('sinistre_form_autosave');
    if (savedData) {
      try {
        const parsed = JSON.parse(savedData);
        console.log('✅ Auto-saved form data loaded:', parsed);
        return parsed;
      } catch (error) {
        console.error('❌ Error parsing auto-saved data:', error);
        return null;
      }
    }

    console.log('❌ No auto-saved form data found');
    return null;
  }

  /**
   * Clear auto-saved form data
   */
  clearAutoSavedFormData(): void {
    console.log('🧹 Clearing auto-saved form data');
    localStorage.removeItem('sinistre_form_autosave');
  }

  /**
   * Get all ouverture sinistres
   */
  getAllOuvertureSinistres(): Observable<any[]> {
    console.log('🔍 Getting all ouverture sinistres');
    return this.http.get<any[]>(this.apiUrl).pipe(
      tap(response => console.log('📄 Found ouverture sinistres:', response.length))
    );
  }

  /**
   * Get ouverture sinistre by ID
   */
  getOuvertureSinistreById(id: number): Observable<any> {
    console.log('🔍 Getting ouverture sinistre by ID:', id);
    return this.http.get<any>(`${this.apiUrl}/${id}`).pipe(
      tap(response => console.log('✅ Found ouverture sinistre:', response))
    );
  }

  /**
   * Get ouverture sinistre by numero
   */
  getOuvertureSinistreByNumero(numeroSinistre: string): Observable<any> {
    console.log('🔍 Getting ouverture sinistre by numero:', numeroSinistre);
    return this.http.get<any>(`${this.apiUrl}/numero/${numeroSinistre}`).pipe(
      tap(response => console.log('✅ Found ouverture sinistre:', response))
    );
  }

  /**
   * Update ouverture sinistre
   */
  updateOuvertureSinistre(id: number, formData: OuvertureSinistreData): Observable<OuvertureSinistreResponse> {
    console.log('🔍 Updating ouverture sinistre:', id, formData);

    const headers = new HttpHeaders({
      'Content-Type': 'application/json'
    });

    return this.http.put<OuvertureSinistreResponse>(`${this.apiUrl}/${id}`, formData, { headers }).pipe(
      tap(response => console.log('✅ Ouverture sinistre updated:', response))
    );
  }

  /**
   * Delete ouverture sinistre
   */
  deleteOuvertureSinistre(id: number): Observable<OuvertureSinistreResponse> {
    console.log('🔍 Deleting ouverture sinistre:', id);
    return this.http.delete<OuvertureSinistreResponse>(`${this.apiUrl}/${id}`).pipe(
      tap(response => console.log('✅ Ouverture sinistre deleted:', response))
    );
  }

  /**
   * Store data in session storage for next component
   */
  storeFormDataInSession(formData: OuvertureSinistreData, numeroSinistre?: string): void {
    console.log('💾 Storing form data in session storage');
    
    const sessionData = {
      formData: formData,
      numeroSinistre: numeroSinistre,
      timestamp: new Date().toISOString()
    };

    sessionStorage.setItem('ouverture_sinistre_data', JSON.stringify(sessionData));
    console.log('✅ Form data stored in session storage');
  }

  /**
   * Get data from session storage
   */
  getFormDataFromSession(): { formData: OuvertureSinistreData; numeroSinistre?: string; timestamp: string } | null {
    console.log('📖 Reading form data from session storage');
    
    const sessionData = sessionStorage.getItem('ouverture_sinistre_data');
    if (sessionData) {
      try {
        const parsed = JSON.parse(sessionData);
        console.log('✅ Form data retrieved from session storage:', parsed);
        return parsed;
      } catch (error) {
        console.error('❌ Error parsing session data:', error);
        return null;
      }
    }
    
    console.log('❌ No form data found in session storage');
    return null;
  }

  /**
   * Clear session storage
   */
  clearFormDataFromSession(): void {
    console.log('🧹 Clearing form data from session storage');
    sessionStorage.removeItem('ouverture_sinistre_data');
  }

  /**
   * Convert form data to API format
   */
  convertFormDataToApiFormat(sinistreData: any): OuvertureSinistreData {
    const tiersExiste = sinistreData.tiersExiste === true;

    return {
      evenement: sinistreData.evenement || '',
      degatType: sinistreData.degatType || '',
      description: sinistreData.descriptionDegat || sinistreData.description || '',
      tiersExiste: tiersExiste,
      // Database constraint: when tier=true, these fields must NOT be NULL
      // when tier=false, these fields must be NULL
      tierType: tiersExiste ? (sinistreData.tierType1 || sinistreData.tierType || '') : null,
      compagnieTier: tiersExiste ? (sinistreData.compagnieTier || '') : null,
      casBareme: tiersExiste ? (sinistreData.casBareme || '') : null,
      nombreDeTier: tiersExiste ? (sinistreData.nombreTiers || 0) : null,
      degatEstimatif: sinistreData.degatEstimatif || '',
      responsabilite: sinistreData.responsabilite ? parseInt(sinistreData.responsabilite) : undefined,
      linkVehicule: sinistreData.linkVehicule || '',
      selectedDamagePoints: sinistreData.selectedDamagePoints || [],
      lieu: sinistreData.lieu || '',
      date: sinistreData.date || '',
      heure: sinistreData.heure || ''
    };
  }
}
