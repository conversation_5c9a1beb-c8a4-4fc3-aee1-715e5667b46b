import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatNativeDateModule } from '@angular/material/core';
import { Router } from '@angular/router';

@Component({
  selector: 'app-conducteur',
  standalone: true,
  imports: [
    CommonModule, 
    ReactiveFormsModule,
    MatDatepickerModule,
    MatFormFieldModule,
    MatInputModule,
    MatNativeDateModule
  ],
  templateUrl: './conducteur.component.html',
  styleUrls: ['./conducteur.component.css']
})
export class ConducteurComponent implements OnInit {
  conducteurForm!: FormGroup;

  constructor(private fb: FormBuilder, private router: Router) {
    this.initializeForm();
  }

  ngOnInit() {}

  private initializeForm() {
    this.conducteurForm = this.fb.group({
      nomConducteur: ['', Validators.required],
      categoriePermis: ['', Validators.required],
      originePermis: ['', Validators.required],
      numeroPermis: ['', Validators.required],
      dateObtention: ['', Validators.required]
    });
  }

  onSubmit() {
    if (this.conducteurForm.valid) {
      console.log(this.conducteurForm.value);
    } else {
      this.markFormGroupTouched(this.conducteurForm);
    }
  }

  private markFormGroupTouched(formGroup: FormGroup) {
    Object.values(formGroup.controls).forEach(control => {
      control.markAsTouched();

      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      }
    });
  }

  onRetour(): void {
    this.router.navigate(['/agent/declaration-sinistre/survenance']);
  }

  onContinuer(): void {
    if (this.conducteurForm.valid) {
      // Save form data and navigate to next step
      this.router.navigate(['/agent/declaration-sinistre/tiers']);
    }
  }

  // Getter methods for form controls
  get nomConducteur() { return this.conducteurForm.get('nomConducteur'); }
  get categoriePermis() { return this.conducteurForm.get('categoriePermis'); }
  get originePermis() { return this.conducteurForm.get('originePermis'); }
  get numeroPermis() { return this.conducteurForm.get('numeroPermis'); }
  get dateObtention() { return this.conducteurForm.get('dateObtention'); }
}
