import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { Router } from '@angular/router';

// Interfaces for data structure
interface Contrat {
  id: number;
  numContrat: string;
  immatriculation: string;
  dateEffet: string;
  dateFin: string;
  produit: string;
  nom: string;
  clientName: string;
  clientCode: string;
}

interface Garantie {
  id: number;
  contratId: number;
  nom: string;
  description: string;
  montant: number;
}

interface ContratSelectionData {
  contrat: Contrat;
  garanties: Garantie[];
  numeroSinistre: string;
  sinistreData: any;
  dateOfOccurrence: string;
  personType: string;
  searchType: string;
  selectionTimestamp: string;
  currentStep: string;
  completedSteps: string[];
}

@Component({
  selector: 'app-conducteur',
  standalone: true,
  imports: [
    CommonModule, 
    ReactiveFormsModule,
    MatDatepickerModule,
    MatFormFieldModule,
    MatInputModule,
    MatNativeDateModule
  ],
  templateUrl: './conducteur.component.html',
  styleUrls: ['./conducteur.component.css']
})
export class ConducteurComponent implements OnInit {
  conducteurForm!: FormGroup;
  contratSelectionData: ContratSelectionData | null = null;

  constructor(private fb: FormBuilder, private router: Router) {
    this.initializeForm();
  }

  ngOnInit() {
    this.loadContratSelectionData();
  }

  private loadContratSelectionData(): void {
    console.log('🔍 Loading contract selection data from session storage...');

    const sessionData = sessionStorage.getItem('contratSelection');
    if (sessionData) {
      try {
        this.contratSelectionData = JSON.parse(sessionData);
        console.log('✅ Loaded contract selection data:', this.contratSelectionData);

        // Display summary information
        if (this.contratSelectionData) {
          console.log('📄 Contract:', this.contratSelectionData.contrat?.numContrat);
          console.log('🚗 Immatriculation:', this.contratSelectionData.contrat?.immatriculation);
          console.log('🛡️ Selected guarantees:', this.contratSelectionData.garanties?.map(g => g.nom));
          console.log('🔢 Numero sinistre:', this.contratSelectionData.numeroSinistre);
        }
      } catch (error) {
        console.error('❌ Error parsing contract selection data:', error);
        this.contratSelectionData = null;
      }
    } else {
      console.log('⚠️ No contract selection data found in session storage');
      // Optionally redirect back to previous step
      // this.router.navigate(['/recherche-contrat']);
    }
  }

  private initializeForm() {
    this.conducteurForm = this.fb.group({
      nomConducteur: ['', Validators.required],
      categoriePermis: ['', Validators.required],
      originePermis: ['', Validators.required],
      numeroPermis: ['', Validators.required],
      dateObtention: ['', Validators.required]
    });
  }

  onSubmit() {
    if (this.conducteurForm.valid) {
      console.log('✅ Conducteur form is valid');
      console.log('📋 Form data:', this.conducteurForm.value);

      // Save conducteur data and continue to next step
      this.saveConducteurDataAndContinue();
    } else {
      console.log('❌ Conducteur form is invalid');
      this.markFormGroupTouched(this.conducteurForm);
    }
  }

  private saveConducteurDataAndContinue(): void {
    console.log('💾 Saving conducteur data...');

    const conducteurData = this.conducteurForm.value;

    // Update the session data with conducteur information
    if (this.contratSelectionData) {
      const updatedData = {
        ...this.contratSelectionData,
        conducteurData: conducteurData,
        currentStep: 'tiers',
        completedSteps: [...this.contratSelectionData.completedSteps, 'conducteur']
      };

      // Save updated data to session storage
      sessionStorage.setItem('contratSelection', JSON.stringify(updatedData));
      console.log('✅ Updated session data with conducteur info');

      // Navigate to next step
      this.router.navigate(['/tiers']);
    } else {
      console.error('❌ No contract selection data available');
      alert('Données du contrat manquantes. Veuillez recommencer le processus.');
      this.router.navigate(['/recherche-contrat']);
    }
  }

  private markFormGroupTouched(formGroup: FormGroup) {
    Object.values(formGroup.controls).forEach(control => {
      control.markAsTouched();

      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      }
    });
  }

  onRetour(): void {
    console.log('🔙 Returning to previous step...');
    this.router.navigate(['/recherche-contrat']);
  }

  onContinuer(): void {
    console.log('➡️ Continuing to next step...');
    if (this.conducteurForm.valid) {
      this.saveConducteurDataAndContinue();
    } else {
      console.log('❌ Form is invalid, cannot continue');
      this.markFormGroupTouched(this.conducteurForm);
      alert('Veuillez remplir tous les champs obligatoires.');
    }
  }

  // Getter methods for form controls
  get nomConducteur() { return this.conducteurForm.get('nomConducteur'); }
  get categoriePermis() { return this.conducteurForm.get('categoriePermis'); }
  get originePermis() { return this.conducteurForm.get('originePermis'); }
  get numeroPermis() { return this.conducteurForm.get('numeroPermis'); }
  get dateObtention() { return this.conducteurForm.get('dateObtention'); }
}
