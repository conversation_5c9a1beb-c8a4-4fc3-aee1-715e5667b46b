// src/app/backoffice/backoffice/type-de-degat/type-de-degat.component.ts
import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { TypeDegat } from './type-degat.model';
import { TypeDegatService } from './type-degat.service';

@Component({
  selector: 'app-type-de-degat',
  templateUrl: './type-de-degat.component.html',
  styleUrls: ['./type-de-degat.component.css'],
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule]
})
export class TypeDeDegatComponent implements OnInit {
  typeDegats: TypeDegat[] = [];
  showModal = false;
  editingTypeDegat: TypeDegat | null = null;
  typeDegatForm: FormGroup;
  isLoading = false;
  errorMessage: string | null = null;

  constructor(
    private fb: FormBuilder,
    private typeDegatService: TypeDegatService
  ) {
    this.typeDegatForm = this.fb.group({
      code: ['', [Validators.required]],
      libelle: ['', [Validators.required]],
      description: ['']
    });
  }

  ngOnInit(): void {
    this.loadTypeDegats();
  }

  loadTypeDegats(): void {
    this.isLoading = true;
    this.errorMessage = null;
    
    this.typeDegatService.getAll().subscribe({
      next: (types) => {
        this.typeDegats = types;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading damage types:', error);
        this.errorMessage = 'Failed to load damage types. Please check your connection.';
        this.isLoading = false;
      }
    });
  }

  openModal(): void {
    this.showModal = true;
    this.editingTypeDegat = null;
    this.typeDegatForm.reset();
    this.errorMessage = null;
  }

  closeModal(): void {
    this.showModal = false;
    this.editingTypeDegat = null;
    this.typeDegatForm.reset();
    this.errorMessage = null;
  }

  editTypeDegat(type: TypeDegat): void {
    this.editingTypeDegat = type;
    this.typeDegatForm.patchValue({
      code: type.code,
      libelle: type.libelle,
      description: type.description
    });
    this.showModal = true;
  }

  deleteTypeDegat(type: TypeDegat): void {
    if (confirm('Êtes-vous sûr de vouloir supprimer ce type de dégât ?') && type.id) {
      this.isLoading = true;
      this.typeDegatService.delete(type.id).subscribe({
        next: () => {
          this.typeDegats = this.typeDegats.filter(t => t.id !== type.id);
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error deleting damage type', error);
          this.errorMessage = 'Failed to delete damage type. Please try again.';
          this.isLoading = false;
        }
      });
    }
  }

  onSubmit(): void {
    if (this.typeDegatForm.invalid) return;

    const formValue = this.typeDegatForm.value;
    const typeDegatData: Omit<TypeDegat, 'id'> = {
      code: formValue.code,
      libelle: formValue.libelle,
      description: formValue.description
    };

    this.isLoading = true;
    this.errorMessage = null;

    if (this.editingTypeDegat && this.editingTypeDegat.id) {
      this.typeDegatService.update(this.editingTypeDegat.id, { ...this.editingTypeDegat, ...typeDegatData })
        .subscribe({
          next: (updatedType) => {
            const index = this.typeDegats.findIndex(t => t.id === updatedType.id);
            if (index !== -1) {
              this.typeDegats[index] = updatedType;
            }
            this.closeModal();
            this.isLoading = false;
          },
          error: (error) => {
            console.error('Error updating damage type', error);
            this.errorMessage = 'Failed to update damage type. Please try again.';
            this.isLoading = false;
          }
        });
    } else {
      this.typeDegatService.create(typeDegatData).subscribe({
        next: (newType) => {
          this.typeDegats.push(newType);
          this.closeModal();
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error creating damage type', error);
          this.errorMessage = 'Failed to create damage type. Please try again.';
          this.isLoading = false;
        }
      });
    }
  }
}